<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { useAuthStore } from '@/stores/authStore'
import type { LoginFormData, LoginFormRules } from '@/types/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 登录表单数据
const loginForm = reactive<LoginFormData>({
  username: 'zhous',
  password: 'admin',
  verification: '42FF8F', // 默认验证码值
  remember: false,
})

// 表单验证规则
const loginRules: LoginFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 5, max: 20, message: '密码长度在 5 到 20 个字符', trigger: 'blur' },
  ],
  verification: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度为 6 位', trigger: 'blur' },
  ],
}

// 登录加载状态
const loginLoading = ref(false)

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 表单验证
    await loginFormRef.value.validate()

    loginLoading.value = true

    // 构建登录请求数据
    const loginData = {
      username: loginForm.username,
      password: loginForm.password,
      verifcation: loginForm.verification, // 使用用户输入的验证码
      client_time: Date.now(),
    }

    // 调用登录接口
    await authStore.login(loginData)

    ElMessage({
      message: '登录成功',
      type: 'success',
    })

    // 跳转到首页
    await router.push('/')
  } catch (error: any) {
    console.error('登录失败:', error)

    // TODO: 根据后端错误码进行不同的错误处理
    const errorMessage = error?.response?.data?.message || error?.message || '登录失败，请重试'
    ElMessage.error(errorMessage)
  } finally {
    loginLoading.value = false
  }
}
</script>

<template>
  <div class="login-container">
    <div class="login-wrapper">
      <div class="login-header">
        <h1 class="login-title">低空遥感云平台</h1>
        <p class="login-subtitle">welcome back</p>
      </div>

      <div class="login-form-container">
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              prefix-icon="User"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item prop="verification">
            <el-input
              v-model="loginForm.verification"
              placeholder="请输入验证码"
              prefix-icon="Shield"
              clearable
              maxlength="6"
            />
          </el-form-item>

          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.remember"> 记住密码 </el-checkbox>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              class="login-button"
              :loading="loginLoading"
              @click="handleLogin"
            >
              {{ loginLoading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.login-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(
    135deg,
    #0a0e27 0%,
    #16213e 25%,
    #0f1b2e 50%,
    #1a1a2e 75%,
    #16213e 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  // 光晕效果
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 800px;
    height: 800px;
    background: radial-gradient(
      circle,
      rgba(0, 255, 254, 0.08) 0%,
      rgba(0, 200, 255, 0.04) 30%,
      rgba(0, 150, 255, 0.02) 60%,
      transparent 100%
    );
    transform: translate(-50%, -50%);
    animation: pulse-glow 4s ease-in-out infinite alternate;
    pointer-events: none;
  }
}

@keyframes pulse-glow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

.login-wrapper {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;

  .login-title {
    font-size: $font-size-headline;
    color: $text-active;
    margin: 0 0 0.5rem 0;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(0, 255, 254, 0.3);
  }

  .login-subtitle {
    font-size: $font-size-base;
    color: $text-secondary;
    margin: 0;
    font-weight: 300;
  }
}

.login-form-container {
  background: rgba(10, 20, 40, 0.85);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid rgba(0, 255, 254, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(0, 255, 254, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;

  .login-form {
    .el-form-item {
      margin-bottom: 1.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .login-options {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .el-checkbox {
        color: $text-default;
      }
    }

    .login-button {
      width: 100%;
      height: 3rem;
      font-size: $font-size-large;
      font-weight: 500;
      border-radius: 8px;
      background: linear-gradient(135deg, rgba(0, 255, 254, 0.8) 0%, rgba(0, 180, 255, 0.6) 100%);
      border: 1px solid rgba(0, 255, 254, 0.3);
      color: #ffffff;
      box-shadow:
        0 4px 15px rgba(0, 255, 254, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      // 按钮内部光效
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }

      &:hover {
        transform: translateY(-1px);
        background: linear-gradient(135deg, rgba(0, 255, 254, 0.9) 0%, rgba(0, 180, 255, 0.7) 100%);
        border-color: rgba(0, 255, 254, 0.5);
        box-shadow:
          0 6px 20px rgba(0, 255, 254, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(0);
        box-shadow:
          0 2px 8px rgba(0, 255, 254, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }

      &.is-loading {
        transform: none;
        background: linear-gradient(135deg, rgba(0, 255, 254, 0.6) 0%, rgba(0, 180, 255, 0.4) 100%);

        &::before {
          display: none;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-wrapper {
    max-width: 90%;
    padding: 1rem;
  }

  .login-form-container {
    padding: 1.5rem;
  }

  .login-header .login-title {
    font-size: $font-size-title;
  }
}
</style>
