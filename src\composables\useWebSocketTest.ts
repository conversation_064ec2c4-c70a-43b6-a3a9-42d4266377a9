/**
 * WebSocket 测试组合函数
 * 专门用于测试后端WebSocket接口连通性
 */
import { useWebSocket } from './useWebSocket'

export function useWebSocketTest() {
  const webSocket = useWebSocket()

  /**
   * 测试基础WebSocket连接
   */
  const testBasicConnection = () => {
    console.log('🧪 开始测试基础WebSocket连接...')

    // 直接连接基础WebSocket地址
    webSocket.connect()
  }

  /**
   * 测试带端点的WebSocket连接
   * @param endpoint 端点路径
   */
  const testEndpointConnection = (endpoint: string) => {
    console.log('🧪 开始测试WebSocket端点连接:', endpoint)

    // 连接指定端点
    webSocket.connect(endpoint)
  }

  /**
   * 发送获取飞控数据请求
   * @param droneId 无人机ID
   */
  const sendGetFlyDataRequest = (droneId: string = 'test-drone-001') => {
    const request = {
      type: 'get_fly_data',
      droneId: droneId,
      timestamp: Date.now(),
    }

    console.log('🚁 发送获取飞控数据请求:', request)
    webSocket.sendMessage(request)
  }

  /**
   * 发送文本消息（根据你提到的apipost测试）
   */
  const sendTextMessage = (text: string = 'get_fly_data') => {
    console.log('📝 发送文本消息:', text)
    webSocket.sendMessage(text)
  }

  /**
   * 发送测试消息
   */
  const sendTestMessage = () => {
    const testMessage = {
      type: 'test',
      timestamp: Date.now(),
      message: 'Hello from frontend!',
    }

    console.log('🧪 发送测试消息:', testMessage)
    webSocket.sendMessage(testMessage)
  }

  /**
   * 发送心跳消息
   */
  const sendHeartbeat = () => {
    const heartbeat = {
      type: 'ping',
      timestamp: Date.now(),
    }

    console.log('💓 发送心跳消息:', heartbeat)
    webSocket.sendMessage(heartbeat)
  }

  /**
   * 打印连接统计信息
   */
  const printStats = () => {
    const stats = webSocket.getStats()
    console.log('📊 WebSocket连接统计:', stats)
    return stats
  }

  /**
   * 运行完整的连接测试
   */
  const runFullTest = async () => {
    console.log('🚀 开始完整WebSocket测试...')
    console.log('='.repeat(50))

    // 1. 测试基础连接
    testBasicConnection()

    // 等待连接建立
    await new Promise((resolve) => setTimeout(resolve, 2000))

    if (webSocket.isConnected.value) {
      // 2. 发送文本消息 "get_fly_data"（模拟apipost测试）
      console.log('📝 第一步：发送文本消息...')
      sendTextMessage('get_fly_data')

      // 等待响应
      await new Promise((resolve) => setTimeout(resolve, 3000))

      // 3. 发送JSON格式的飞控数据请求
      console.log('🚁 第二步：发送JSON格式飞控数据请求...')
      sendGetFlyDataRequest('test-drone-001')

      // 等待响应
      await new Promise((resolve) => setTimeout(resolve, 3000))

      // 4. 发送测试消息
      console.log('🧪 第三步：发送测试消息...')
      sendTestMessage()

      // 等待响应
      await new Promise((resolve) => setTimeout(resolve, 3000))

      // 5. 发送心跳
      console.log('💓 第四步：发送心跳消息...')
      sendHeartbeat()

      // 等待响应
      await new Promise((resolve) => setTimeout(resolve, 2000))
    } else {
      console.log('❌ WebSocket未连接，跳过消息发送测试')
    }

    // 6. 打印统计信息
    printStats()

    console.log('='.repeat(50))
    console.log('✅ WebSocket测试完成')
  }

  /**
   * 停止所有测试
   */
  const stopTest = () => {
    console.log('🛑 停止WebSocket测试')
    webSocket.disconnect()
  }

  return {
    // WebSocket基础功能
    ...webSocket,

    // 测试专用方法
    testBasicConnection,
    testEndpointConnection,
    sendGetFlyDataRequest,
    sendTextMessage,
    sendTestMessage,
    sendHeartbeat,
    printStats,
    runFullTest,
    stopTest,
  }
}
