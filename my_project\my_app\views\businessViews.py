#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
# @Time    :  2023/3/13 13:53
# <AUTHOR> chenxw
# @Email   : <EMAIL>
# @File    : businessViews.py
# @Descr   : 业务操作视图
# @Software: PyCharm
import logging
import time
import datetime
from django.db import connection
from django.views.decorators.csrf import csrf_exempt
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from vgis_log.logTools import LoggerHelper
from vgis_utils.vgis_http.httpTools import HttpHelper
from my_app.manage.businessManager import BuisnessOperator
from my_app.manage.commonManager import CommonOperator

from my_app.models import TtUploadFileData, TmDdistrict, TtAnnotationSysmbolData, TtAnnotationType, \
    TtFeatureAnnotationData, TtFriendFoeInfo, TtIntelligenceSource, TtPoiData, TtViewBookmarkData, SysLog, TtPointType, \
    TmBingtuan, TtFieldType, TtFeatureAnnotationeNewFields, TtServiceInterfaceType, TtKeyPointAreaData
from my_app.serializers import TtUploadFileDataSerializer, TmDdistrictSerializer, TtAnnotationSysmbolDataSerializer, \
    TtAnnotationTypeSerializer, TtFeatureAnnotationDataSerializer, TtFriendFoeInfoSerializer, \
    TtIntelligenceSourceSerializer, TtPoiDataSerializer, TtViewBookmarkDataSerializer, TtPointTypeSerializer, \
    TmBingtuanSerializer, TtFieldTypeSerializer, TtServiceInterfaceTypeSerializer, TtKeyPointAreaDataSerializer
from my_app.utils.businessUtility import businessHelper

from my_app.utils.commonUtility import CommonHelper
from my_app.utils.snowflake_id_util import SnowflakeIDUtil
from my_app.views.response.baseRespone import Result
from my_project import settings
from my_project.token import ExpiringTokenAuthentication
import os
from django.http import HttpResponse
from wsgiref.util import FileWrapper
from django.http import StreamingHttpResponse
from django.http import FileResponse
logger = logging.getLogger('django')
from my_app import models



# 上传文件视图集
class TtUploadFileDataViewSet(viewsets.ModelViewSet):
    # 查询集，获取所有TtUploadFileData对象并按id排序
    queryset = TtUploadFileData.objects.all().order_by('id')
    # 序列化器类，用于将TtUploadFileData对象转换为JSON格式
    serializer_class = TtUploadFileDataSerializer
    # 权限类，要求用户必须经过身份验证
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)

    @action(detail=False, methods=['POST'])
    def download_one_file(slef, request):
        params = request.data
        if params["type"] == "task_slt":
            fileattr = models.TtTaskSltFileData.objects.filter(id=params["id"])[0]
        elif params["type"] == "dron_bask":
            fileattr = models.TtDronBaskFileData.objects.filter(id=params["id"])[0]
        elif params["type"] == "alert_f":
            fileattr = models.TtDronAlertFileData.objects.filter(id=params["id"])[0]
        else:
            fileattr = models.TtUploadFileData.objects.filter(id=params["id"])[0]
        filename = """{}.{}""".format(fileattr.file_name, fileattr.file_suffix)
        file_path = fileattr.path

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return HttpResponse("文件不存在", status=404)

        file = open(file_path, 'rb')
        response = StreamingHttpResponse(FileWrapper(file), content_type='application/octet-stream')
        response['Content-Disposition'] = 'attachment; filename="{}"'.format(filename)
        # response['Content-Disposition'] = "attachment; filename*=utf-8''{}".format(escape_uri_path(filename))

        # file = open(zip_file_name, 'rb')  # 打开文件
        # response = FileResponse(file)  # 创建FileResponse对象
        return response
    # 上传单个文件的方法
    @action(detail=False, methods=['POST'], url_path='uploadFile')
    @csrf_exempt
    def upload_file(self, request):
        # 创建CommonOperator实例，用于处理文件上传操作，传入数据库连接对象
        uploadOperator = CommonOperator(connection)
        # 记录请求的JSON数据的键值对到日志中
        CommonHelper.logger_json_key_value(request, logger)
        # 调用uploadOperator的upload_single_file方法处理上传请求
        res = uploadOperator.upload_single_file(request)
        # 返回包含上传结果的响应
        return Response(res)

    # 上传自定义图标的方法
    @action(detail=False, methods=['POST'], url_path='uploadCustomImage')
    @csrf_exempt
    def uploadCustomImage(self, request):
        # 创建CommonOperator实例
        uploadOperator = CommonOperator(connection)
        # 记录请求的JSON数据的键值对到日志中
        CommonHelper.logger_json_key_value(request, logger)
        # 调用uploadOperator的upload_custom_image方法处理上传请求
        res = uploadOperator.upload_custom_image(request)
        # 返回包含上传结果的响应
        return Response(res)

    # 上传多个文件的方法
    @action(detail=False, methods=['POST'], url_path='uploadFiles')
    @csrf_exempt
    def upload_files(self, request):
        # 创建CommonOperator实例
        uploadOperator = CommonOperator(connection)
        # 记录请求的JSON数据的键值对到日志中
        CommonHelper.logger_json_key_value(request, logger)
        # 调用uploadOperator的upload_multi_files方法处理上传请求
        res = uploadOperator.upload_multi_files(request)
        # 返回包含上传结果的响应
        return Response(res)


# 兵团数据视图集
class TmBingtuanViewSet(viewsets.ModelViewSet):
    # 查询集，获取所有TmBingtuan对象并按id排序
    queryset = TmBingtuan.objects.all().order_by('id')
    # 序列化器类，用于将TmBingtuan对象转换为JSON格式
    serializer_class = TmBingtuanSerializer
    # 权限类，要求用户必须经过身份验证
    permission_classes = (IsAuthenticated,)
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)

    # 获取新疆兵团的师级数据的方法
    @action(detail=False, methods=['GET'], url_path='getDivisionByCorps')
    def get_division_by_corps(self, request, *args, **kwargsst):
        try:
            # 创建CommonOperator实例，用于处理数据查询操作
            query = CommonOperator(connection)
            # 记录请求的JSON数据的键值对到日志中
            CommonHelper.logger_json_key_value(request, logger)
            # 从请求的查询参数中获取兵团代码，默认为空字符串
            corps_code = self.request.query_params.get('corps_code', '')
            # 调用query的get_division_by_corps方法获取师级数据
            res = query.get_division_by_corps(corps_code, request)
        except Exception as exp:
            # 如果发生异常，构建错误响应数据
            res = {
               'success': False,
                'info': "获取新疆兵团的师级数据失败:{}".format(str(exp))
            }
        # 返回包含查询结果或错误信息的响应
        return Response(res)

    # 通过师级获取团级数据的方法
    @action(detail=False, methods=['GET'], url_path='getRegimentByDivision')
    def get_regiment_by_division(self, request, *args, **kwargsst):
        try:
            # 创建CommonOperator实例
            query = CommonOperator(connection)
            # 记录请求的JSON数据的键值对到日志中
            CommonHelper.logger_json_key_value(request, logger)
            # 从请求的查询参数中获取师级代码，默认为空字符串
            regiment_code = self.request.query_params.get('division_code', '')
            # 调用query的get_regiment_by_division方法获取团级数据
            res = query.get_regiment_by_division(regiment_code, request)
        except Exception as exp:
            # 如果发生异常，构建错误响应数据
            res = {
               'success': False,
                'info': "通过师级获取团级数据失败:{}".format(str(exp))
            }
        # 返回包含查询结果或错误信息的响应
        return Response(res)


# 分省数据视图集
class TmDdistrictViewSet(viewsets.ModelViewSet):
    # 查询集，获取所有TmDdistrict对象并按id排序
    queryset = TmDdistrict.objects.all().order_by('id')
    # 序列化器类，用于将TmDdistrict对象转换为JSON格式
    serializer_class = TmDdistrictSerializer
    # 权限类，要求用户必须经过身份验证
    permission_classes = (IsAuthenticated,)
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)

    # 获取全国的分地区分省数据的方法
    @action(detail=False, methods=['GET'], url_path='getRegionAndProvince')
    def get_region_and_province(self, request, *args, **kwargsst):
        try:
            # 创建CommonOperator实例
            query = CommonOperator(connection)
            # 记录请求的JSON数据的键值对到日志中
            CommonHelper.logger_json_key_value(request, logger)
            # 调用query的get_region_and_province方法获取分地区分省数据
            res = query.get_region_and_province(request)
        except Exception as exp:
            # 如果发生异常，构建错误响应数据
            res = {
               'success': False,
                'info': "获取全国的分地区分省数据失败:{}".format(str(exp))
            }
        # 返回包含查询结果或错误信息的响应
        return Response(res)

    # 通过省份获取地市数据的方法
    @action(detail=False, methods=['GET'], url_path='getCityByProvince')
    def get_city_by_province(self, request, *args, **kwargsst):
        try:
            # 创建CommonOperator实例
            query = CommonOperator(connection)
            # 记录请求的JSON数据的键值对到日志中
            CommonHelper.logger_json_key_value(request, logger)
            # 从请求的查询参数中获取省份代码，默认为空字符串
            province_code = self.request.query_params.get('province_code', '')
            # 调用query的get_city_by_province方法获取地市数据
            res = query.get_city_by_province(province_code, request)
        except Exception as exp:
            # 如果发生异常，构建错误响应数据
            res = {
               'success': False,
                'info': "通过省份获取地市数据失败:{}".format(str(exp))
            }
        # 返回包含查询结果或错误信息的响应
        return Response(res)

    # 通过地市获取区县数据的方法
    @action(detail=False, methods=['GET'], url_path='getCountyByCity')
    def get_county_by_city(self, request, *args, **kwargsst):
        try:
            # 创建CommonOperator实例
            query = CommonOperator(connection)
            # 记录请求的JSON数据的键值对到日志中
            CommonHelper.logger_json_key_value(request, logger)
            # 从请求的查询参数中获取地市代码，默认为空字符串
            city_code = self.request.query_params.get('city_code', '')
            # 调用query的get_county_by_city方法获取区县数据
            res = query.get_county_by_city(city_code, request)
        except Exception as exp:
            # 如果发生异常，构建错误响应数据
            res = {
               'success': False,
                'info': "通过地市获取区县数据失败:{}".format(str(exp))
            }
        # 返回包含查询结果或错误信息的响应
        return Response(res)


# 标注符号数据表viewer类
class TtAnnotationSysmbolDataViewSet(viewsets.ModelViewSet):
    # 查询集，获取所有TtAnnotationSysmbolData对象并按id排序
    queryset = TtAnnotationSysmbolData.objects.all().order_by('id')
    # 序列化器类，用于将TtAnnotationSysmbolData对象转换为JSON格式
    serializer_class = TtAnnotationSysmbolDataSerializer
    # 权限类，要求用户必须经过身份验证
    permission_classes = (IsAuthenticated,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)

    # 标注符号查询方法
    @action(detail=False, methods=['POST'], url_path='search_symbol')
    def search_symbol(self, request):
        # 定义操作的功能标题
        function_title = "标注符号查询"
        # 创建BuisnessOperator实例，用于处理标注符号查询操作
        buisnessOperator = BuisnessOperator(connection)
        # 记录请求的JSON数据的键值对到日志中
        CommonHelper.logger_json_key_value(request, logger)
        # 调用buisnessOperator的search_symbol方法进行标注符号查询
        res = buisnessOperator.search_symbol(request, function_title, connection)
        # 返回包含查询结果的响应
        return Response(res)

# 标注类型字典表viewer类
class TtAnnotationTypeViewSet(viewsets.ModelViewSet):
    queryset = TtAnnotationType.objects.all().order_by('id')
    serializer_class = TtAnnotationTypeSerializer
    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)


# 要素标注数据表viewer类
class TtFeatureAnnotationDataViewSet(viewsets.ModelViewSet):
    queryset = TtFeatureAnnotationData.objects.all().order_by('id')
    serializer_class = TtFeatureAnnotationDataSerializer
    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)

    # 根据类型获取标注数据
    @action(detail=False, methods=['POST'], url_path='get_annotation_data_by_type')
    def get_poi_dataget_annotation_data_by_type_by_type(self, request):
        # 定义操作的功能标题
        function_title = "根据类型获取标注数据"
        # 创建业务操作类的实例，传入数据库连接对象
        buisnessOperator = BuisnessOperator(connection)
        # 记录请求的JSON数据的键值对到日志中
        CommonHelper.logger_json_key_value(request, logger)
        # 调用业务操作类的方法，根据请求数据获取标注数据
        res = buisnessOperator.get_annotation_data_by_type(request, function_title, connection)
        # 返回包含查询结果的响应
        return Response(res)

    # 标注数据分页查询
    @action(detail=False, methods=['POST'], url_path='query_list')
    def query_list(self, request):
        try:
            # 定义操作的功能标题
            function_title = "标注数据分页查询"
            # 定义失败标识
            fail_flag = "失败"
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)
            # 创建业务操作类的实例，传入数据库连接对象
            operator = BuisnessOperator(connection)
            # 调用业务操作类的方法进行标注数据分页查询
            response_data = operator.query_annotation_data_list(request)
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            # 返回查询结果的响应
            return response_data
        except Exception as exp:
            # 构建失败信息
            msg = "{}{}".format(function_title, fail_flag)
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path,
                                                       request.auth.user, request,
                                                       function_title, msg, None)
            # 返回失败结果，包含异常信息
            return Result.fail(msg, str(exp))

    # 获取历史已新增字段列表
    @action(detail=False, methods=['POST'], url_path='get_history_add_field_list')
    def get_history_add_field_list(self, request):
        try:
            # 定义操作的功能标题
            function_title = "获取历史已新增字段列表"
            # 定义失败标识
            fail_flag = "失败"
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)
            # 创建业务操作类的实例，传入数据库连接对象
            operator = BuisnessOperator(connection)
            # 调用业务操作类的方法获取历史已新增字段列表
            res = operator.get_annotation_data_all_history_field()
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            # 返回包含查询结果的响应
            return Response(res)
        except Exception as exp:
            # 构建失败信息
            msg = "{}{}".format(function_title, fail_flag)
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path,
                                                       request.auth.user, request,
                                                       function_title, msg, None)
            # 返回失败结果，包含异常信息
            return Result.fail(msg, str(exp))

    # 全文检索
    @action(detail=False, methods=['POST'], url_path='full_search')
    def full_search(self, request):
        # 定义操作的功能标题
        function_title = "全文检索"
        # 创建业务操作类的实例，传入数据库连接对象
        buisnessOperator = BuisnessOperator(connection)
        # 记录请求的JSON数据的键值对到日志中
        CommonHelper.logger_json_key_value(request, logger)
        # 调用业务操作类的方法进行全文检索
        res = buisnessOperator.full_search(request, function_title, connection)
        # 返回包含检索结果的响应
        return Response(res)

    # 高级检索
    @action(detail=False, methods=['POST'], url_path='advance_search')
    def advance_search(self, request):
        # 定义操作的功能标题
        function_title = "高级检索"
        # 创建业务操作类的实例，传入数据库连接对象
        buisnessOperator = BuisnessOperator(connection)
        # 记录请求的JSON数据的键值对到日志中
        CommonHelper.logger_json_key_value(request, logger)
        # 调用业务操作类的方法进行高级检索
        res = buisnessOperator.advance_search(request, function_title, connection)
        # 返回包含检索结果的响应
        return Response(res)

    def create(self, request, *args, **kwargs):
        # 记录操作开始时间
        start = time.perf_counter()
        # 记录开始时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        # 创建业务操作类的实例，传入数据库连接对象
        buisnessOperator = BuisnessOperator(connection)
        # 从请求数据中获取标注名称
        name = request.data["name"]
        # 检查数据库中是否已存在相同名称的标注
        if len(TtFeatureAnnotationData.objects.filter(name=name)) > 0:
            # 若存在，构建失败响应数据
            res = {
                'success': False,
                'info': "新增的标注名称:{}已存在，请换个名称".format(name)
            }
            # 记录结束时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            # 记录操作总用时到日志
            logger.info("总共用时{}秒".format(t))
            # 插入日志信息，记录操作失败
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "新增视口书签失败",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            # 返回失败响应
            return Response(res)
        else:
            # 获取数据库游标
            cursor = connection.cursor()
            # 初始化历史新增字段和本次新增字段为 None
            history_fields = None
            new_fields = None
            # 检查请求数据中是否包含历史新增字段
            if "history_fields" in request.data:
                # 获取历史新增字段
                history_fields = request.data["history_fields"]
                # 从请求数据中移除历史新增字段
                request.data.pop("history_fields")
            # 创建本次新增属性字段
            if "new_fields" in request.data:
                # 获取本次新增字段
                new_fields = request.data["new_fields"]
                # 从请求数据中移除本次新增字段
                request.data.pop("new_fields")
                # 标记是否有重复的新增字段
                is_repeate_new_field = False
                # 遍历本次新增字段
                for new_field in new_fields:
                    # 检查新增字段的中文名称是否重复
                    is_repeate_new_field = buisnessOperator.is_annotation_data_add_field_cname_repeat(
                        new_field["field_cname"])
                    if is_repeate_new_field:
                        break
                if is_repeate_new_field:
                    # 若有重复，构建失败响应数据
                    res = {
                        'success': False,
                        'info': "新增的标注属性字段:{}已存在，请换个名称".format(new_field["field_cname"])
                    }
                    # 返回失败响应
                    return Response(res)
                # 遍历本次新增字段
                for new_field in new_fields:
                    # 将字段类型转换为达梦数据库支持的字段类型
                    new_field["dm_field_type"] = businessHelper.convert_to_dm_field_type(new_field["field_type"])
                    # 获取新增字段的英文名称
                    new_field["field_ename"] = buisnessOperator.get_annotation_data_add_field()
                    # 拼接增加字段的 SQL 语句
                    alter_sql = "ALTER TABLE tt_feature_annotation_data ADD {} {}; ".format(new_field["field_ename"],
                                                                                            new_field["dm_field_type"])
                    # 执行增加字段的 SQL 语句
                    cursor.execute(alter_sql)
                    # 提交数据库事务
                    connection.commit()
                    # 拼接给字段添加备注的 SQL 语句
                    alter_sql = "COMMENT ON COLUMN tt_feature_annotation_data.{} IS '{}'; ".format(
                        new_field["field_ename"], new_field["field_cname"])
                    # 执行给字段添加备注的 SQL 语句
                    cursor.execute(alter_sql)
                    # 提交数据库事务
                    connection.commit()

            # 录入固定属性字段内容
            # 生成唯一的 ID
            request.data["id"] = SnowflakeIDUtil.snowflakeId()
            # 获取友方敌方信息的中文名称
            request.data["friend_foe_information_name"] = TtFriendFoeInfo.objects.get(
                id=request.data["friend_foe_information"]).info_cname
            # 获取情报来源的中文名称
            request.data["intelligence_source_name"] = TtIntelligenceSource.objects.get(
                id=request.data["intelligence_source"]).source_cname
            # 获取标注类型的中文名称
            request.data["type_name"] = TtAnnotationType.objects.get(
                id=request.data["type"]).annotation_cname
            # 获取点类型的中文名称
            request.data["point_type_name"] = TtPointType.objects.get(
                id=request.data["point_type"]).point_cname
            # 获取创建用户的 ID
            request.data["create_user_id"] = request.auth.user_id
            # 获取当前时间并格式化为年-月-日 时:分:秒
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 在数据库中创建新的标注数据记录
            TtFeatureAnnotationData.objects.create(**request.data)

            # 更新空间字段内容
            # 拼接更新空间字段的 SQL 语句
            update_sql = "UPDATE tt_feature_annotation_data SET "
            update_sql += " geom = dmgeo.ST_PointFromText('point({} {})', 4326) ".format(
                request.data["lng"], request.data["lat"])

            # 录入历史新增属性字段
            if history_fields is not None and str(history_fields) != "":
                # 遍历历史新增字段
                for key, value in history_fields.items():
                    # 获取字段类型
                    field_type = buisnessOperator.get_annotation_data_field_type(key)
                    # 拼接更新字段的 SQL 语句
                    update_sql += " , " + businessHelper.build_update_field_sql(key,
                                                                                field_type,
                                                                                value)
            # 录入本次新增属性字段
            if new_fields is not None and str(new_fields) != "":
                # 遍历本次新增字段
                for field in new_fields:
                    # 拼接更新字段的 SQL 语句
                    update_sql += " , " + businessHelper.build_update_field_sql(field["field_ename"],
                                                                                field["field_type"],
                                                                                field["field_value"])
            # 拼接更新条件的 SQL 语句
            update_sql += " where id = {}".format(
                request.data["id"])
            # 执行更新 SQL 语句
            cursor.execute(update_sql)
            # 提交数据库事务
            connection.commit()

            # 更新标注数据新增字段表
            if new_fields is not None and str(new_fields) != "":
                # 初始化新增字段信息对象
                obj = {}
                obj["new_fields_cname"] = ""
                obj["new_fields_ename"] = ""
                obj["new_fields_type"] = ""
                # 遍历本次新增字段
                for field in new_fields:
                    # 拼接新增字段的中文名称
                    obj["new_fields_cname"] += field["field_cname"] + ","
                    # 拼接新增字段的英文名称
                    obj["new_fields_ename"] += field["field_ename"] + ","
                    # 拼接新增字段的类型
                    obj["new_fields_type"] += field["field_type"] + ","
                # 生成唯一的 ID
                obj["id"] = SnowflakeIDUtil.snowflakeId()
                # 关联标注数据的 ID
                obj["annotation_id"] = request.data["id"]
                # 去除新增字段中文名称末尾的逗号
                obj["new_fields_cname"] = obj["new_fields_cname"][:-1]
                # 去除新增字段英文名称末尾的逗号
                obj["new_fields_ename"] = obj["new_fields_ename"][:-1]
                # 去除新增字段类型末尾的逗号
                obj["new_fields_type"] = obj["new_fields_type"][:-1]
                # 在数据库中创建新的标注数据新增字段记录
                TtFeatureAnnotationeNewFields.objects.create(**obj)

            # 记录结束时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            # 记录操作总用时到日志
            logger.info("总共用时{}秒".format(t))
            # 插入日志信息，记录操作成功
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "新增标注",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            # 构建成功响应数据
            res = {
                'success': True,
                'info': "新增标注:{}成功".format(request.data["name"])
            }
            # 返回成功响应
            return Response(res)

    def update(self, request, *args, **kwargs):
        # 记录操作开始的时间戳
        start = time.perf_counter()
        # 记录操作开始时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        # 从 kwargs 中获取要更新的标注数据的主键 ID
        id = kwargs["pk"]
        # 检查数据库中是否存在该 ID 对应的标注数据
        if len(TtFeatureAnnotationData.objects.filter(id=id)) > 0:
            # 获取该标注数据的旧名称
            old_name = TtFeatureAnnotationData.objects.filter(id=id)[0].name

        # 创建业务操作类的实例，传入数据库连接对象
        buisnessOperator = BuisnessOperator(connection)
        # 从请求数据中获取新的标注名称
        new_name = request.data["name"]
        # 检查新名称与旧名称不同且数据库中已存在该新名称的标注数据
        if old_name != new_name and len(
                TtFeatureAnnotationData.objects.filter(name=new_name)) > 0:
            # 若存在重复名称，构建失败响应数据
            res = {
                'success': False,
                'info': "更新的标注名称:{}已存在，请换个名称".format(new_name)
            }
            # 记录操作结束时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的时间戳
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            # 记录操作总用时到日志
            logger.info("总共用时{}秒".format(t))
            # 插入日志信息，记录操作失败
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "更新视口书签失败",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            # 返回失败响应
            return Response(res)
        else:
            # 记录操作结束时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的时间戳
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            # 记录操作总用时到日志
            logger.info("总共用时{}秒".format(t))
            # 插入日志信息，记录操作开始
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "更新标注",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            # 获取数据库游标
            cursor = connection.cursor()
            # 初始化历史新增字段和本次新增字段为 None
            history_fields = None
            new_fields = None
            # 检查请求数据中是否包含历史新增字段
            if "history_fields" in request.data:
                # 获取历史新增字段
                history_fields = request.data["history_fields"]
                # 从请求数据中移除历史新增字段
                request.data.pop("history_fields")

            # 创建本次新增属性字段
            if "new_fields" in request.data:
                # 获取本次新增字段
                new_fields = request.data["new_fields"]
                # 从请求数据中移除本次新增字段
                request.data.pop("new_fields")
                # 标记是否有重复的新增字段
                is_repeate_new_field = False
                # 遍历本次新增字段
                for new_field in new_fields:
                    # 检查新增字段的中文名称是否重复
                    is_repeate_new_field = buisnessOperator.is_annotation_data_add_field_cname_repeat(
                        new_field["field_cname"])
                    if is_repeate_new_field:
                        break
                if is_repeate_new_field:
                    # 若有重复，构建失败响应数据
                    res = {
                        'success': False,
                        'info': "新增的标注属性字段:{}已存在，请换个名称".format(new_field["field_cname"])
                    }
                    # 返回失败响应
                    return Response(res)
                # 遍历本次新增字段
                for new_field in new_fields:
                    # 将字段类型转换为达梦数据库支持的字段类型
                    new_field["dm_field_type"] = businessHelper.convert_to_dm_field_type(new_field["field_type"])
                    # 获取新增字段的英文名称
                    new_field["field_ename"] = buisnessOperator.get_annotation_data_add_field()
                    # 拼接增加字段的 SQL 语句
                    alter_sql = "ALTER TABLE tt_feature_annotation_data ADD {} {}; ".format(new_field["field_ename"],
                                                                                            new_field["dm_field_type"])
                    # 执行增加字段的 SQL 语句
                    cursor.execute(alter_sql)
                    # 提交数据库事务
                    connection.commit()
                    # 拼接给字段添加备注的 SQL 语句
                    alter_sql = "COMMENT ON COLUMN tt_feature_annotation_data.{} IS '{}'; ".format(
                        new_field["field_ename"], new_field["field_cname"])
                    # 执行给字段添加备注的 SQL 语句
                    cursor.execute(alter_sql)
                    # 提交数据库事务
                    connection.commit()

            # 更新固定字段内容
            # 获取友方敌方信息的中文名称
            request.data["friend_foe_information_name"] = TtFriendFoeInfo.objects.get(
                id=request.data["friend_foe_information"]).info_cname
            # 获取情报来源的中文名称
            request.data["intelligence_source_name"] = TtIntelligenceSource.objects.get(
                id=request.data["intelligence_source"]).source_cname
            # 获取标注类型的中文名称
            request.data["type_name"] = TtAnnotationType.objects.get(
                id=request.data["type"]).annotation_cname
            # 获取点类型的中文名称
            request.data["point_type_name"] = TtPointType.objects.get(
                id=request.data["point_type"]).point_cname
            # 获取修改用户的 ID
            request.data["modify_user_id"] = request.auth.user_id
            # 获取当前时间并格式化为年-月-日 时:分:秒
            request.data["modify_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 调用父类的 update 方法更新标注数据
            super().update(request, *args, **kwargs)

            # 更新空间字段内容
            # 拼接更新空间字段的 SQL 语句
            update_sql = "UPDATE tt_feature_annotation_data SET "
            update_sql += " geom = dmgeo.ST_PointFromText('point({} {})', 4326) ".format(
                request.data["lng"], request.data["lat"])

            # 录入历史新增属性字段
            if history_fields is not None and str(history_fields) != "":
                # 遍历历史新增字段
                for key, value in history_fields.items():
                    # 获取字段类型
                    field_type = buisnessOperator.get_annotation_data_field_type(key)
                    # 拼接更新字段的 SQL 语句
                    update_sql += " , " + businessHelper.build_update_field_sql(key,
                                                                                field_type,
                                                                                value)
            # 录入本次新增属性字段
            if new_fields is not None and str(new_fields) != "":
                # 遍历本次新增字段
                for field in new_fields:
                    # 拼接更新字段的 SQL 语句
                    update_sql += " , " + businessHelper.build_update_field_sql(field["field_ename"],
                                                                                field["field_type"],
                                                                                field["field_value"])
            # 拼接更新条件的 SQL 语句
            update_sql += " where id = {}".format(
                id)
            # 执行更新 SQL 语句
            cursor.execute(update_sql)
            # 提交数据库事务
            connection.commit()

            # 更新标注数据新增字段表（要把以前的历史新增字段包含进来）
            # 初始化新增字段信息对象
            obj = {}
            # 获取该标注数据对应的新增字段记录
            annotationNewFieldObject = TtFeatureAnnotationeNewFields.objects.get(annotation_id=id)
            # 获取历史新增字段的中文名称
            obj["new_fields_cname"] = annotationNewFieldObject.new_fields_cname
            # 获取历史新增字段的英文名称
            obj["new_fields_ename"] = annotationNewFieldObject.new_fields_ename
            # 获取历史新增字段的类型
            obj["new_fields_type"] = annotationNewFieldObject.new_fields_type
            # 初始化新增字段中文名称列表
            new_fields_cname_list = []
            # 初始化新增字段英文名称列表
            new_fields_ename_list = []
            # 初始化新增字段类型列表
            new_fields_type_list = []
            # 若历史新增字段中文名称不为空
            if obj["new_fields_cname"] is not None and str(obj["new_fields_cname"]) != "":
                # 将历史新增字段中文名称按逗号分割成列表
                new_fields_cname_list = obj["new_fields_cname"].split(",")
            # 若历史新增字段英文名称不为空
            if obj["new_fields_ename"] is not None and str(obj["new_fields_ename"]) != "":
                # 将历史新增字段英文名称按逗号分割成列表
                new_fields_ename_list = obj["new_fields_ename"].split(",")
            # 若历史新增字段类型不为空
            if obj["new_fields_type"] is not None and str(obj["new_fields_type"]) != "":
                # 将历史新增字段类型按逗号分割成列表
                new_fields_type_list = obj["new_fields_type"].split(",")
            # 若本次新增字段不为空
            if new_fields is not None and str(new_fields) != "":
                # 遍历本次新增字段
                for field in new_fields:
                    # 将本次新增字段的英文名称添加到列表中
                    new_fields_ename_list.append(field["field_ename"])
                    # 将本次新增字段的中文名称添加到列表中
                    new_fields_cname_list.append(field["field_cname"])
                    # 将本次新增字段的类型添加到列表中
                    new_fields_type_list.append(field["field_type"])
                # 将新增字段中文名称列表用逗号连接成字符串
                obj["new_fields_cname"] = ",".join(new_fields_cname_list)
                # 将新增字段英文名称列表用逗号连接成字符串
                obj["new_fields_ename"] = ",".join(new_fields_ename_list)
                # 将新增字段类型列表用逗号连接成字符串
                obj["new_fields_type"] = ",".join(new_fields_type_list)
                # 更新该标注数据对应的新增字段记录
                TtFeatureAnnotationeNewFields.objects.filter(annotation_id=id).update(**obj)

            # 构建成功响应数据
            res = {
                'success': True,
                'info': "修改标注成功"
            }
            # 返回成功响应
            return Response(res)

    # 删除标注数据的方法
    def destroy(self, request, *args, **kwargs):
        # 操作标题
        title = "删除标注"
        # 初始化响应内容
        res = ""
        # 从 kwargs 中获取要删除的标注数据的主键 ID
        id = kwargs["pk"]
        # 记录操作开始的时间戳
        start = time.perf_counter()
        try:
            # 调用父类的 destroy 方法删除标注数据
            super().destroy(request, *args, **kwargs)
            # 同时删除该标注数据对应的新增字段记录
            TtFeatureAnnotationeNewFields.objects.filter(annotation_id=id).delete()
            # 记录操作结束时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的时间戳
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            # 记录操作总用时到日志
            logger.info("总共用时{}秒".format(t))
            # 插入日志信息，记录操作成功
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title,
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            # 构建成功响应数据
            res = {
                'success': True,
                'info': "{}(编号为{})成功".format(title, id)
            }
        except Exception as exp:
            # 若删除过程中出现异常，构建失败响应数据
            res = {
                'success': True,
                'info': "{}(编号为{})失败，原因为：{}".format(title, id, str(exp))
            }
        finally:
            # 最终返回响应
            return Response(res)


# 敌我信息字典表viewer类
class TtFriendFoeInfoViewSet(viewsets.ModelViewSet):
    queryset = TtFriendFoeInfo.objects.all().order_by('id')
    serializer_class = TtFriendFoeInfoSerializer
    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)


# 情报来源字典表viewer类
class TtIntelligenceSourceViewSet(viewsets.ModelViewSet):
    queryset = TtIntelligenceSource.objects.all().order_by('id')
    serializer_class = TtIntelligenceSourceSerializer
    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)


# 标注点类型字典表viewer类
class TtPointTypeViewSet(viewsets.ModelViewSet):
    queryset = TtPointType.objects.all().order_by('id')
    serializer_class = TtPointTypeSerializer
    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)


# 字段类型字典表viewer类
class TtFieldTypeViewSet(viewsets.ModelViewSet):
    queryset = TtFieldType.objects.all().order_by('id')
    serializer_class = TtFieldTypeSerializer
    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)


# POI数据表viewer类
class TtPoiDataViewSet(viewsets.ModelViewSet):
    queryset = TtPoiData.objects.all().order_by('id')
    serializer_class = TtPoiDataSerializer
    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)

    # 根据类型获取poi数据
    @action(detail=False, methods=['POST'], url_path='get_poi_data_by_type')
    def get_poi_data_by_type(self, request):
        function_title = "根据类型获取POI数据"
        buisnessOperator = BuisnessOperator(connection)
        CommonHelper.logger_json_key_value(request, logger)
        res = buisnessOperator.get_poi_data_by_type(request, function_title, connection)
        return Response(res)


# 视口书签数据表viewer类
class TtViewBookmarkDataViewSet(viewsets.ModelViewSet):
    queryset = TtViewBookmarkData.objects.all().order_by('id')
    serializer_class = TtViewBookmarkDataSerializer
    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)

    # 方向书签数据分页查询
    # 定义一个视图集方法，用于处理方向书签数据的分页查询请求
    @action(detail=False, methods=['POST'], url_path='query_list')
    def query_list(self, request):
        try:
            # 定义操作的功能标题，用于日志记录和结果反馈
            function_title = "方向书签数据分页查询"
            # 定义失败标识，用于构建失败信息
            fail_flag = "失败"
            # 记录日志开始信息，包含操作的起始时间等
            start = LoggerHelper.set_start_log_info(logger)
            # 创建业务操作类的实例，传入数据库连接对象，用于执行具体的业务逻辑
            operator = BuisnessOperator(connection)
            # 调用业务操作类的方法，根据请求数据进行方向书签数据的分页查询
            response_data = operator.query_view_bookmark_data_list(request)
            # 记录日志结束信息，包含操作的结束时间、请求路径、用户信息等
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            # 返回查询结果的响应
            return response_data
        except Exception as exp:
            # 若查询过程中出现异常，构建失败信息
            msg = "{}{}".format(function_title, fail_flag)
            # 记录异常日志信息，包含异常的详细信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path,
                                                       request.auth.user, request,
                                                       function_title, msg, None)
            # 返回失败结果，包含异常信息
            return Result.fail(msg, str(exp))

    # 重写 list 方法，用于获取方向书签列表
    def list(self, request, *args, **kwargs):
        try:
            # 定义操作的功能标题，用于日志记录和结果反馈
            function_title = "获取方向书签列表"
            # 记录日志开始信息，包含操作的起始时间等
            start = LoggerHelper.set_start_log_info(logger)
            # 对查询集进行过滤，根据请求的条件筛选数据
            queryset = self.filter_queryset(self.get_queryset())
            # 对查询集进行分页处理
            page = self.paginate_queryset(queryset)
            if page is not None:
                # 若分页成功，对分页后的数据进行序列化
                serializer = self.get_serializer(page, many=True)
                # 获取分页响应对象
                page_response = self.get_paginated_response(serializer.data)
                # 为分页响应数据添加 url 头信息
                page_response.data["url_head"] = CommonHelper.get_url_head()
                # 定义成功标识，用于构建成功信息
                suceess_flag = "成功"
                # 构建成功响应数据，包含分页数据和成功信息
                data = Result.list(page_response.data, message=function_title + "{}".format(suceess_flag))
                # 记录日志结束信息，包含操作的结束时间、请求路径、用户信息等
                LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                              function_title)
            # 返回包含方向书签列表的响应
            return Response(data)
        except Exception as exp:
            # 若获取列表过程中出现异常，定义失败标识
            fail_flag = "失败"
            # 构建失败信息
            msg = "{}{}".format(function_title, fail_flag)
            # 记录异常日志信息，包含异常的详细信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path,
                                                       request.auth.user, request,
                                                       function_title, msg, None)
            # 返回失败结果，包含异常信息
            return Result.fail(msg, str(exp))

    # 重写 create 方法，用于创建新的视口书签
    def create(self, request, *args, **kwargs):
        # 记录操作开始时间，用于性能统计
        start = time.perf_counter()
        # 记录开始时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        # 从请求数据中获取视口书签名
        view_bookmark_name = request.data["view_bookmark_name"]
        # 检查数据库中是否已存在相同名称的视口书签
        if len(TtViewBookmarkData.objects.filter(view_bookmark_name=view_bookmark_name)) > 0:
            # 若存在，构建失败响应数据
            res = {
                'success': False,
                'info': "新增的视口书签名:{}已存在，请换个名称".format(view_bookmark_name)
            }
            # 记录结束时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间，用于性能统计
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            # 记录操作总用时到日志
            logger.info("总共用时{}秒".format(t))
            # 插入日志信息，记录操作失败
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "新增视口书签失败",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            # 返回失败响应
            return Response(res)
        else:
            # 若不存在重复名称，为新视口书签生成唯一的 ID
            request.data["id"] = SnowflakeIDUtil.snowflakeId()
            # 记录创建用户的 ID
            request.data["create_user_id"] = request.auth.user_id
            # 记录创建时间，格式为年-月-日 时:分:秒
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 在数据库中创建新的视口书签记录
            TtViewBookmarkData.objects.create(**request.data)
            # 记录结束时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间，用于性能统计
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            # 记录操作总用时到日志
            logger.info("总共用时{}秒".format(t))
            # 插入日志信息，记录操作成功
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "新增视口书签",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            # 构建成功响应数据
            res = {
                'success': True,
                'info': "新增视口书签:{}成功".format(request.data["view_bookmark_name"])
            }
            # 返回成功响应
            return Response(res)

    # 重写 update 方法，用于更新视口书签信息
    def update(self, request, *args, **kwargs):
        # 记录操作开始时间，用于性能统计
        start = time.perf_counter()
        # 记录开始时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        # 从 kwargs 中获取要更新的视口书签的主键 ID
        id = kwargs["pk"]
        # 检查数据库中是否存在该 ID 对应的视口书签
        if len(TtViewBookmarkData.objects.filter(id=id)) > 0:
            # 获取该视口书签的旧名称
            old_view_bookmark_name = TtViewBookmarkData.objects.filter(id=id)[0].view_bookmark_name
        # 从请求数据中获取新的视口书签名
        new_view_bookmark_name = request.data["view_bookmark_name"]
        # 检查新名称与旧名称不同且数据库中已存在该新名称的视口书签
        if old_view_bookmark_name != new_view_bookmark_name and len(
                TtViewBookmarkData.objects.filter(view_bookmark_name=new_view_bookmark_name)) > 0:
            # 若存在重复名称，构建失败响应数据
            res = {
                'success': False,
                'info': "更新的视口书签名:{}已存在，请换个名称".format(new_view_bookmark_name)
            }
            # 记录结束时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间，用于性能统计
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            # 记录操作总用时到日志
            logger.info("总共用时{}秒".format(t))
            # 插入日志信息，记录操作失败
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "更新视口书签失败",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            # 返回失败响应
            return Response(res)
        else:
            # 若不存在重复名称，记录结束时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间，用于性能统计
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            # 记录操作总用时到日志
            logger.info("总共用时{}秒".format(t))
            # 插入日志信息，记录操作开始
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "更新视口书签",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            # 记录修改用户的 ID
            request.data["modify_user_id"] = request.auth.user_id
            # 记录修改时间，格式为年-月-日 时:分:秒
            request.data["modify_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 调用父类的 update 方法更新视口书签信息
            super().update(request, *args, **kwargs)
            # 构建成功响应数据
            res = {
                'success': True,
                'info': "修改视口书签成功"
            }
            # 返回成功响应
            return Response(res)

    # 重写 destroy 方法，用于删除视口书签
    def destroy(self, request, *args, **kwargs):
        # 操作标题
        title = "删除视口书签"
        # 初始化响应内容
        res = ""
        # 从 kwargs 中获取要删除的视口书签的主键 ID
        id = kwargs["pk"]
        # 记录操作开始时间，用于性能统计
        start = time.perf_counter()
        try:
            # 调用父类的 destroy 方法删除视口书签记录
            super().destroy(request, *args, **kwargs)
            # 记录结束时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间，用于性能统计
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            # 记录操作总用时到日志
            logger.info("总共用时{}秒".format(t))
            # 插入日志信息，记录操作成功
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title,
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            # 构建成功响应数据
            res = {
                'success': True,
                'info': "{}(编号为{})成功".format(title, id)
            }
        except Exception as exp:
            # 若删除过程中出现异常，构建失败响应数据
            res = {
                'success': True,
                'info': "{}(编号为{})失败，原因为：{}".format(title, id, str(exp))
            }
        finally:
            # 最终返回响应
            return Response(res)

# 定义一个视图集类，用于处理 TtServiceInterfaceType 模型的相关操作
class TtServiceInterfaceTypeViewSet(viewsets.ModelViewSet):
    # 定义查询集，获取 TtServiceInterfaceType 模型的所有对象，并按 id 字段升序排序
    queryset = TtServiceInterfaceType.objects.all().order_by('id')
    # 指定序列化器类，用于将 TtServiceInterfaceType 模型对象序列化为 JSON 数据
    serializer_class = TtServiceInterfaceTypeSerializer
    # 定义权限类，要求用户必须经过身份验证才能访问该视图集的接口
    permission_classes = (IsAuthenticated,)
    # token 认证方式，这里注释掉了默认的 TokenAuthentication
    # authentication_classes = (TokenAuthentication,)
    # 使用自定义的 token 认证方式
    authentication_classes = (ExpiringTokenAuthentication,)

    # 重写 list 方法，用于处理获取 TtServiceInterfaceType 列表的请求
    def list(self, request, *args, **kwargs):
        # 从请求的查询参数中获取 interface_type_category 参数的值
        interface_type_category = self.request.query_params.get('interface_type_category')
        # 根据获取到的 interface_type_category 参数值，过滤 TtServiceInterfaceType 模型的对象
        results = TtServiceInterfaceType.objects.filter(interface_type_category=interface_type_category)
        # 初始化一个空列表，用于存储序列化后的数据
        data = []
        # 遍历过滤后的结果集
        for result in results:
            # 使用指定的序列化器对每个结果对象进行序列化，并将序列化后的数据添加到 data 列表中
            data.append(TtServiceInterfaceTypeSerializer(result).data)
        # 构建一个包含序列化后数据的字典，键为 'results'
        results = {'results': data}
        # 返回包含序列化后数据的响应
        return Response(results)

# 定义一个视图集类，用于处理 TtKeyPointAreaData 模型的相关操作
class TtKeyPointAreaDataViewSet(viewsets.ModelViewSet):
    # 定义查询集，获取 TtKeyPointAreaData 模型的所有对象，并按 id 字段升序排序
    queryset = TtKeyPointAreaData.objects.all().order_by('id')
    # 指定序列化器类，用于将 TtKeyPointAreaData 模型对象序列化为 JSON 数据
    serializer_class = TtKeyPointAreaDataSerializer
    # 定义权限类，要求用户必须经过身份验证才能访问该视图集的接口
    permission_classes = (IsAuthenticated,)
    # token 认证方式，这里注释掉了默认的 TokenAuthentication
    # authentication_classes = (TokenAuthentication,)
    # 使用自定义的 token 认证方式
    authentication_classes = (ExpiringTokenAuthentication,)

    # 重写 list 方法，用于处理获取 TtKeyPointAreaData 列表的请求
    def list(self, request, *args, **kwargs):
        # 这里注释掉的代码表示原本可能想从请求查询参数中获取 interface_type_category 参数，但实际未使用
        # interface_type_category = self.request.query_params.get('interface_type_category')
        # 获取 TtKeyPointAreaData 模型的所有对象
        results = TtKeyPointAreaData.objects.all()
        # 初始化一个空列表，用于存储序列化后的数据
        data = []
        # 遍历所有结果对象
        for result in results:
            # 修改结果对象的 doc_url 属性，将其拼接成完整的 URL 格式，使用项目配置的 IP、端口、静态 URL 等信息
            result.doc_url = "http://{}:{}{}report/{}".format(settings.PROJECT_SERVICE_IP,
                                                              settings.PROJECT_SERVICE_PORT,
                                                              settings.STATIC_URL,
                                                              result.doc_url)
            # 使用指定的序列化器对修改后的结果对象进行序列化，并将序列化后的数据添加到 data 列表中
            data.append(TtKeyPointAreaDataSerializer(result).data)
        # 遍历序列化后的数据列表
        for each in data:
            # 从每个序列化后的数据中移除不需要返回给前端的字段
            each.pop("create_user_id")
            each.pop("create_time")
            each.pop("modify_user_id")
            each.pop("modify_time")
            each.pop("poi_type")
        # 构建一个包含序列化后数据的字典，键为 'results'
        results = {'results': data}
        # 返回包含序列化后数据的响应
        return Response(results)
