<script setup lang="ts">
import BasePannel from '@/components/common/BasePannel/BasePannel.vue'
import WarningInfo from './WarningInfo/WarningInfo.vue'
import FlightTask from './FlightTask/FlightTask.vue'
import ImageSearch from './ImageSearch/ImageSearch.vue'
import { computed } from 'vue'
import { useUIStore } from '@/stores/uiStore'

// 使用UI Store
const uiStore = useUIStore()

interface TabItem {
  id: string
  name: string
  title: string
  content: string
}

const tabs: TabItem[] = [
  {
    id: 'warning',
    name: '预警信息',
    title: '预警信息',
    content: '重要预警及风险提示信息',
  },
  {
    id: 'flight-task',
    name: '飞行任务',
    title: '飞行任务',
    content: '无人机飞行任务信息及状态',
  },
  {
    id: 'image-search',
    name: '时空搜图',
    title: '时空搜图',
    content: '基于时间和空间位置的图像搜索功能',
  },
]

// 使用Store中的activeTab状态
const activeTab = computed(() => uiStore.activeRightPanelTab)

const switchTab = (tabId: string) => {
  // 将tabId映射到Store中的类型
  const tabMap: Record<string, any> = {
    warning: 'warning',
    'flight-task': 'flight-task',
    'image-search': 'image-search',
  }

  const mappedTabId = tabMap[tabId] || tabId
  uiStore.setActiveRightPanelTab(mappedTabId)
}
</script>

<template>
  <BasePannel
    class="right-panel"
    :tabs="tabs"
    @activeTabChange="switchTab"
    :defaultActiveTab="activeTab"
  >
    <!-- 内容区域 -->
    <template #content>
      <WarningInfo v-if="activeTab === 'warning'" class="panel-content" />
      <FlightTask v-else-if="activeTab === 'flight-task'" class="panel-content" />
      <ImageSearch v-else-if="activeTab === 'image-search'" class="panel-content" />
    </template>
  </BasePannel>
</template>

<style scoped lang="scss">
.right-panel {
  width: 100%;
  height: 100%;

  .panel-content {
    height: calc(100% - 2rem);
  }
}
</style>
