/**
 * 全局UI状态管理 Store
 * 管理界面布局、面板状态、tab切换等UI相关状态
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'

// 右侧面板tab类型
export type RightPanelTab = 'warning' | 'flight-task' | 'image-search'

// 左侧面板状态
export type LeftPanelState = 'expanded' | 'collapsed'

// 底部面板状态
export type BottomPanelState = 'visible' | 'hidden'

// 视图模式类型
export type ViewMode = 'normal' | 'flight'

// 飞控模式下的背景显示类型
export type FlightBackgroundType = 'map' | 'monitor' | 'drone_video'

export const useUIStore = defineStore('ui', () => {
  // ===== 状态定义 =====

  // 右侧面板当前激活的tab
  const activeRightPanelTab = ref<RightPanelTab>('warning')

  // 左侧面板状态
  const leftPanelState = ref<LeftPanelState>('expanded')

  // 底部面板状态
  const bottomPanelState = ref<BottomPanelState>('visible')

  // 右侧面板是否可见
  const rightPanelVisible = ref(true)

  // 左侧面板是否可见
  const leftPanelVisible = ref(true)

  // 全屏模式
  const isFullscreen = ref(false)

  // 加载状态
  const isLoading = ref(false)

  // 错误信息
  const errorMessage = ref('')

  // 成功信息
  const successMessage = ref('')

  // 视图模式
  const viewMode = ref<ViewMode>('normal')

  // 监控卡片显示状态
  const showMonitorCards = ref(false)

  // 飞控模式下的背景显示类型
  const flightBackgroundType = ref<FlightBackgroundType>('map')

  // 地图显示位置：'background' | 'card-1' | 'card-2'
  const mapDisplayPosition = ref<'background' | 'card-1' | 'card-2'>('background')

  // ===== Actions =====

  /**
   * 切换右侧面板tab
   */
  const setActiveRightPanelTab = (tab: RightPanelTab) => {
    activeRightPanelTab.value = tab
  }

  /**
   * 切换到飞行任务tab
   */
  const switchToFlightTaskTab = () => {
    setActiveRightPanelTab('flight-task')
  }

  /**
   * 切换到预警信息tab
   */
  const switchToWarningTab = () => {
    setActiveRightPanelTab('warning')
  }

  /**
   * 切换到时空搜图tab
   */
  const switchToImageSearchTab = () => {
    setActiveRightPanelTab('image-search')
  }

  /**
   * 切换左侧面板状态
   */
  const toggleLeftPanel = () => {
    leftPanelState.value = leftPanelState.value === 'expanded' ? 'collapsed' : 'expanded'
  }

  /**
   * 设置左侧面板状态
   */
  const setLeftPanelState = (state: LeftPanelState) => {
    leftPanelState.value = state
  }

  /**
   * 切换底部面板状态
   */
  const toggleBottomPanel = () => {
    bottomPanelState.value = bottomPanelState.value === 'visible' ? 'hidden' : 'visible'
  }

  /**
   * 设置底部面板状态
   */
  const setBottomPanelState = (state: BottomPanelState) => {
    bottomPanelState.value = state
  }

  /**
   * 切换右侧面板可见性
   */
  const toggleRightPanel = () => {
    rightPanelVisible.value = !rightPanelVisible.value
  }

  /**
   * 设置右侧面板可见性
   */
  const setRightPanelVisible = (visible: boolean) => {
    rightPanelVisible.value = visible
  }

  /**
   * 切换左侧面板可见性
   */
  const toggleLeftPanelVisible = () => {
    leftPanelVisible.value = !leftPanelVisible.value
  }

  /**
   * 设置左侧面板可见性
   */
  const setLeftPanelVisible = (visible: boolean) => {
    leftPanelVisible.value = visible
  }

  /**
   * 切换全屏模式
   */
  const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value
  }

  /**
   * 设置全屏模式
   */
  const setFullscreen = (fullscreen: boolean) => {
    isFullscreen.value = fullscreen
  }

  /**
   * 设置加载状态
   */
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  /**
   * 显示错误信息
   */
  const showError = (message: string) => {
    errorMessage.value = message
    // 3秒后自动清除
    setTimeout(() => {
      errorMessage.value = ''
    }, 3000)
  }

  /**
   * 显示成功信息
   */
  const showSuccess = (message: string) => {
    successMessage.value = message
    // 3秒后自动清除
    setTimeout(() => {
      successMessage.value = ''
    }, 3000)
  }

  /**
   * 清除所有消息
   */
  const clearMessages = () => {
    errorMessage.value = ''
    successMessage.value = ''
  }

  /**
   * 设置视图模式
   */
  const setViewMode = (mode: ViewMode) => {
    viewMode.value = mode
  }

  /**
   * 显示监控卡片
   */
  const showMonitorCardsAction = () => {
    showMonitorCards.value = true
  }

  /**
   * 隐藏监控卡片
   */
  const hideMonitorCards = () => {
    showMonitorCards.value = false
  }

  /**
   * 设置飞控模式下的背景显示类型
   */
  const setFlightBackgroundType = (type: FlightBackgroundType) => {
    flightBackgroundType.value = type
  }

  /**
   * 切换飞控模式下的背景显示
   */
  const switchFlightBackground = (type: FlightBackgroundType) => {
    if (viewMode.value === 'flight') {
      flightBackgroundType.value = type

      // 根据背景类型更新地图显示位置
      if (type === 'map') {
        mapDisplayPosition.value = 'background'
      } else {
        // 当背景不是地图时，地图显示在卡片中
        // 需要根据MonitorCards组件中的实际卡片排序来确定地图位置

        // MonitorCards的排序逻辑：地图卡片总是排在第一位（如果存在）
        // 所以当背景不是地图时，地图总是显示在第一个卡片位置
        mapDisplayPosition.value = 'card-1'

        // 调试日志
        console.log('切换背景到:', type)
        console.log('地图显示位置:', mapDisplayPosition.value)
      }
    }
  }

  /**
   * 进入飞控模式
   */
  const enterFlightMode = async () => {
    viewMode.value = 'flight'
    // 进入飞控模式时默认显示地图背景
    flightBackgroundType.value = 'map'
    // 地图显示在背景位置
    mapDisplayPosition.value = 'background'
    // 显示监控卡片
    showMonitorCards.value = true

    // 获取飞控实时数据
    const { useDroneStore } = await import('@/stores/droneStore')
    const droneStore = useDroneStore()

    // 获取当前选中无人机的飞控数据
    const selectedDroneId = droneStore.selectedDrone?.id
    if (selectedDroneId) {
      await droneStore.fetchFlightRealTimeData(selectedDroneId)

      // 启动模拟数据更新
      const { mockDataSimulator } = await import('@/utils/mockDataSimulator')
      mockDataSimulator.start(selectedDroneId, (data) => {
        droneStore.updateFlightRealTimeData(data)
      })
    } else {
      // 如果没有选中无人机，尝试获取第一个可用的无人机
      if (droneStore.drones.length > 0) {
        const firstDrone = droneStore.drones[0]
        droneStore.selectDrone(firstDrone.id)
        await droneStore.fetchFlightRealTimeData(firstDrone.id)

        // 启动模拟数据更新
        const { mockDataSimulator } = await import('@/utils/mockDataSimulator')
        mockDataSimulator.start(firstDrone.id, (data) => {
          droneStore.updateFlightRealTimeData(data)
        })
      }
    }
  }

  /**
   * 退出飞控模式，回到正常模式
   */
  const exitFlightMode = async () => {
    viewMode.value = 'normal'
    // 重置飞控背景类型
    flightBackgroundType.value = 'map'
    // 重置地图显示位置
    mapDisplayPosition.value = 'background'
    hideMonitorCards()

    // 停止模拟数据更新
    const { mockDataSimulator } = await import('@/utils/mockDataSimulator')
    mockDataSimulator.stop()
  }

  /**
   * 回到正常模式
   */
  const returnToNormalMode = async () => {
    viewMode.value = 'normal'
    // 重置飞控背景类型
    flightBackgroundType.value = 'map'
    // 重置地图显示位置
    mapDisplayPosition.value = 'background'
    hideMonitorCards()

    // 停止模拟数据更新
    const { mockDataSimulator } = await import('@/utils/mockDataSimulator')
    mockDataSimulator.stop()
  }

  /**
   * 重置UI状态到默认值
   */
  const resetUIState = () => {
    activeRightPanelTab.value = 'warning'
    leftPanelState.value = 'expanded'
    bottomPanelState.value = 'visible'
    rightPanelVisible.value = true
    leftPanelVisible.value = true
    isFullscreen.value = false
    isLoading.value = false
    viewMode.value = 'normal'
    showMonitorCards.value = false
    flightBackgroundType.value = 'map'
    mapDisplayPosition.value = 'background'
    clearMessages()
  }

  return {
    // 状态
    activeRightPanelTab,
    leftPanelState,
    bottomPanelState,
    rightPanelVisible,
    leftPanelVisible,
    isFullscreen,
    isLoading,
    errorMessage,
    successMessage,
    viewMode,
    showMonitorCards,
    flightBackgroundType,
    mapDisplayPosition,

    // 方法
    setActiveRightPanelTab,
    switchToFlightTaskTab,
    switchToWarningTab,
    switchToImageSearchTab,
    toggleLeftPanel,
    setLeftPanelState,
    toggleBottomPanel,
    setBottomPanelState,
    toggleRightPanel,
    setRightPanelVisible,
    toggleLeftPanelVisible,
    setLeftPanelVisible,
    toggleFullscreen,
    setFullscreen,
    setLoading,
    showError,
    showSuccess,
    clearMessages,
    resetUIState,
    setViewMode,
    showMonitorCardsAction,
    hideMonitorCards,
    setFlightBackgroundType,
    switchFlightBackground,
    enterFlightMode,
    exitFlightMode,
    returnToNormalMode,
  }
})
