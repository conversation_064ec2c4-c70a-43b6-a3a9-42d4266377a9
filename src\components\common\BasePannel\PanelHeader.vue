<template>
  <div class="panel-tabs">
    <div class="tabs-container">
      <div
        v-for="tab in tabs"
        :key="tab.id"
        class="tab"
        :class="{ active: activeTab === tab.id }"
        @click="switchTab(tab.id)"
      >
        {{ tab.name }}
      </div>
    </div>
    <UIcon name="mdi:menu" @click="emit('close')" size="1rem" class="close-icon" />
  </div>
</template>

<script setup lang="ts">
import UIcon from '@/components/common/UIcon/UIcon.vue'

interface TabItem {
  id: string
  name: string
}

const props = withDefaults(
  defineProps<{
    tabs: TabItem[]
    defaultActiveTab?: string
  }>(),
  {
    defaultActiveTab: undefined,
  },
)

const activeTab = ref<string>(
  props.defaultActiveTab || (props.tabs.length > 0 ? props.tabs[0].id : ''),
)

// 监听 defaultActiveTab 的变化，同步更新内部状态
watch(
  () => props.defaultActiveTab,
  (newActiveTab) => {
    if (newActiveTab && newActiveTab !== activeTab.value) {
      activeTab.value = newActiveTab
    }
  },
  { immediate: true },
)

const emit = defineEmits<{
  activeTabChange: [id: string]
  close: []
}>()

const switchTab = (tabId: string) => {
  activeTab.value = tabId
  emit('activeTabChange', tabId)
}
</script>

<style scoped lang="scss">
.panel-tabs {
  display: flex;
  align-items: center;
  padding: 0.2rem;
  border-radius: $border-radius-small;
  box-shadow: inset 0 0.5rem 0.5rem -0.5rem $glow-color;

  .tabs-container {
    display: flex;
    flex: 1;
    justify-content: space-evenly;
    align-items: center;
  }

  .tab {
    padding: 0.2rem;
    cursor: pointer;
    font-size: 0.7rem;
    color: $text-inactive;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;

    &:hover {
      color: $text-hover;
    }

    &.active {
      color: $text-active;
      border-bottom: 1px solid $glow-color;
    }
  }

  .close-icon {
    color: $text-inactive;
    cursor: pointer;
    margin-left: 0.5rem;
    flex-shrink: 0;

    &:hover {
      color: $text-hover;
    }
  }
}
</style>
