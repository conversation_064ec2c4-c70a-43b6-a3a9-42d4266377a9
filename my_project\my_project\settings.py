"""
Django settings for my_project project.

Generated by 'django-admin startproject' using Django 3.1.5.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.1/ref/settings/
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
LOGGER_ROOT = os.path.join(BASE_DIR, 'logger')

# 加载环境变量文件
env_file = BASE_DIR / '.env'
if env_file.exists():
    load_dotenv(env_file)

# 辅助函数：获取布尔值环境变量
def get_bool_env(key, default=False):
    value = os.getenv(key, str(default)).lower()
    return value in ('true', '1', 'yes', 'on')

# 辅助函数：获取整数环境变量
def get_int_env(key, default=0):
    try:
        return int(os.getenv(key, str(default)))
    except ValueError:
        return default

# 辅助函数：获取列表环境变量
def get_list_env(key, default=None):
    if default is None:
        default = []
    value = os.getenv(key, '')
    if value == '*':
        return ['*']
    return [item.strip() for item in value.split(',') if item.strip()] or default

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.1/howto/deployment/checklist/

APP_NAME = "my_app"
PROJECT_NAME = "my_project"
STATIC_NAME = "wrj_static"
# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('DJANGO_SECRET_KEY', '&8ue%+g+nyf8oy6ctogjia!q$o_qv@^sr44&px*63_k!=^$192')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = get_bool_env('DEBUG', True)

ALLOWED_HOSTS = get_list_env('ALLOWED_HOSTS', ['*'])

# Application definition

INSTALLED_APPS = [
    'daphne',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_apscheduler',
    # 'django.contrib.gis',
    # 项目工程
    APP_NAME,
    # rest framework框架
    'rest_framework',
    # 'rest_framework_gis',
    # 过滤器
    'django_filters',
    # 身份认证
    'rest_framework.authtoken',
    # 'my_models',  # 已迁移到 my_app.models
    # swagger文档
    # 'rest_framework_swagger',
    # swagger 文档现在变成了这个
    'drf_yasg',
    # 跨域
    'corsheaders',
    # 'channels',  # 用于websocket
    # 'sslserver',
    # 'werkzeug_debugger_runserver',
    # 'django_extensions'

]
# AUTH_USER_MODEL = "{}.SysUser".format(APP_NAME)

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    '{}.middleware.DisableCSRF'.format(APP_NAME),
    '{}.middleware.CORSMiddleware'.format(APP_NAME),
    '{}.middleware.EncryptionMiddleware'.format(APP_NAME)

]

# # SECURITY安全设置 - 支持http时建议开启
# SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
# # SECURE_SSL_REDIRECT = True # 将所有非SSL请求永久重定向到SSL
# SESSION_COOKIE_SECURE = True  # 仅通过https传输cookie
# CSRF_COOKIE_SECURE = True  # 仅通过https传输cookie
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True  # 严格要求使用https协议传输
# SECURE_HSTS_PRELOAD = True  # HSTS为
# SECURE_HSTS_SECONDS = 60
# SECURE_CONTENT_TYPE_NOSNIFF = True  # 防止浏览器猜测资产的内容类型

# 跨域增加忽略
CORS_ALLOW_CREDENTIALS = True
CORS_ORIGIN_ALLOW_ALL = True
# CORS_ORIGIN_WHITELIST = (
#     '*'
# )

CORS_ALLOW_METHODS = (
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
    'VIEW',
)

CORS_ALLOW_HEADERS = (
    'XMLHttpRequest',
    'X_FILENAME',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'Pragma',
)

REST_FRAMEWORK = {
    # Use Django's standard `django.contrib.auth` permissions,
    # or allow read-only access for unauthenticated users.
    # 身份认证
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.BasicAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        '{}.token.ExpiringTokenAuthentication'.format(PROJECT_NAME)  # 自定义token认证，增加了token失效时间
        # 'rest_framework.authentication.TokenAuthentication',  # token认证
    ),
    # 权限认证
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',  # IsAuthenticated 仅通过认证的用户
        'rest_framework.permissions.AllowAny',  # AllowAny 允许所有用户
        'rest_framework.permissions.IsAdminUser',  # IsAdminUser 仅管理员用户
        'rest_framework.permissions.IsAuthenticatedOrReadOnly',  # IsAuthenticatedOrReadOnly 认证的用户可以完全操作，否则只能get读取
    ),
    # 过滤器
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend', 'rest_framework.filters.OrderingFilter'),
    # 分页
    # 'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    # 分页
    'DEFAULT_PAGINATION_CLASS': 'my_project.customPageNumberPagination.CustomPageNumberPagination',
    'PAGE_SIZE': 100000000,  # 每页数目
    #  swagger文档
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.AutoSchema',

}

ROOT_URLCONF = '{}.urls'.format(PROJECT_NAME)

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, "templates")],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
            'libraries': {  # Adding this section should work around the issue.
                'staticfiles': 'django.templatetags.static',
            },
        },
    },
]

# WSGI_APPLICATION = '{}.wsgi.application'.format(PROJECT_NAME)
ASGI_APPLICATION = '{}.asgi.application'.format(PROJECT_NAME)

# # SECURITY安全设置 - 支持http时建议开启
# SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
# # SECURE_SSL_REDIRECT = True # 将所有非SSL请求永久重定向到SSL
# SESSION_COOKIE_SECURE = True  # 仅通过https传输cookie
# CSRF_COOKIE_SECURE = True  # 仅通过https传输cookie
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True  # 严格要求使用https协议传输
# SECURE_HSTS_PRELOAD = True  # HSTS为
# SECURE_HSTS_SECONDS = 60
# SECURE_CONTENT_TYPE_NOSNIFF = True  # 防止浏览器猜测资产的内容类型

# 指定日志的目录所在，如果不存在则创建
LOG_ROOT = os.path.join(BASE_DIR, 'log')
if not os.path.exists(LOG_ROOT):
    os.mkdir(LOG_ROOT)

# 日志配置（基本跟原生的TimedRotatingFileHandler一样）
LOGGING = {
    'version': 1,
    'disable_existing_loggers': True,
    'formatters': {
        'standard': {
            'format': '[%(asctime)s] [%(filename)s:%(lineno)d] [%(module)s:%(funcName)s] '
                      '[%(levelname)s]- %(message)s'},
        'simple': {  # 简单格式
            'format': '%(levelname)s %(message)s'
        },
    },
    'handlers': {
        'servers': {
            'class': '{}.log.InterceptTimedRotatingFileHandler'.format(PROJECT_NAME),  # 这个路径看你本地放在哪里(下面的log文件)
            'filename': os.path.join(LOG_ROOT, 'myapp.log'),
            # 每天自动归档写新的日志文件
            'when': "D",
            'interval': 1,
            'backupCount': 1,
            'formatter': 'standard',
            'encoding': 'utf-8',
        },
        'db': {
            'class': '{}.log.InterceptTimedRotatingFileHandler'.format(PROJECT_NAME),  # 这个路径看你本地放在哪里
            'filename': os.path.join(LOG_ROOT, 'myapp_db.log'),
            # 每天自动归档写新的日志文件
            'when': "D",
            'interval': 1,
            'backupCount': 1,
            'formatter': 'standard',
            'encoding': 'utf-8',
            'logging_levels': ['info']  # 😒注意这里，这是自定义类多了一个参数，因为我只想让db日志有debug文件，所以我只看sql，这个可以自己设置
        }
    },
    'loggers': {
        # Django全局绑定
        'django': {
            'handlers': ['servers'],
            'propagate': True,
            'level': os.getenv('LOG_LEVEL_DJANGO', 'INFO')
        },
        'celery': {
            'handlers': ['servers'],
            'propagate': False,
            'level': os.getenv('LOG_LEVEL_CELERY', 'INFO')
        },
        'django.db.backends': {
            'handlers': ['db'],
            'propagate': False,
            'level': os.getenv('LOG_LEVEL_DB', 'INFO')
        },
        'django.request': {
            'handlers': ['servers'],
            'propagate': False,
            'level': os.getenv('LOG_LEVEL_REQUEST', 'DEBUG')
        },
    }
}


# Channel Layers 配置 - 根据环境变量动态选择后端
CHANNEL_BACKEND = os.getenv('CHANNEL_LAYERS_BACKEND', 'memory')

if CHANNEL_BACKEND == 'redis':
    # Redis 模式 - 适用于生产环境和多进程部署
    CHANNEL_LAYERS = {
        "default": {
            "BACKEND": "channels_redis.core.RedisChannelLayer",
            "CONFIG": {
                "hosts": [(os.getenv('CHANNEL_REDIS_HOST', '**************'), get_int_env('CHANNEL_REDIS_PORT', 6739))],
                "capacity": 1500,  # 每个频道的最大消息数
                "expiry": 60,      # 消息过期时间（秒）
            },
        },
    }
else:
    # 内存模式 - 适用于开发环境和单进程部署
    CHANNEL_LAYERS = {
        "default": {
            "BACKEND": "channels.layers.InMemoryChannelLayer",
            "CONFIG": {
                "capacity": 300,   # 每个频道的最大消息数
                "expiry": 60,      # 消息过期时间（秒）
            }
        },
    }

# # Redis缓存库配置
# CACHES = {
#     "default": {
#         "BACKEND": "django_redis.cache.RedisCache",
#         "LOCATION": "redis://*************:6379/0",
#         "OPTIONS": {
#             "CLIENT_CLASS": "django_redis.client.DefaultClient",
#             "COMPRESSOR": "django_redis.compressors.zlib.ZlibCompressor",
#             "CONNECTION_POOL_KWARGS": {"max_connections": 512},
#             "IGNORE_EXCEPTIONS": True,
#             "SOCKET_CONNECT_TIMEOUT": 5,  # in seconds
#             "SOCKET_TIMEOUT": 5,  # in seconds
#         }
#     }
# }


# dm数据库
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.django_dmPython',
#         'NAME': 'DAMENG',
#         'USER': 'GZWJ',
#         'PASSWORD': 'GZWJ00123',
#         # 'HOST': 'localhost',
#         'HOST': '*************',
#         'PORT': '5236',
#         'OPTIONS': {'local_code': 1, 'connection_timeout': 5}
#     }
# }
# PG数据库
DATABASES = {
    'default': {
        # 'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'USER': os.getenv('DB_USER', 'postgres'),
        'PASSWORD': os.getenv('DB_PASSWORD', '123'),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '5432'),
        'NAME': os.getenv('DB_NAME', 'WRJ2'),
        'CONN_MAX_AGE': get_int_env('DB_CONN_MAX_AGE', 60),
    }
}

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.1/topics/i18n/

# 设置为中文
LANGUAGE_CODE = 'zh-Hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True
# 不用世界时
USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.1/howto/static-files/


STATICFILES_DIRS = [os.path.join(BASE_DIR, '{}/{}'.format(APP_NAME, STATIC_NAME))]

# 根据项目名称修改url
STATIC_URL = '/collected_static/'

STATIC_ROOT = os.path.join(BASE_DIR, 'collected_static')

# 上传文件目录
UPLOAD_ROOT = os.path.join(BASE_DIR, '{}'.format(APP_NAME), '{}'.format(STATIC_NAME), 'upload')

# 是否使用模拟数据
IS_USE_MOCK_DATA = get_bool_env('IS_USE_MOCK_DATA', False)

# token失效时间，单位秒，开发可自行配置
AUTH_TOKEN_AGE = get_int_env('AUTH_TOKEN_AGE', 10800)

# 项目部署的IP、端口号、网络协议
PROJECT_WEB_PROTOCOL = os.getenv('PROJECT_WEB_PROTOCOL', 'http')
PROJECT_SERVICE_IP = os.getenv('PROJECT_SERVICE_IP', '127.0.0.1')
PROJECT_SERVICE_PORT = os.getenv('PROJECT_SERVICE_PORT', '16789')
# 调试时使用，部署时需注释
# PROJECT_SERVICE_PORT = '29618'

# 是否开启验证码
IS_USE_VERIFICATION_CODE = get_bool_env('IS_USE_VERIFICATION_CODE', False)

# 是否对请求响应做加解密
IS_ENCRYPTION = get_bool_env('IS_ENCRYPTION', False)

# 登录失败尝试次数
LOGIN_ERROR_ATTEMPTS = get_int_env('LOGIN_ERROR_ATTEMPTS', 4)

# 账号锁定后的解锁时间，单位秒
LOGIN_LOCKED_TIME = get_int_env('LOGIN_LOCKED_TIME', 600)

# Token的key
TOKEN_KEY = 'Authorization'

# 本地化的key,不能太长
LOCAL_KEY = 'Localization'

# 加密相关信息
ENCRPTION = {
    "key1": "uNl-LfGm6NKDQ1Uz9azZIEEzYnaLz68gz0UzaQvYFIY=",
    "key2": "Z0FBQUFBQmsyZWJGZl81NGRMVG9zTVdtQmhTalhfQkhvRVlaVElCMm1ubnZHTXFOb1otNG40ODFlX1JDNk5udmZQaWxrTTI4ODNsVjZyYVZNZDZUZ3NnbS0wMGpEVE1JZFVWS3pkU0pkWVpWcG1ZaTRoeHJDQTd1UXo1Mk5tZ19sdlZUUC1HZ0xiUkNKYWFhcVNuaTFMcTZSam96b3liZmNfMUtKYUxUdldkM3lyVngtYUZleE0zVEZ1cHlkTGdFRVlOQkhlR2dkT191VjJOWXFWSF9YamNuZkE1cURJTlpmWWpUZS1ON2lDZmlNX1MzSzk4eVFEQ2E1MHFTMUpCYjJVRVhrX2pDaEIwcnhVb3lvQ3hqRWF5aTJNVFYwamNtamxyYzFlMXFjaFpDVDBCZkhQdTFma1kwVV93dVNFamlpNUY1ejZIWG1VNEhfMGh0N1hrYXNfcnBobzZ5X1RtVXRiOUtWelZPTFpsNmVxc2xqUHdGYkswPQ==",
    "key3": "Z0FBQUFBQmsyZWJGOWxzeWtGZnVwa0tNdFBQSG1GSkxfRXZPS2lScmtMSHBZM0w4M3FJdXY1N0JHTlRwYjFILXY0akl0NjVvSFA0bkdWdENtTUx6czFab3AwQkpoQ1VNeXJ2QUFTMU9NZ200ZzlVMWZWdTU3eWt0WjhnaElZVzN3WUxZTzFZMkF6czhFN19JWk9zZmRsSDRBdzZuUUVQQW5VbFJpZ1oxVVdlVkVNWEJkSVdGX0VVNEZrRDUtQVFrQm9zalphRkxzLVBGanlnTEhUb0VPaE5YYnBaeDJaUm1laHRUOUxNamIycFMyMDlnYkJ0NTFpZVZxaElkZXlUcEQ5eUJBUTZBVkNkVHI5UktVeFJDMjlXNlhaVWNEZnNlZ09ZWE42Nk52eG9lWnNWOURVRVV4SVFGaGNVM2tuOVBiTHFyQzJOTzNFMUpMendYdWF4ZzBPWnBfc1RXWGxNMnJiNWxsRzQ0Uk12RF85NTR0U2I5SzdQSXc2c1c3N0w2T1cteTNPcWZhdHM0SHlYdXllbkc5ZG04V25TbHdrMC0xRHBXVktQTW1TT3pFMmZsZkNReDczanphYXlxNnpmaE5Db1k0ejBpNVo5N1owM0M4UVNJUWZNUmFSZVctQklaeG5LZE90amNTNHlCU1lzY1B6ZGlfRVczRk05T09udG14OWlSaXZWVnh0dmtwLXJvZUU1MFl4WjBRX0lIaUJhZF9RNTB2a2VYeUtaMmhmY2xTRWtJNFVBX0dHMnNKOGhYRF82bVJDa2Jfck5CeVhIU3BOQW82QmJwZjdXVUhZSEMxSGEwaUk3a0pwS3BoSDRoMDhkblNYVVlyQWV3QWVFSFdIZ1IydE5yeWRwd1ZtR1h6MXJsS28wWWZ6VWtUZzlWZG1nQjE3M1JOQUVvcjg4NThzczZmazg0Tmp6SjNzZXNCanpIek5YRnp0YXZhbFdhWDNYMV9NYXJFMExtZXdYLXhsZ3k2T3g5YlduV1hJRFEzbFVsTDhRYWZ0WFZNTEo3dlFyTHFKREV0ZWxJYnJKN3RRZmt3cE9Bc3F5TlZ3WVg4YTY4VW5MbnZuVkhudllTQ052S3ZwSWx5c2tuX1FuZ1F6ejAyTVZrUG5meTU2c2NSbWs2ZDlDUVA5QXdMLU9Sc3FqVVF6RXB0UG9YNkZSTnRreXZIR05qRW9JQkp4MmU2amw5YlhqZzl2RlFYS0lIck9HU3hHS185em1qLTY1UnV3eDl5SGFyT1lpV1REY3NsMzhEOEh1NFlHMHkwNlpWdWpuTGFVemtTSGFLZmZZWE02ZE1HcGxjRC1nR2NPb0Q3S0FjdXlpNEJjRWlER2h0X1VQR1VuZ0YwNEpzWVZvbmxDaXBSQ0dwSHBYTDBwZ1AycjFSd1pibndFRW41UjNtQzVIczVzYzgyQjdWZ2VlMm5kbnNVN1FqTlVHWHdoRGR2QUVXWGZnZFRRZVlNRk1xZHNGcVVCcEw3RmRsaVUtM29aZHZycV93cXByR291T0dxWnhBVF9VS0FaNjJJb29XOEhpbzBKa2JXbGJBY1Q3VFZGd1BqSGp3LVpaR0p5NGlwZUZfREV0dUNZSG90bnRQU3hPOUxweURyTlB4X3l6VHZRdDFGejREbVQ1QQ==",
    "key4": "b3sVBbMdxJ2pgK/XYPpKVEYdVNk8bUBt9bcpCMegEec3m/nfbvdvVgPlEm1Yd6aavB8jdUR1HF1vf13l/ADVddg6Cl1Yl+vXkDaKeKHa7bHoJpLAWt7i0mIVHSMhPJdQb2qRTbaPCu8MRZNsJWivgnnTSGEHy+vvGhvxCeWcmB0="
}

# 许可文件的路径
# WINDOWS_LICENSE_PATH = "C:/license/gzwj_license.lic"
WINDOWS_LICENSE_PATH = os.getenv('WINDOWS_LICENSE_PATH', "E:/NewDev/zhouzhou/drone-service/my_project/local.lic")
LINUX_LICENSE_PATH = os.getenv('LINUX_LICENSE_PATH', "/home/<USER>/license/gzwj_license.lic")

# 多线程数
MAX_THREAD_COUNT = get_int_env('MAX_THREAD_COUNT', 16)


# TOKEN是否用缓存
TOKEN_USE_CACHE = get_bool_env('TOKEN_USE_CACHE', False)




PAGE_MAX_SIZE = get_int_env('PAGE_MAX_SIZE', 200)

# import platform
# platformType = platform.system().lower()
# proj_pth = WINDOWS_PROJ_PATH if platformType == 'windows' else LINUX_PROJ_PATH
# os.environ['PROJ_LIB'] = proj_pth
ASGI_APPLICATION = 'my_project.asgi.application'

DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'


# 配置celery异步任务使用的redis cache
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://*************:6379/0')  # 使用Redis作为消息队列
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://*************:6379/0')
CELERY_TIMEZONE = os.getenv('CELERY_TIMEZONE', 'Asia/Shanghai')

# 配置supermap iserver
# SUPERMAP_ISERVER_URL = "http://127.0.0.1:8090/"
# SUPERMAP_ISERVER_PASSWORD = "Supermap736" //本机
SUPERMAP_ISERVER_URL = os.getenv('SUPERMAP_ISERVER_URL', "http://192.168.1.21:8090/")
SUPERMAP_ISERVER_PASSWORD = os.getenv('SUPERMAP_ISERVER_PASSWORD', "GZyy741258963!@#")
SUPERMAP_ISERVER_USER = os.getenv('SUPERMAP_ISERVER_USER', "WJYY")