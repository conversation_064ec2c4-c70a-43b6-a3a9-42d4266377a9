/**
 * 无人机状态管理 Store
 * 管理无人机列表、状态、位置、视频流等无人机相关状态
 */
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// 导入UI数据类型
import type {
  DroneBasicInfo,
  DroneListUIData,
  DroneStatsUIData,
  FlightRealTimeData,
  MultiScreenUIData,
} from '@/types/ui'

// 导入API
import { droneApi } from '@/api/drone'
import { flightApi } from '@/api/flight'
import { videoApi } from '@/api/video'

export const useDroneStore = defineStore('drone', () => {
  // ===== 状态定义 =====

  // 无人机基础数据
  const droneListData = ref<DroneListUIData | null>(null)

  // 飞控实时数据
  const flightRealTimeData = ref<FlightRealTimeData | null>(null)

  // 多屏监控数据
  const multiScreenData = ref<MultiScreenUIData | null>(null)

  // 数据加载状态
  const isLoadingDrones = ref(false)
  const isLoadingFlightData = ref(false)
  const isLoadingVideoData = ref(false)

  // 错误信息
  const error = ref<string | null>(null)

  // ===== 计算属性 =====

  // 当前选中的无人机
  const selectedDrone = computed(() => {
    if (!droneListData.value?.selectedDroneId) return null
    return (
      droneListData.value.drones.find(
        (drone) => drone.id === droneListData.value?.selectedDroneId,
      ) || null
    )
  })

  // 无人机列表
  const drones = computed(() => {
    return droneListData.value?.drones || []
  })

  // 在线无人机数量
  const onlineDronesCount = computed(() => {
    return droneListData.value?.onlineCount || 0
  })

  // 无人机状态统计
  const droneStats = computed(() => {
    if (!droneListData.value) {
      return { total: 0, online: 0, working: 0, offline: 0, error: 0, maintenance: 0 }
    }

    return {
      total: droneListData.value.totalCount,
      online: droneListData.value.onlineCount,
      working: droneListData.value.workingCount,
      offline: droneListData.value.offlineCount,
      error: 0, // 从API数据中获取
      maintenance: 0, // 从API数据中获取
    }
  })

  // ===== Actions =====

  /**
   * 获取无人机列表数据
   */
  const fetchDroneList = async () => {
    isLoadingDrones.value = true
    error.value = null

    try {
      const data = await droneApi.getDroneList()
      droneListData.value = data
      console.log('无人机列表数据获取成功', data)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取无人机数据失败'
      console.error('获取无人机列表失败:', err)
    } finally {
      isLoadingDrones.value = false
    }
  }

  /**
   * 获取飞控实时数据
   */
  const fetchFlightRealTimeData = async (droneId: string) => {
    isLoadingFlightData.value = true
    error.value = null

    try {
      const data = await flightApi.getFlightRealTimeData(droneId)
      flightRealTimeData.value = data
      console.log('飞控实时数据获取成功', data)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取飞控数据失败'
      console.error('获取飞控实时数据失败:', err)
    } finally {
      isLoadingFlightData.value = false
    }
  }

  /**
   * 更新飞控实时数据（供外部工具函数调用）
   */
  const updateFlightRealTimeData = (data: FlightRealTimeData) => {
    flightRealTimeData.value = data
  }

  /**
   * 获取多屏监控数据
   */
  const fetchMultiScreenData = async () => {
    isLoadingVideoData.value = true
    error.value = null

    try {
      const data = await videoApi.getMultiScreenData()
      multiScreenData.value = data
      console.log('多屏监控数据获取成功', data)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取视频数据失败'
      console.error('获取多屏监控数据失败:', err)
    } finally {
      isLoadingVideoData.value = false
    }
  }

  /**
   * 选择无人机
   */
  const selectDrone = (droneId: string | null) => {
    if (droneListData.value) {
      droneListData.value.selectedDroneId = droneId
    }
  }

  return {
    // 状态
    droneListData,
    flightRealTimeData,
    multiScreenData,
    isLoadingDrones,
    isLoadingFlightData,
    isLoadingVideoData,
    error,

    // 计算属性
    selectedDrone,
    drones,
    onlineDronesCount,
    droneStats,

    // 方法
    fetchDroneList,
    fetchFlightRealTimeData,
    updateFlightRealTimeData,
    fetchMultiScreenData,
    selectDrone,
  }
})
