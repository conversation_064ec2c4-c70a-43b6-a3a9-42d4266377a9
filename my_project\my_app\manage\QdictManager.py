import logging
import os
from my_app.models import SysLog


from vgis_office.vgis_excel.excelTools import ExcelH<PERSON>per
from my_project import settings
logger = logging.getLogger('django')
from my_app.views.response.baseRespone import Result
from vgis_log.logTools import Lo<PERSON><PERSON>elper
from my_app.models import TtSelectDict
from collections import defaultdict
import datetime
from collections import Counter
class QdictManager:
    def __init__(self, connection):
        self.connection = connection

    def get_select_dict(self, request):
        type = request.data["type"]
        sql = ''' 
                    SELECT  * FROM	tt_select_dict   where 1=1  and type ='{}'  '''.format(type)

        sql += " order by id asc"
        cursor = self.connection.cursor()
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = []
        for item in records:  # 3
            data_list.append(dict(list(zip(titile, item))))

        return data_list