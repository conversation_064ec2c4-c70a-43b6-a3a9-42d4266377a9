# from django.contrib.gis.db import models
from django.db import models
# from my_models.mymodels import WjyyGzAuthUser
# from django.contrib.auth.models import AbstractUser


# Create your models here.
# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each ForeignKey and OneToOneField has `on_delete` set to the desired behavior
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.


# Create your models here.
# 认证相关的模型
class AuthUser(models.Model):
    id = models.BigIntegerField(primary_key=True)
    password = models.CharField(max_length=128, db_comment='用户密码')
    last_login = models.DateTimeField(blank=True, null=True, db_comment='上次登录时间')
    is_superuser = models.Bo<PERSON>an<PERSON>ield(db_comment='是否是超级用户')
    username = models.CharField(max_length=150, blank=True, null=True, db_comment='完整姓名')
    first_name = models.CharField(max_length=150, blank=True, null=True, db_comment='名字')
    last_name = models.CharField(max_length=150, blank=True, null=True, db_comment='姓氏')
    email = models.CharField(max_length=254, blank=True, null=True, db_comment='邮箱')
    is_staff = models.BooleanField(blank=True, null=True, db_comment='是否是职员')
    is_active = models.BooleanField(blank=True, null=True, db_comment='账户是否激活')
    date_joined = models.DateTimeField(blank=True, null=True, db_comment='账户创建时间')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建者用户id')
    department_id = models.BigIntegerField(blank=True, null=True, db_comment='部门id')
    fullname = models.CharField(max_length=255, blank=True, null=True, db_comment='完整姓名')
    login_error_attempts = models.SmallIntegerField(blank=True, null=True, db_comment='登录错误次数')
    login_locked_until = models.DateTimeField(blank=True, null=True, db_comment='登录锁定时间')
    mobile = models.CharField(max_length=100, blank=True, null=True, db_comment='电话')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='修改时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='修改者用户id')
    sex = models.CharField(max_length=255, blank=True, null=True, db_comment='性别')
    status = models.IntegerField(blank=True, null=True, db_comment='状态，1正常，0 禁用')
    age = models.IntegerField(blank=True, null=True, db_comment='年龄')
    educational_background = models.CharField(max_length=100, blank=True, null=True, db_comment='教育背景')
    ethnicity = models.CharField(max_length=100, blank=True, null=True, db_comment='所属民族')
    id_card = models.CharField(max_length=20, blank=True, null=True, db_comment='身份证号码')
    template_id = models.BigIntegerField(blank=True, null=True, db_comment='模板id')

    class Meta:
        managed = False
        db_table = 'auth_user'
        db_table_comment = '系统用户表'

class AuthGroup(models.Model):
    name = models.CharField(unique=True, max_length=150)

    class Meta:
        managed = False
        db_table = 'auth_group'


class AuthGroupPermissions(models.Model):
    group = models.ForeignKey(AuthGroup, models.DO_NOTHING)
    permission = models.ForeignKey('AuthPermission', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'auth_group_permissions'
        unique_together = (('group', 'permission'),)


class AuthPermission(models.Model):
    name = models.CharField(max_length=255)
    content_type = models.ForeignKey('DjangoContentType', models.DO_NOTHING)
    codename = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'auth_permission'
        unique_together = (('content_type', 'codename'),)


class AuthUserGroups(models.Model):
    user = models.ForeignKey(AuthUser, models.DO_NOTHING)
    group = models.ForeignKey(AuthGroup, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'auth_user_groups'
        unique_together = (('user', 'group'),)



class AuthUserUserPermissions(models.Model):
    user = models.ForeignKey(AuthUser, models.DO_NOTHING)
    permission = models.ForeignKey(AuthPermission, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'auth_user_user_permissions'
        unique_together = (('user', 'permission'),)




class DjangoAdminLog(models.Model):
    action_time = models.DateTimeField()
    object_id = models.TextField(blank=True, null=True)
    object_repr = models.CharField(max_length=200)
    action_flag = models.SmallIntegerField()
    change_message = models.TextField()
    content_type = models.ForeignKey('DjangoContentType', models.DO_NOTHING, blank=True, null=True)
    user = models.ForeignKey(AuthUser, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'django_admin_log'


class DjangoContentType(models.Model):
    app_label = models.CharField(max_length=100)
    model = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'django_content_type'
        unique_together = (('app_label', 'model'),)


class DjangoMigrations(models.Model):
    app = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    applied = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'django_migrations'


class DjangoSession(models.Model):
    session_key = models.CharField(primary_key=True, max_length=40)
    session_data = models.TextField()
    expire_date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'wjyy_gz_django_session'


# 业务数据相关的模型

# 配置参数表
class SysConfig(models.Model):
    id = models.BigAutoField(primary_key=True)
    param_key = models.CharField(max_length=50, blank=True, null=True)
    param_value = models.CharField(max_length=2000, blank=True, null=True)
    status = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_CONFIG'


# 部门表
class SysDepartment(models.Model):
    department_id = models.BigAutoField(primary_key=True)
    department_name = models.CharField(max_length=128, blank=True, null=True)
    parent_id = models.BigIntegerField(blank=True, null=True)
    state = models.CharField(max_length=1, blank=True, null=True)
    state_date = models.DateField(blank=True, null=True)
    order_num = models.BigIntegerField(blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    del_flag = models.IntegerField(blank=True, null=True)
    master = models.CharField(max_length=255, blank=True, null=True)
    tel = models.CharField(max_length=255, blank=True, null=True)
    email = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_DEPARTMENT'


# 日志表
class SysLog(models.Model):
    id = models.BigAutoField(primary_key=True)
    username = models.CharField(max_length=50, blank=True, null=True)
    operation = models.CharField(max_length=50, blank=True, null=True)
    method = models.CharField(max_length=200, blank=True, null=True)
    params = models.CharField(max_length=5000, blank=True, null=True)
    time = models.FloatField()
    ip = models.CharField(max_length=64, blank=True, null=True)
    create_date = models.DateTimeField(blank=True, null=True)
    error_info = models.TextField(blank=True, null=True)
    log_type = models.BigIntegerField(blank=True, null=True, db_comment='日志类型：0:登录日志，1：操作日志，2：异常日志')
    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_LOG'
        db_table_comment = '系统日志表'


# 菜单表
class SysMenu(models.Model):
    menu_id = models.BigAutoField(primary_key=True)
    parent_id = models.BigIntegerField(blank=True, null=True)
    name = models.CharField(max_length=50, blank=True, null=True)
    url = models.CharField(max_length=200, blank=True, null=True)
    perms = models.CharField(max_length=500, blank=True, null=True)
    type = models.IntegerField(blank=True, null=True)
    icon = models.CharField(max_length=50, blank=True, null=True)
    order_num = models.IntegerField(blank=True, null=True)
    is_show = models.CharField(max_length=1, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_MENU'


class SysOss(models.Model):
    id = models.BigAutoField(primary_key=True)
    url = models.CharField(max_length=200, blank=True, null=True)
    create_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_OSS'


# 角色表
class SysRole(models.Model):
    role_id = models.BigAutoField(primary_key=True)
    role_name = models.CharField(max_length=100, blank=True, null=True)
    remark = models.CharField(max_length=100, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_ROLE'


# 用户角色菜单表
class SysRoleMenu(models.Model):
    id = models.BigAutoField(primary_key=True)
    role_id = models.BigIntegerField(blank=True, null=True)
    menu_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_ROLE_MENU'


# 用户表-未用
class SysUser(models.Model):
    user_id = models.BigAutoField(primary_key=True)
    username = models.CharField(max_length=50)
    password = models.CharField(max_length=100, blank=True, null=True)
    salt = models.CharField(max_length=20, blank=True, null=True)
    email = models.CharField(max_length=100, blank=True, null=True)
    mobile = models.CharField(max_length=100, blank=True, null=True)
    status = models.IntegerField(blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    department_id = models.BigIntegerField(blank=True, null=True)
    sex = models.CharField(max_length=255, blank=True, null=True)
    fullname = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'sys_user'


# 用户角色表
class SysUserRole(models.Model):
    id = models.BigAutoField(primary_key=True)
    user_id = models.BigIntegerField(blank=True, null=True)
    role_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_USER_ROLE'


# 用户登录token表
class SysUserToken(models.Model):
    user_id = models.BigAutoField(primary_key=True)
    token = models.CharField(max_length=100)
    expire_time = models.DateTimeField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_USER_TOKEN'


# 上传文件表
class TtUploadFileData(models.Model):
    id = models.BigAutoField(primary_key=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    file_size = models.DecimalField(max_digits=24, decimal_places=0, blank=True, null=True)
    file_name = models.CharField(max_length=255, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    file_suffix = models.CharField(max_length=255, blank=True, null=True)
    path = models.CharField(max_length=512, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_TT_UPLOAD_FILE_DATA'

class TtTaskSltFileData(models.Model):
    id = models.BigAutoField(primary_key=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    file_size = models.DecimalField(max_digits=24, decimal_places=0, blank=True, null=True)
    file_name = models.CharField(max_length=255, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    file_suffix = models.CharField(max_length=255, blank=True, null=True)
    path = models.CharField(max_length=512, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tt_task_slt_file_data'

class TtDronBaskFileData(models.Model):
    id = models.BigAutoField(primary_key=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    file_size = models.DecimalField(max_digits=24, decimal_places=0, blank=True, null=True)
    file_name = models.CharField(max_length=255, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    file_suffix = models.CharField(max_length=255, blank=True, null=True)
    path = models.CharField(max_length=512, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tt_dron_bask_file_data'


# 行政区域表
class TmRegion(models.Model):
    id = models.BigAutoField(primary_key=True)
    dis_name = models.CharField(max_length=255, blank=True, null=True)
    dis_code = models.IntegerField(blank=True, null=True)
    parent_code = models.IntegerField(blank=True, null=True)
    type = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tm_region'


# 行政区划表
class TmDdistrict(models.Model):
    id = models.BigAutoField(primary_key=True)
    dis_name = models.CharField(max_length=255, blank=True, null=True)
    dis_code = models.IntegerField(blank=True, null=True)
    parent_code = models.IntegerField(blank=True, null=True)
    type = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_TM_DISTRICT'


# 兵团区划表
class TmBingtuan(models.Model):
    id = models.BigAutoField(primary_key=True)
    dis_name = models.CharField(max_length=255, blank=True, null=True)
    dis_code = models.IntegerField(blank=True, null=True)
    parent_code = models.IntegerField(blank=True, null=True)
    type = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tm_bingtuan'


class SysParam(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    param_en_key = models.CharField(max_length=200, db_comment="参数键英文名称")
    param_cn_key = models.CharField(max_length=200, db_comment="参数键中文名称")
    param_value = models.CharField(max_length=255, db_comment="参数键值")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    update_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_PARAM'
        db_table_comment = '系统参数表'


class TtAnnotationSysmbolData(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    cname = models.CharField(max_length=100, db_comment="符号中文名称")
    ename = models.CharField(max_length=100, db_comment="符号英文名称")
    type = models.BigIntegerField(blank=True, null=True,
                                  db_comment='标注符号类型：1 军标标注 2 图元标注 3 三维模型 4 自定义标注')
    remark = models.CharField(max_length=2000, db_comment="符号描述")
    sysmbol_path = models.CharField(max_length=255, db_comment="标注符号相对路径")
    td_sysmbol = models.CharField(max_length=255, db_comment="3D符号相对路径")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_annotation_sysmbol_data'
        db_table_comment = '标注符号数据表'


class TtAnnotationType(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    annotation_ename = models.CharField(max_length=100, db_comment="标注类型英文名称")
    annotation_cname = models.CharField(max_length=100, db_comment="标注类型中文名称")
    remark = models.CharField(max_length=255, db_comment="备注信息")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_annotation_type'
        db_table_comment = '标注类型字典表'


class TtFeatureAnnotationData(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    name = models.CharField(max_length=100, db_comment="标注名称")
    type = models.BigIntegerField(blank=True, null=True,
                                  db_comment='标注类型：1 军标标注 2 图元标注 3 三维模型 4 自定义标注')
    type_name = models.CharField(max_length=100, db_comment="标注类型名称")
    point_type = models.BigIntegerField(blank=True, null=True,
                                        db_comment='点类型：1 地理要素 2 重要目标 3 战场事件 4 战略支援点')
    point_type_name = models.CharField(max_length=100, db_comment="点类型名称")
    remark = models.CharField(max_length=2000, db_comment="标注描述", null=True)
    friend_foe_information = models.BigIntegerField(blank=True, null=True,
                                                    db_comment='敌我信息：0 全部 1我方，2敌方 3 友邻 4 中立 5  不明')
    friend_foe_information_name = models.CharField(max_length=100, db_comment="敌我信息名称")
    intelligence_source = models.BigIntegerField(blank=True, null=True,
                                                 db_comment='情报来源：0 全部 1 技术/武器侦查 2 无线电侦听 3 无人机 4 各级通报 5 观察 6 捕获 7 其他')
    intelligence_source_name = models.CharField(max_length=100, db_comment="情报来源名称")
    lng = models.FloatField(blank=True, null=True, db_comment='经度')
    lat = models.FloatField(blank=True, null=True, db_comment='纬度')
    elv = models.FloatField(blank=True, null=True, db_comment='海拔')
    annotation_sysmbol_id = models.BigIntegerField(blank=True, null=True,
                                                   db_comment='标注符号编号')
    annotation_sysmbol_style = models.TextField(blank=True, null=True,
                                                db_comment='情报来源：0 全部 1 技术/武器侦查 2 无线电侦听 3 无人机 4 各级通报 5 观察 6 捕获 7 其他')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_feature_annotation_data'
        db_table_comment = '要素标注数据表'


class TtFriendFoeInfo(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    info_ename = models.CharField(max_length=100, db_comment="信息英文名称")
    info_cname = models.CharField(max_length=100, db_comment="信息中文名称")
    remark = models.CharField(max_length=255, db_comment="备注信息")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_friend_foe_info'
        db_table_comment = '敌我信息字典表'


class TtFieldType(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    type_ename = models.CharField(max_length=100, db_comment="字段类型英文名")
    type_cname = models.CharField(max_length=100, db_comment="字段类型中文名")
    remark = models.CharField(max_length=255, db_comment="备注信息")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_field_type'
        db_table_comment = '字段类型字典表'


class TtServiceData(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    service_ename = models.CharField(max_length=100, db_comment="服务英文名")
    service_cname = models.CharField(max_length=100, db_comment="服务中文名")
    service_aliasname = models.CharField(max_length=100, db_comment="服务别名")
    service_url = models.CharField(max_length=255, db_comment="服务url")
    map_names = models.CharField(max_length=2550, db_comment="地图信息")  #
    service_interface_type = models.CharField(max_length=100, db_comment="服务接口类型")  # REST服务、WFS服务、WFS服务
    service_component_type = models.CharField(max_length=100, db_comment="服务组件类型")  # 地图服务，数据服务，三维服务
    service_status = models.SmallIntegerField(blank=True, null=True, db_comment='服务状态')  # 0:停用 1:启用
    remark = models.CharField(max_length=255, db_comment="备注信息")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_service_data'
        db_table_comment = '服务数据表'


class TtServiceInterfaceType(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    interface_type_ename = models.CharField(max_length=100, db_comment="服务接口类型英文名")
    interface_type_ename = models.CharField(max_length=100, db_comment="服务接口类型中文名")
    interface_type_category = models.CharField(max_length=100, db_comment="服务接口所属大类")
    remark = models.CharField(max_length=255, db_comment="备注信息")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_service_interface_type'
        db_table_comment = '服务接口类型表'


class TtFeatureAnnotationeNewFields(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    annotation_id = models.BigIntegerField(blank=True, null=True, db_comment="标注数据ID")
    new_fields_ename = models.CharField(max_length=100, db_comment="标注数据新增字段的英文名拼接字符串")
    new_fields_cname = models.CharField(max_length=100, db_comment="标注数据新增字段的中文名拼接字符串")
    new_fields_type = models.CharField(max_length=100, db_comment="标注数据新增字段的字段类型拼接字符串")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_feature_annotatione_new_fields'
        db_table_comment = '标注数据新增字段表'


class TtPointType(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    point_ename = models.CharField(max_length=100, db_comment="点类型英文名称")
    point_cname = models.CharField(max_length=100, db_comment="点类型中文名称")
    remark = models.CharField(max_length=255, db_comment="备注信息")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_point_type'
        db_table_comment = '标注点类型字典表'


class TtIntelligenceSource(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    source_ename = models.CharField(max_length=100, db_comment="来源英文名称")
    source_cname = models.CharField(max_length=100, db_comment="来源中文名称")
    remark = models.CharField(max_length=255, db_comment="备注信息")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_intelligence_source'
        db_table_comment = '情报来源字典表'


class TtPoiData(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    lng = models.FloatField(blank=True, null=True, db_comment='经度')
    lat = models.FloatField(blank=True, null=True, db_comment='纬度')
    name = models.CharField(max_length=255, db_comment="名称")
    address = models.CharField(max_length=255, db_comment="地址")
    province = models.CharField(max_length=2550, db_comment="省份")
    city = models.CharField(max_length=2550, db_comment="地市")
    area = models.CharField(max_length=2550, db_comment="区县")
    poi_type = models.CharField(max_length=2550, db_comment="图层类型，医院，学校")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_poi_data'
        db_table_comment = 'POI数据表'


class TtKeyPointAreaData(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    lng = models.FloatField(blank=True, null=True, db_comment='经度')
    lat = models.FloatField(blank=True, null=True, db_comment='纬度')
    name = models.CharField(max_length=255, db_comment="名称")
    address = models.CharField(max_length=255, db_comment="地址")
    province = models.CharField(max_length=2550, db_comment="省份")
    city = models.CharField(max_length=2550, db_comment="地市")
    area = models.CharField(max_length=2550, db_comment="区县")
    poi_type = models.CharField(max_length=2550, db_comment="图层类型，医院，学校")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')
    doc_url = models.CharField(max_length=2550, db_comment="报告url地址")

    class Meta:
        managed = False
        db_table = 'tt_key_point_area'
        db_table_comment = '重点点位区域数据表'


class TtViewBookmarkData(models.Model):
    id = models.BigAutoField(primary_key=True, db_comment="id")
    lng = models.FloatField(blank=True, null=True, db_comment='经度')
    lat = models.FloatField(blank=True, null=True, db_comment='纬度')
    elv = models.FloatField(blank=True, null=True, db_comment='海拔')
    heading = models.FloatField(blank=True, null=True, db_comment='视图方向')
    pitch = models.FloatField(blank=True, null=True, db_comment='俯仰角')
    roll = models.FloatField(blank=True, null=True, db_comment='视点高度')
    view_bookmark_name = models.CharField(max_length=100, db_comment="备注信息")
    bingtuan_code_list = models.CharField(max_length=1000, db_comment="包含兵团编码信息")
    bingtuan_name_list = models.CharField(max_length=1000, db_comment="包含兵团名称信息")
    remark = models.CharField(max_length=2550, null=True, db_comment="备注信息")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_view_bookmark_data'
        db_table_comment = '方向书签数据表'

class TtDronAlertFileData(models.Model):
    id = models.BigAutoField(primary_key=True)
    alert_id =  models.BigIntegerField(blank=True, null=True, db_comment='告警id')
    create_user_id = models.BigIntegerField(blank=True, null=True)
    file_size = models.DecimalField(max_digits=24, decimal_places=0, blank=True, null=True)
    file_name = models.CharField(max_length=255, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    file_suffix = models.CharField(max_length=255, blank=True, null=True)
    path = models.CharField(max_length=512, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tt_dron_alert_file_data'
