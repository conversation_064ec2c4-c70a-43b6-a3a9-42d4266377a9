/**
 * Element Plus 组件主题样式
 * 直接覆盖 Element Plus 默认样式，符合项目青色主题
 */

// ===== 日期选择器样式 =====
.el-date-editor {
  --el-text-color-regular: #00fffe !important;
  --el-input-text-color: #00fffe !important;
  --el-input-border-color: rgba(0, 255, 254, 0.2) !important;
  --el-border-color: rgba(0, 255, 254, 0.2) !important;
  --el-input-border-color-hover: rgba(0, 255, 254, 0.5) !important;
  --el-bg-color: transparent !important;
  --el-border-radius-base: 4px !important;
  --el-input-font-size: 0.6rem !important;

  background-color: transparent !important;
  border: 1px solid rgba(0, 255, 254, 0.2) !important;
  transition: border-color 0.3s ease !important;
  font-size: 0.6rem !important;

  &:hover {
    --el-input-border-color: rgba(0, 255, 254, 0.5) !important;
    border-color: rgba(0, 255, 254, 0.5) !important;
  }

  &.is-focus {
    --el-input-border-color: #00fffe !important;
    border-color: #00fffe !important;
    box-shadow: 0 0 0 1px rgba(0, 255, 254, 0.2) !important;
  }

  .el-input__wrapper {
    background-color: transparent !important;
    border: none !important;
    box-shadow: none !important;

    &:hover {
      border-color: rgba(0, 255, 254, 0.5) !important;
    }
  }

  .el-input__inner {
    color: #00fffe !important;
    background-color: transparent !important;
    font-size: 0.6rem !important;
  }

  .el-range-separator {
    color: #00fffe !important;
    font-size: 0.6rem !important;
  }

  .el-range-input {
    color: #00fffe !important;
    background-color: transparent !important;
    font-size: 0.6rem !important;
  }

  .el-input__icon {
    color: #00fffe !important;
    font-size: 0.7rem !important;
  }

  .el-range__icon {
    color: #00fffe !important;
    font-size: 0.7rem !important;
  }
}

// 更具体的日期选择器 hover 样式
.el-date-editor.el-input__wrapper:hover,
.el-date-editor:hover .el-input__wrapper {
  border-color: rgba(0, 255, 254, 0.5) !important;
}

// 日期范围选择器特殊处理
.el-date-editor--daterange:hover,
.el-date-editor--datetimerange:hover {
  border-color: rgba(0, 255, 254, 0.5) !important;
}

// ===== 下拉菜单样式 =====
.el-dropdown-menu {
  font-size: 0.5rem !important;

  .el-dropdown-menu__item {
    font-size: 0.55rem !important;

    &:hover {
      background: rgba(0, 255, 254, 0.1) !important;
      color: #00fffe !important;
    }

    &:focus {
      background: rgba(0, 255, 254, 0.1) !important;
      color: #00fffe !important;
    }
  }
}

// ===== 下拉菜单触发器样式 =====
.el-dropdown {
  .dropdown-trigger,
  .el-dropdown-link {
    font-size: 0.55rem !important;

    // 确保图标也相应缩小
    .dropdown-icon,
    .el-icon {
      font-size: 0.5rem !important;
    }
  }
}

// 针对特定组件的下拉菜单触发器
.flight-task .dropdown-trigger {
  font-size: 0.55rem !important;
}

.warning-list-container .el-dropdown-link {
  font-size: 0.55rem !important;
}

// ===== 分页组件样式 =====
.el-pagination {
  // 确保分页组件不超出容器宽度
  max-width: 100% !important;
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-wrap: nowrap !important;

  .el-pagination__total {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.5rem !important;
    margin-right: 0.25rem !important;
    flex-shrink: 0 !important;
  }

  .el-pager {
    // 确保分页器能够自适应
    flex-wrap: nowrap !important;
    overflow: hidden !important;

    li {
      background: transparent !important;
      color: rgba(255, 255, 255, 0.8) !important;
      font-size: 0.5rem !important;
      border: 1px solid rgba(0, 255, 254, 0.2) !important;
      margin: 0 0.1rem !important;
      border-radius: 3px !important;
      min-width: 1.3rem !important;
      height: 1.3rem !important;
      line-height: 1.1rem !important;
      padding: 0 !important;
      flex-shrink: 0 !important;

      &:hover {
        color: #00fffe !important;
        border-color: #00fffe !important;
        background: rgba(0, 255, 254, 0.1) !important;
      }

      &.is-active {
        color: #ffffff !important;
        background: rgba(0, 255, 254, 0.3) !important;
        border-color: #00fffe !important;
      }
    }
  }

  .btn-prev,
  .btn-next {
    background: transparent !important;
    color: rgba(255, 255, 255, 0.8) !important;
    border: 1px solid rgba(0, 255, 254, 0.2) !important;
    border-radius: 3px !important;
    font-size: 0.5rem !important;
    min-width: 1.3rem !important;
    height: 1.3rem !important;
    padding: 0 0.2rem !important;
    flex-shrink: 0 !important;

    &:hover {
      color: #00fffe !important;
      border-color: #00fffe !important;
      background: rgba(0, 255, 254, 0.1) !important;
    }

    &:disabled {
      color: rgba(255, 255, 255, 0.3) !important;
      border-color: rgba(0, 255, 254, 0.1) !important;
      background: transparent !important;
    }
  }
}

// ===== 输入框样式 =====
.el-input {
  --el-input-text-color: #00fffe !important;
  --el-input-border-color: rgba(0, 255, 254, 0.2) !important;
  --el-input-border-color-hover: rgba(0, 255, 254, 0.5) !important;
  --el-input-focus-border-color: #00fffe !important;
  --el-input-bg-color: transparent !important;
  --el-input-placeholder-color: rgba(0, 255, 254, 0.5) !important;

  .el-input__wrapper {
    background-color: transparent !important;
    border: 1px solid rgba(0, 255, 254, 0.2) !important;
    transition: border-color 0.3s ease !important;
    box-shadow: none !important;

    &:hover {
      border-color: rgba(0, 255, 254, 0.5) !important;
      box-shadow: none !important;
    }

    &.is-focus {
      border-color: #00fffe !important;
      box-shadow: 0 0 0 1px rgba(0, 255, 254, 0.2) !important;
    }
  }

  .el-input__inner {
    color: #00fffe !important;
    background-color: transparent !important;
    font-size: 0.75rem !important;

    &::placeholder {
      color: rgba(0, 255, 254, 0.5) !important;
    }
  }

  // 错误状态样式
  &.input-error .el-input__wrapper {
    border-color: #f56c6c !important;
    box-shadow: 0 0 0 1px #f56c6c !important;
  }
}

// ===== 按钮样式 =====
.el-button {
  --el-button-font-size: 0.75rem !important;
  --el-button-border-radius: 4px !important;

  // 主要按钮样式
  &.el-button--primary {
    --el-button-text-color: #ffffff !important;
    --el-button-bg-color: #00fffe !important;
    --el-button-border-color: #00fffe !important;
    --el-button-hover-text-color: #ffffff !important;
    --el-button-hover-bg-color: rgba(0, 255, 254, 0.8) !important;
    --el-button-hover-border-color: rgba(0, 255, 254, 0.8) !important;
    --el-button-active-bg-color: rgba(0, 255, 254, 0.9) !important;
    --el-button-active-border-color: rgba(0, 255, 254, 0.9) !important;
    --el-button-disabled-text-color: rgba(255, 255, 255, 0.5) !important;
    --el-button-disabled-bg-color: rgba(0, 255, 254, 0.3) !important;
    --el-button-disabled-border-color: rgba(0, 255, 254, 0.3) !important;

    background-color: #00fffe !important;
    border-color: #00fffe !important;
    color: #ffffff !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;

    &:hover {
      background-color: rgba(0, 255, 254, 0.8) !important;
      border-color: rgba(0, 255, 254, 0.8) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(0, 255, 254, 0.3) !important;
    }

    &:active {
      background-color: rgba(0, 255, 254, 0.9) !important;
      border-color: rgba(0, 255, 254, 0.9) !important;
      transform: translateY(0) !important;
    }

    &:disabled {
      background-color: rgba(0, 255, 254, 0.3) !important;
      border-color: rgba(0, 255, 254, 0.3) !important;
      color: rgba(255, 255, 255, 0.5) !important;
      transform: none !important;
      box-shadow: none !important;
    }
  }

  // 小尺寸按钮
  &.el-button--small {
    --el-button-size: 24px !important;
    --el-button-font-size: 0.75rem !important;
    padding: 0.25rem 0.75rem !important;
  }

  // 默认按钮样式
  &.el-button--default {
    --el-button-text-color: rgba(255, 255, 255, 0.8) !important;
    --el-button-bg-color: rgba(10, 42, 74, 0.2) !important;
    --el-button-border-color: rgba(255, 255, 255, 0.2) !important;
    --el-button-hover-text-color: #ffffff !important;
    --el-button-hover-bg-color: rgba(10, 42, 74, 0.4) !important;
    --el-button-hover-border-color: rgba(255, 255, 255, 0.4) !important;
    --el-button-active-bg-color: rgba(10, 42, 74, 0.6) !important;
    --el-button-active-border-color: rgba(255, 255, 255, 0.6) !important;

    background-color: rgba(10, 42, 74, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    transition: all 0.3s ease !important;

    &:hover {
      background-color: rgba(10, 42, 74, 0.4) !important;
      border-color: rgba(255, 255, 255, 0.4) !important;
      color: #ffffff !important;
    }

    &:active {
      background-color: rgba(10, 42, 74, 0.6) !important;
      border-color: rgba(255, 255, 255, 0.6) !important;
    }
  }
}

// ===== MessageBox 对话框样式 =====
.el-message-box {
  --el-messagebox-width: 420px !important;
  --el-messagebox-border-radius: 8px !important;
  --el-messagebox-font-size: 0.75rem !important;
  --el-messagebox-content-font-size: 0.7rem !important;
  --el-messagebox-content-color: rgba(255, 255, 255, 0.8) !important;
  --el-messagebox-title-color: rgba(255, 255, 255, 0.9) !important;
  --el-messagebox-border-color: rgba(0, 255, 254, 0.3) !important;

  // 主容器样式 - 参考BasePannel设计
  background: rgba(0, 0, 30, 0.95) !important;
  border: 1px solid rgba(0, 255, 254, 0.3) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
  box-shadow:
    0 0 20px rgba(0, 255, 254, 0.2),
    0 8px 32px rgba(0, 0, 0, 0.4) !important;

  // 头部样式
  .el-message-box__header {
    padding: 1rem 1.5rem 0.5rem !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;

    .el-message-box__title {
      color: rgba(255, 255, 255, 0.9) !important;
      font-size: 0.8rem !important;
      font-weight: 600 !important;
    }

    .el-message-box__headerbtn {
      top: 1rem !important;
      right: 1rem !important;

      .el-message-box__close {
        color: rgba(255, 255, 255, 0.6) !important;
        font-size: 1rem !important;

        &:hover {
          color: #00fffe !important;
        }
      }
    }
  }

  // 内容区域样式
  .el-message-box__content {
    padding: 1rem 1.5rem !important;
    color: rgba(255, 255, 255, 0.8) !important;

    .el-message-box__container {
      .el-message-box__message {
        color: rgba(255, 255, 255, 0.8) !important;
        font-size: 0.7rem !important;
        line-height: 1.5 !important;
        margin-left: 0 !important;
      }

      .el-message-box__status {
        &.el-icon {
          font-size: 1.2rem !important;

          // 不同状态图标颜色
          &.el-message-box-icon--info {
            color: #00fffe !important;
          }

          &.el-message-box-icon--success {
            color: #4caf50 !important;
          }

          &.el-message-box-icon--warning {
            color: #ff9800 !important;
          }

          &.el-message-box-icon--error {
            color: #f44336 !important;
          }
        }
      }
    }
  }

  // 按钮区域样式
  .el-message-box__btns {
    padding: 0.5rem 1.5rem 1rem !important;
    text-align: right !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;

    .el-button {
      margin-left: 0.5rem !important;
      font-size: 0.7rem !important;
      padding: 0.4rem 1rem !important;
      border-radius: 4px !important;

      // 主要按钮（确认）
      &.el-button--primary {
        background: rgba(0, 255, 254, 0.9) !important;
        border-color: #00fffe !important;
        color: #ffffff !important;

        &:hover {
          background: #00fffe !important;
          box-shadow: 0 0 8px rgba(0, 255, 254, 0.4) !important;
        }

        &:active {
          background: rgba(0, 255, 254, 0.8) !important;
        }
      }

      // 默认按钮（取消）
      &.el-button--default {
        background: rgba(10, 42, 74, 0.3) !important;
        border-color: rgba(255, 255, 255, 0.2) !important;
        color: rgba(255, 255, 255, 0.8) !important;

        &:hover {
          background: rgba(10, 42, 74, 0.5) !important;
          border-color: rgba(255, 255, 255, 0.4) !important;
          color: #ffffff !important;
        }

        &:active {
          background: rgba(10, 42, 74, 0.7) !important;
        }
      }
    }
  }
}

// ===== MessageBox 遮罩层样式 =====
.el-overlay {
  background-color: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(4px) !important;
}
