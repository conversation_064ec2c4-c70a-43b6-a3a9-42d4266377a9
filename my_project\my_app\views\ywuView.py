import logging
import time
import datetime
from django.db import connection
from django.views.decorators.csrf import csrf_exempt
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from vgis_log.logTools import Lo<PERSON>Helper
from vgis_utils.vgis_http.httpTools import HttpHelper
from my_app.models import SysLog
from my_app.models import TtGlwZhbzfx,RencheDict,TtGlwJjdy,TtGlwZhfx
import json
from my_app.models import TtUploadFileData
from django.http import HttpResponse
from wsgiref.util import FileWrapper
from my_app.utils.businessUtility import businessHelper
from django.http import StreamingHttpResponse
from my_app.utils.commonUtility import CommonHelper
from my_app.utils.snowflake_id_util import SnowflakeIDUtil
from my_app.views.response.baseRespone import Result
from my_project import settings
from my_project.token import ExpiringTokenAuthentication
from my_app.manage.ywManager import YwManager

from io import BytesIO
logger = logging.getLogger('django')
import platform
import os
platformType = platform.system().lower()

class YewuView(viewsets.ModelViewSet):

    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)

    @action(detail=False, methods=['POST'])
    def create_zhbzfx(self, request, *args, **kwargs):
        """
        新增综合保障分析记录的接口。
        此接口会处理请求数据，添加创建用户、创建时间等信息，检查名称是否重复，
        若不重复则创建新的综合保障分析记录。

        :param request: 请求对象，包含新增记录所需的数据
        :param args: 可变参数
        :param kwargs: 关键字参数
        :return: 新增操作的结果响应
        """
        # 定义 API 路径
        api_path = "/yw/create_zhbzfx/"
        try:
            # 定义功能标题
            function_title = "新增综合保障分析"
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)

            # 添加创建用户 ID 到请求数据
            request.data["create_user_id"] = request.auth.user_id
            # 添加创建用户到请求数据
            request.data["create_user"] = request.auth.user
            # 添加创建时间到请求数据，格式为 'YYYY-MM-DD HH:MM:SS'
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 如果请求数据中的 'rwtu' 不为空，将其转换为 JSON 字符串
            if request.data["rwtu"] is not None:
                request.data["rwtu"] = json.dumps(request.data["rwtu"])
            # 将请求数据中的 'baogao' 转换为 JSON 字符串
            request.data["baogao"] = json.dumps(request.data["baogao"])

            # 获取请求数据中的名称，如果不存在则为 None
            mc = request.data["mc"] if "mc" in request.data else None

            # 检查名称是否已存在
            if len(TtGlwZhbzfx.objects.filter(mc=mc)) > 0:
                # 若名称已存在，构造错误信息
                error_info = "名称:{}已存在，请换个名称".format(mc)
                # 记录异常日志信息
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                           request.auth.user, request,
                                                           function_title, error_info, None)
                # 返回失败响应
                return Result.fail(error_info, error_info)

            # 为新增记录生成唯一 ID
            request.data["id"] = SnowflakeIDUtil.snowflakeId()

            # 创建新的综合保障分析记录
            TtGlwZhbzfx.objects.create(**request.data)
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            # 返回成功响应
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            # 若出现异常，构造错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                       request, function_title, None, exp)
            # 返回失败响应
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def up_zhbzfx(self, request, *args, **kwargs):
        """
        更新综合保障分析记录的接口。
        此接口会处理请求数据，添加更新用户、更新时间等信息，
        然后根据记录 ID 查找并更新相应的综合保障分析记录。

        :param request: 请求对象，包含更新记录所需的数据
        :param args: 可变参数
        :param kwargs: 关键字参数
        :return: 更新操作的结果响应
        """
        # 定义 API 路径
        api_path = "/yw/create_zhbzfx/"
        try:
            # 定义功能标题
            function_title = "更新综合保障分析"
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)

            # 添加更新用户 ID 到请求数据
            request.data["update_user_id"] = request.auth.user_id
            # 添加更新时间到请求数据，格式为 'YYYY-MM-DD HH:MM:SS'
            request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 处理更新用户信息
            if type(request.auth.user) != "str":
                request.data["update_user"] = request.auth.user.username
            else:
                request.data["update_user"] = request.auth.user

            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)

            # 从请求数据中获取记录 ID
            id = request.data["id"]

            # 根据 ID 查找综合保障分析记录
            tis = TtGlwZhbzfx.objects.filter(id=id)
            if len(tis) > 0:
                # 将请求数据中的 'baogao' 转换为 JSON 字符串
                request.data["baogao"] = json.dumps(request.data["baogao"])
                # 更新记录信息
                tis.update(**request.data)
                # 返回成功响应
                return Result.sucess("{}成功".format(function_title))
            else:
                return Result.fail("{}失败，可能原因是{}".format(function_title, "未找到该记录"),None)
        except Exception as exp:
            # 若出现异常，构造错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                       request, function_title, None, exp)
            # 返回失败响应
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def del_zhbzfx(self, request, *args, **kwargs):
        """
        删除综合保障分析记录的接口。
        此接口会根据请求数据中的记录 ID 查找相应记录，若存在则删除该记录。

        :param request: 请求对象，包含要删除记录的 ID
        :param args: 可变参数
        :param kwargs: 关键字参数
        :return: 删除操作的结果响应
        """
        try:
            # 定义功能标题
            function_title = "删除综合保障分析"
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)
            # 从请求数据中获取记录 ID
            id = request.data["id"]
            # 获取请求路径作为 API 路径
            api_path = request.path
            # 根据 ID 查找综合保障分析记录
            Tto = TtGlwZhbzfx.objects.filter(id=id)
            if len(Tto) > 0:
                # 若记录存在，更新功能标题为包含记录名称
                function_title = "删除综合保障分析{}".format(Tto[0].mc)
                # 删除记录
                Tto.delete()
                # 构造成功消息
                msg = "{}成功".format(function_title)
            else:
                # 若记录不存在，构造失败消息
                msg = "删除{}失败，未找到数据".format("删除综合保障分析")
                return Result.fail(msg, msg)
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            # 返回成功响应
            return Result.sucess(msg, msg)
        except Exception as exp:
            # 若出现异常，记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            # 构造错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 返回失败响应
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def get_zhbzfx(self, request):
        """
        查询综合保障分析记录的接口。
        此接口会调用 YwManager 类的方法来查询记录，并返回查询结果。

        :param request: 请求对象，包含查询所需的条件
        :return: 查询操作的结果响应
        """
        # 获取请求路径作为 API 路径
        api_path = request.path
        # 定义功能标题
        function_title = "查询综合保障分析"
        try:
            # 创建 YwManager 类的实例
            ywManager = YwManager(connection)
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)

            # 调用 YwManager 类的方法进行查询
            static_value = ywManager.get_zhbzfx(request)
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            # 构造成功消息
            msg = "{}成功".format(function_title)
            # 返回成功响应
            return Result.sucess(msg, static_value)
        except Exception as exp:
            # 若出现异常，记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            # 构造失败消息
            msg = "{}失败".format(function_title)
            # 返回失败响应
            return Result.fail(msg, str(exp))

    @action(detail=False, methods=['POST'])
    def export_zhbzfx(self, request):
        """
        导出综合保障分析记录的接口。
        此接口会创建文档，将其保存到内存中，并以流的形式返回给客户端。

        :param request: 请求对象，包含导出所需的信息
        :return: 导出操作的响应，包含导出的文件
        """
        # 获取请求路径作为 API 路径
        api_path = request.path
        # 定义功能标题
        function_title = "查询综合保障分析"
        try:
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)

            # 创建文档
            file = create_doc(request)
            # 创建内存中的字节流
            output = BytesIO()
            # 将文档保存到字节流中
            file.save(output)
            # 将文件指针移动到字节流开头
            output.seek(0)
            # 创建流式 HTTP 响应，指定内容类型为二进制流
            response = StreamingHttpResponse(FileWrapper(output), content_type='application/octet-stream')
            # 设置响应头，指定文件名
            response['Content-Disposition'] = 'attachment; filename="{}"'.format("filename")
            # 返回响应
            return response
        except Exception as exp:
            # 若出现异常，记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            # 构造失败消息
            msg = "{}失败".format(function_title)
            # 返回失败响应
            return Result.fail(msg, str(exp))

class kongdiView(viewsets.ModelViewSet):

    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)

    @action(detail=False, methods=['POST'])
    def serve_docx(self, request, *args, **kwargs):
        try:
            # 替换为你的 DOCX 文件的实际路径
            file_path = 'D:/example.docx'
            if os.path.exists(file_path):
                with open(file_path, 'rb') as file:
                    response = HttpResponse(file.read(),
                                            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document')
                    response['Content-Disposition'] = f'attachment; filename={os.path.basename(file_path)}'
                    return response
            else:
                return HttpResponse('文件未找到', status=404)
        except Exception as e:
            return HttpResponse(f'发生错误: {str(e)}', status=500)

    @action(detail=False, methods=['POST'])
    def create_renche(self, request, *args, **kwargs):
        """
        新增人车记录的接口。
        该接口会处理请求数据，添加创建用户、创建时间等信息，
        检查人车名称是否已存在，若不存在则创建新的人车记录。

        :param request: 请求对象，包含新增人车记录所需的数据
        :param args: 可变参数
        :param kwargs: 关键字参数
        :return: 新增操作的结果响应
        """
        # 定义 API 路径
        api_path = "/yw/create_renche/"
        try:
            # 定义功能标题
            function_title = "新增人车"
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)

            # 添加创建用户 ID 到请求数据
            request.data["create_user_id"] = request.auth.user_id
            # 添加创建用户到请求数据
            request.data["create_user"] = request.auth.user
            # 添加创建时间到请求数据，格式为 'YYYY-MM-DD HH:MM:SS'
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 获取请求数据中的人车名称，如果不存在则为 None
            mc = request.data["mc"] if "mc" in request.data else None

            # 检查该名称的人车记录是否已存在
            if len(RencheDict.objects.filter(mc=mc)) > 0:
                # 若名称已存在，构造错误信息
                error_info = "名称:{}已存在，请换个名称".format(mc)
                # 记录异常日志信息
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                           request.auth.user, request,
                                                           function_title, error_info, None)
                # 返回失败响应
                return Result.fail(error_info, error_info)

            # 为新增记录生成唯一 ID
            request.data["id"] = SnowflakeIDUtil.snowflakeId()

            # 创建新的人车记录
            RencheDict.objects.create(**request.data)
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            # 返回成功响应
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            # 若出现异常，构造错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                       request, function_title, None, exp)
            # 返回失败响应
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def up_renche(self, request, *args, **kwargs):
        """
        更新人车记录的接口。
        该接口会处理请求数据，添加更新用户、更新时间等信息，
        根据记录 ID 查找并更新相应的人车记录。

        :param request: 请求对象，包含更新人车记录所需的数据
        :param args: 可变参数
        :param kwargs: 关键字参数
        :return: 更新操作的结果响应
        """
        # 定义 API 路径
        api_path = "/yw/create_renche/"
        try:
            # 定义功能标题
            function_title = "更新人车"
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)

            # 添加更新用户 ID 到请求数据
            request.data["update_user_id"] = request.auth.user_id
            # 添加更新时间到请求数据，格式为 'YYYY-MM-DD HH:MM:SS'
            request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 处理更新用户信息
            if type(request.auth.user) != "str":
                request.data["update_user"] = request.auth.user.username
            else:
                request.data["update_user"] = request.auth.user

            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)

            # 从请求数据中获取记录 ID
            id = request.data["id"]

            # 根据 ID 查找人车记录
            tis = RencheDict.objects.filter(id=id)
            if len(tis) > 0:
                # 更新人车记录信息
                tis.update(**request.data)
            # 返回成功响应
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            # 若出现异常，构造错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                       request, function_title, None, exp)
            # 返回失败响应
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def del_renche(self, request, *args, **kwargs):
        """
        删除人车记录的接口。
        该接口会根据请求数据中的记录 ID 查找相应的人车记录，
        若存在则删除该记录。

        :param request: 请求对象，包含要删除的人车记录的 ID
        :param args: 可变参数
        :param kwargs: 关键字参数
        :return: 删除操作的结果响应
        """
        try:
            # 定义功能标题
            function_title = "删除人车"
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)

            # 从请求数据中获取记录 ID
            id = request.data["id"]
            # 获取请求路径作为 API 路径
            api_path = request.path

            # 根据 ID 查找人车记录
            Tto = RencheDict.objects.filter(id=id)
            if len(Tto) > 0:
                # 若记录存在，更新功能标题为包含记录名称
                function_title = "删除人车{}".format(Tto[0].mc)
                # 删除人车记录
                Tto.delete()
                # 构造成功消息
                msg = "{}成功".format(function_title)
            else:
                # 若记录不存在，构造失败消息
                msg = "删除{}失败，未找到数据".format("删除人车")

            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            # 返回成功响应
            return Result.sucess(msg, msg)
        except Exception as exp:
            # 若出现异常，记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            # 构造错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 返回失败响应
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def get_renche(self, request):
        """
        查询人车记录的接口。
        该接口会调用 YwManager 类的方法进行人车记录的查询操作。

        :param request: 请求对象，包含查询人车记录所需的条件
        :return: 查询操作的结果响应
        """
        # 获取请求路径作为 API 路径
        api_path = request.path
        # 定义功能标题
        function_title = "查询人车"
        try:
            # 创建 YwManager 类的实例
            ywManager = YwManager(connection)
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)

            # 调用 YwManager 类的方法进行人车记录查询
            static_value = ywManager.get_renche(request)
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            # 构造成功消息
            msg = "{}成功".format(function_title)
            # 返回成功响应
            return Result.sucess(msg, static_value)
        except Exception as exp:
            # 若出现异常，记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            # 构造失败消息
            msg = "{}失败".format(function_title)
            # 返回失败响应
            return Result.fail(msg, str(exp))

    @action(detail=False, methods=['GET'])
    def get_all_renche(self, request):
        """
        查询人车记录的接口。
        该接口会调用 YwManager 类的方法进行人车记录的查询操作。

        :param request: 请求对象，包含查询人车记录所需的条件
        :return: 查询操作的结果响应
        """
        # 获取请求路径作为 API 路径
        api_path = request.path
        # 定义功能标题
        function_title = "查询全部车辆长宽高载重"
        try:
            # 创建 YwManager 类的实例
            ywManager = YwManager(connection)
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)

            # 调用 YwManager 类的方法进行人车记录查询
            static_value = ywManager.get_all_renche(request)
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            # 构造成功消息
            msg = "{}成功".format(function_title)
            # 返回成功响应
            return Result.sucess(msg, static_value)
        except Exception as exp:
            # 若出现异常，记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            # 构造失败消息
            msg = "{}失败".format(function_title)
            # 返回失败响应
            return Result.fail(msg, str(exp))
    @action(detail=False, methods=['POST'])
    def create_jjdy(self, request, *args, **kwargs):
        """
        新增集结地域记录的接口。
        该接口会处理请求数据，添加创建用户、创建时间等信息，
        检查集结地域名称是否已存在，若不存在则创建新的集结地域记录。

        :param request: 请求对象，包含新增集结地域记录所需的数据
        :param args: 可变参数
        :param kwargs: 关键字参数
        :return: 新增操作的结果响应
        """
        # 定义 API 路径
        api_path = "/yw/create_jjdy/"
        try:
            # 定义功能标题
            function_title = "新增集结地域"
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)

            # 添加创建用户 ID 到请求数据
            request.data["create_user_id"] = request.auth.user_id
            # 添加创建用户到请求数据
            request.data["create_user"] = request.auth.user
            # 添加创建时间到请求数据，格式为 'YYYY-MM-DD HH:MM:SS'
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 若请求数据中的 'rwtu' 不为空，将其转换为 JSON 字符串
            if request.data["rwtu"] is not None:
                request.data["rwtu"] = json.dumps(request.data["rwtu"])
            # 将请求数据中的 'baogao' 转换为 JSON 字符串
            request.data["baogao"] = json.dumps(request.data["baogao"])
            # 注释掉的代码，原本可能用于处理 'lists' 字段，将其转换为 JSON 字符串
            # request.data["lists"] = json.dumps(request.data["lists"])

            # 获取请求数据中的集结地域名称，如果不存在则为 None
            mc = request.data["mc"] if "mc" in request.data else None

            # 检查该名称的集结地域记录是否已存在
            if len(TtGlwJjdy.objects.filter(mc=mc)) > 0:
                # 若名称已存在，构造错误信息
                error_info = "名称:{}已存在，请换个名称".format(mc)
                # 记录异常日志信息
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                           request.auth.user, request,
                                                           function_title, error_info, None)
                # 返回失败响应
                return Result.fail(error_info, error_info)

            # 为新增记录生成唯一 ID
            request.data["id"] = SnowflakeIDUtil.snowflakeId()

            # 创建新的集结地域记录
            TtGlwJjdy.objects.create(**request.data)
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            # 返回成功响应
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            # 若出现异常，构造错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                       request, function_title, None, exp)
            # 返回失败响应
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def up_jjdy(self, request, *args, **kwargs):
        api_path = "/yw/up_jjdy/"
        try:
            function_title = "更新集结地域"
            start = LoggerHelper.set_start_log_info(logger)

            request.data["update_user_id"] = request.auth.user_id
            request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            if type(request.auth.user) != "str":
                request.data["update_user"] = request.auth.user.username
            else:
                request.data["update_user"] = request.auth.user
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)

            id = request.data["id"]

            tis = TtGlwJjdy.objects.filter(id=id)
            if len(tis) > 0:
                request.data["baogao"] = json.dumps(request.data["baogao"])
                tis.update(**request.data)
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                       request, function_title, None, exp)
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def del_jjdy(self, request, *args, **kwargs):
        try:
            function_title = "删除集结地域"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            id = request.data["id"]
            api_path = request.path
            Tto = TtGlwJjdy.objects.filter(id=id)
            if len(Tto) > 0:

                function_title = "删除集结地域{}".format(Tto[0].mc)
                Tto.delete()
                msg = "{}成功".format(function_title)
            else:
                msg = "删除{}失败，未找到数据".format("删除集结地域")
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            return Result.sucess(msg, msg)

        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def get_jjdy(self, request):
        api_path = request.path
        function_title = "查询集结地域"
        try:
            ywManager = YwManager(connection)
            start = LoggerHelper.set_start_log_info(logger)

            static_value = ywManager.get_jjdy(request)
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))
    @action(detail=False, methods=['POST'])
    def export_jjdy(self, request):
        api_path = request.path
        function_title = "查询集结地域"
        try:

            start = LoggerHelper.set_start_log_info(logger)

            file = create_doc_jjdy(request)
            output = BytesIO()
            file.save(output)
            output.seek(0)
            response = StreamingHttpResponse(FileWrapper(output), content_type='application/octet-stream')
            response['Content-Disposition'] = 'attachment; filename="{}"'.format("filename")

            return response
            # msg = "{}成功".format(function_title)
            # return Result.sucess(msg, msg)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))

class zongheView(viewsets.ModelViewSet):

    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)

    @action(detail=False, methods=['POST'])
    def daochu_zhfx(self, request, *args, **kwargs):
        api_path = "/yw/daochu_zhfx/"
        try:
            function_title = "导出综合分析"
            start = LoggerHelper.set_start_log_info(logger)

            request.data["create_user_id"] = request.auth.user_id
            request.data["create_user"] = request.auth.user
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            request.data["baogao"] = None
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            # request.data = None
            request.data["baogao"] = None
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))

            # LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
            #                                            request, function_title, None, exp)
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def create_zhfx(self, request, *args, **kwargs):
        api_path = "/yw/create_zhfx/"
        try:
            function_title = "新增综合分析"
            start = LoggerHelper.set_start_log_info(logger)

            request.data["create_user_id"] = request.auth.user_id
            request.data["create_user"] = request.auth.user
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # if request.data["rwtu"] is not None:
            #     request.data["rwtu"] = json.dumps(request.data["rwtu"])
            # request.data["baogao"] = json.dumps(request.data["baogao"])
            # request.data["lists"] = json.dumps(request.data["lists"])
            mc = request.data["mc"] if "mc" in request.data else None

            if len(TtGlwZhfx.objects.filter(mc=mc)) > 0:
                error_info = "名称:{}已存在，请换个名称".format(mc)
                request.data["baogao"] = None
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                           request.auth.user, request,
                                                           function_title, error_info, None)

                return Result.fail(error_info, error_info)
            request.data["id"] = SnowflakeIDUtil.snowflakeId()

            TtGlwZhfx.objects.create(**request.data)
            request.data["baogao"] = None
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            # request.data = None
            request.data["baogao"] = None
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))

            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                       request, function_title, None, exp)
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def up_zhfx(self, request, *args, **kwargs):
        api_path = "/yw/up_zhfx/"
        try:
            function_title = "更新综合分析"
            start = LoggerHelper.set_start_log_info(logger)

            request.data["update_user_id"] = request.auth.user_id
            request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            if type(request.auth.user) != "str":
                request.data["update_user"] = request.auth.user.username
            else:
                request.data["update_user"] = request.auth.user
            request.data["baogao"] = None
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)

            id = request.data["id"]

            tis = TtGlwZhfx.objects.filter(id=id)
            if len(tis) > 0:
                request.data["baogao"] = json.dumps(request.data["baogao"])
                tis.update(**request.data)
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            request.data["baogao"] = None
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                       request, function_title, None, exp)
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def del_zhfx(self, request, *args, **kwargs):
        try:
            function_title = "删除综合分析"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            id = request.data["id"]
            api_path = request.path
            Tto = TtGlwZhfx.objects.filter(id=id)
            if len(Tto) > 0:

                function_title = "删除综合分析{}".format(Tto[0].mc)
                Tto.delete()
                msg = "{}成功".format(function_title)
            else:
                msg = "删除{}失败，未找到数据".format("删除综合分析")
            request.data["baogao"] = None
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            return Result.sucess(msg, msg)

        except Exception as exp:
            request.data["baogao"] = None
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def get_zhfx(self, request):
        api_path = request.path
        function_title = "查询综合分析"
        try:
            ywManager = YwManager(connection)
            start = LoggerHelper.set_start_log_info(logger)

            static_value = ywManager.get_zhfx(request)
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request, function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))
    @action(detail=False, methods=['POST'])
    def export_zhfx(self, request):
        api_path = request.path
        function_title = "查询综合分析"
        try:

            start = LoggerHelper.set_start_log_info(logger)

            file = create_doc_jjdy(request)
            output = BytesIO()
            file.save(output)
            output.seek(0)
            response = StreamingHttpResponse(FileWrapper(output), content_type='application/octet-stream')
            response['Content-Disposition'] = 'attachment; filename="{}"'.format("filename")

            return response
            # msg = "{}成功".format(function_title)
            # return Result.sucess(msg, msg)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))