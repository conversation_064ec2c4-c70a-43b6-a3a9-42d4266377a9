/**
 * Mars3D平台插件,结合heatmap可视化功能插件  mars3d-heatmap
 *
 * 版本信息：v3.5.19
 * 编译日期：2024-10-29 13:42
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：维璟（北京）科技有限公司 ，2023-06-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d')), (window.h337 || require('@mars3d/heatmap.js'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d', '@mars3d/heatmap.js'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-heatmap"] = {}, global.mars3d, global.h337));
})(this, (function (exports, mars3d, h337) { 
'use strict';const _0x172fe9=_0x3421;(function(_0x2990bd,_0x252adf){const _0x45d147={_0x44c75e:0xb1,_0x33c96f:0x9b,_0x166408:0xb4,_0x97c99d:0x85,_0x51dfba:0x9e},_0x5612e7=_0x3421,_0x44664b=_0x2990bd();while(!![]){try{const _0x3ee497=parseInt(_0x5612e7(0xcd))/0x1*(-parseInt(_0x5612e7(_0x45d147._0x44c75e))/0x2)+-parseInt(_0x5612e7(0xbb))/0x3*(parseInt(_0x5612e7(_0x45d147._0x33c96f))/0x4)+-parseInt(_0x5612e7(_0x45d147._0x166408))/0x5+-parseInt(_0x5612e7(0xa9))/0x6*(-parseInt(_0x5612e7(_0x45d147._0x97c99d))/0x7)+parseInt(_0x5612e7(0xba))/0x8*(parseInt(_0x5612e7(0x93))/0x9)+-parseInt(_0x5612e7(_0x45d147._0x51dfba))/0xa*(parseInt(_0x5612e7(0xd1))/0xb)+-parseInt(_0x5612e7(0x9c))/0xc*(-parseInt(_0x5612e7(0x8b))/0xd);if(_0x3ee497===_0x252adf)break;else _0x44664b['push'](_0x44664b['shift']());}catch(_0x4eb12d){_0x44664b['push'](_0x44664b['shift']());}}}(_0x1073,0xd3b6c));function _0x3421(_0x36c6ca,_0xcd860){const _0x107350=_0x1073();return _0x3421=function(_0x3421c4,_0x3f52c2){_0x3421c4=_0x3421c4-0x7d;let _0x374645=_0x107350[_0x3421c4];return _0x374645;},_0x3421(_0x36c6ca,_0xcd860);}function _interopNamespace(_0x1c1e12){const _0x599e11={_0x323f89:0xbc,_0x50e143:0xae};if(_0x1c1e12&&_0x1c1e12['__esModule'])return _0x1c1e12;var _0x585a84=Object['create'](null);return _0x1c1e12&&Object['keys'](_0x1c1e12)['forEach'](function(_0x474e58){const _0x904c2a=_0x3421;if(_0x474e58!=='default'){var _0x47a189=Object[_0x904c2a(_0x599e11._0x323f89)](_0x1c1e12,_0x474e58);Object[_0x904c2a(_0x599e11._0x50e143)](_0x585a84,_0x474e58,_0x47a189['get']?_0x47a189:{'enumerable':!![],'get':function(){return _0x1c1e12[_0x474e58];}});}}),_0x585a84['default']=_0x1c1e12,_0x585a84;}var mars3d__namespace=_interopNamespace(mars3d),h337__namespace=_interopNamespace(h337),HeatMaterial='uniform\x20sampler2D\x20image;\x0a\x0aczm_material\x20czm_getMaterial(czm_materialInput\x20materialInput)\x20{\x0a\x20\x20czm_material\x20material\x20=\x20czm_getDefaultMaterial(materialInput);\x0a\x20\x20vec2\x20st\x20=\x20materialInput.st;\x0a\x20\x20vec4\x20colorImage\x20=\x20texture(image,\x20st);\x0a\x20\x20if(colorImage.rgb\x20==\x20vec3(1.0)\x20||\x20colorImage.rgb\x20==\x20vec3(0.0))\x20{\x0a\x20\x20\x20\x20discard;\x0a\x20\x20}\x0a\x20\x20material.diffuse\x20=\x20colorImage.rgb;\x0a\x20\x20material.alpha\x20=\x20colorImage.a;\x0a\x20\x20return\x20material;\x0a}\x0a';if(!h337__namespace['create'])throw new Error('请引入\x20heatmap.js\x20库\x20');const Cesium=mars3d__namespace[_0x172fe9(0xa7)],BaseLayer=mars3d__namespace['layer']['BaseLayer'],DEF_HEATSTYLE={'maxOpacity':0.8,'minOpacity':0.1,'blur':0.85,'radius':0x19,'gradient':{0.4:'blue',0.6:'green',0.8:_0x172fe9(0xd5),0.9:'red'}},DEF_STYLE={'arcRadiusScale':1.5,'arcBlurScale':1.5,'vertexFormat':Cesium['EllipsoidSurfaceAppearance']['VERTEX_FORMAT']};class HeatLayer extends BaseLayer{constructor(_0x322d30={}){const _0x38aba1={_0x50a519:0xcf,_0x1f4d2d:0x90,_0x506d08:0xb2,_0x3c87db:0x84},_0x44f63e=_0x172fe9;super(_0x322d30),this['options']['canvasWidth']=this['options'][_0x44f63e(_0x38aba1._0x50a519)]??document['body']['clientWidth'],this['options'][_0x44f63e(0xcf)]=Math['min'](this['options'][_0x44f63e(0xcf)],0x1388),this['options'][_0x44f63e(_0x38aba1._0x1f4d2d)]=this['options']['redrawRatio']||0x1,this[_0x44f63e(0xb2)]['heatStyle']={...DEF_HEATSTYLE,...this['options']['heatStyle']},this[_0x44f63e(_0x38aba1._0x506d08)][_0x44f63e(_0x38aba1._0x3c87db)]={...DEF_STYLE,...this[_0x44f63e(0xb2)]['style']};}get['layer'](){return this['_layer'];}get[_0x172fe9(0xa8)](){return this['options']['heatStyle'];}set['heatStyle'](_0x24fd08){const _0x124df0={_0x375166:0xb2,_0x5ca7ff:0xa8,_0x151c3b:0xab},_0x22f28e=_0x172fe9;this['options']['heatStyle']=mars3d__namespace[_0x22f28e(0xa2)]['merge'](this[_0x22f28e(_0x124df0._0x375166)][_0x22f28e(_0x124df0._0x5ca7ff)],_0x24fd08);if(this['_heat']){this['_heat']['configure'](this['options'][_0x22f28e(0xa8)]);const _0x5a18ee=this['_heat']['getDataURL']();this[_0x22f28e(_0x124df0._0x151c3b)](_0x5a18ee);}}get['style'](){return this['options']['style'];}set['style'](_0x29355e){const _0xb934a6={_0xc202a3:0xb2},_0x2aacf5=_0x172fe9;this[_0x2aacf5(_0xb934a6._0xc202a3)]['style']=mars3d__namespace[_0x2aacf5(0xa2)]['merge'](this['options']['style'],_0x29355e);}get['positions'](){return this['_positions'];}set['positions'](_0x48cf93){this['setPositions'](_0x48cf93);}get['coordinates'](){const _0xcf8fcb=[];return this['points']['forEach'](_0x26b443=>{_0xcf8fcb['push'](_0x26b443['toArray']());}),_0xcf8fcb;}get['rectangle'](){return this['_rectangle'];}[_0x172fe9(0x7d)](_0x3841cf,_0x35e013){const _0x5dbdff=_0x172fe9;_0x3841cf['positions']&&(this[_0x5dbdff(0xc2)]=_0x3841cf[_0x5dbdff(0xc2)]);}['_mountedHook'](){const _0x457902={_0xacfc56:0xbd},_0x506783=_0x172fe9;this['style']['type']===_0x506783(_0x457902._0xacfc56)?this['_layer']=new mars3d__namespace['layer']['ImageLayer']({'crs':'EPSG:3857','private':!![]}):this['_layer']=new mars3d__namespace['layer']['GraphicLayer']({'private':!![]});}['_addedHook'](){const _0x5a736f={_0x4b8956:0xc3},_0x1d5d67=_0x172fe9;this['_map']['addLayer'](this['_layer']),this['_container']=mars3d__namespace['DomUtil']['create']('div',_0x1d5d67(_0x5a736f._0x4b8956),this['_map']['container']),this['options']['positions']&&(this['positions']=this['options']['positions']),this['options']['flyTo']&&this['flyToByAnimationEnd']();}['_removedHook'](){const _0x2a7e15={_0x546469:0xc0,_0x3b0432:0xc5},_0x16ebfc=_0x172fe9;this['_container']&&(mars3d__namespace['DomUtil']['remove'](this['_container']),delete this['_container']),this['clear'](),this[_0x16ebfc(0x98)][_0x16ebfc(_0x2a7e15._0x546469)](this[_0x16ebfc(_0x2a7e15._0x3b0432)]);}['addPosition'](_0x26b2ac){const _0x462744={_0x1607b9:0x92},_0x2046be=_0x172fe9;this[_0x2046be(_0x462744._0x1607b9)]=this['_positions']||[],this[_0x2046be(0x92)]['push'](_0x26b2ac),this['_updatePositionsHook']();}['setPositions'](_0x20dd45){const _0x180e56=_0x172fe9;this['_positions']=_0x20dd45,this[_0x180e56(0xc8)]();}[_0x172fe9(0xa3)](){const _0x408f40={_0x76dc8a:0x8f},_0x521dc5=_0x172fe9;this['_graphic']&&(this['_layer'][_0x521dc5(_0x408f40._0x76dc8a)](this['_graphic'],!![]),delete this['_graphic']),this['_graphic2']&&(this['_layer'][_0x521dc5(0x8f)](this['_graphic2'],!![]),delete this['_graphic2']);}[_0x172fe9(0xc8)](){const _0x2b36db=_0x172fe9;if(!this['show']||!this['_map']||!this[_0x2b36db(0xc2)]||this[_0x2b36db(0xc2)][_0x2b36db(0x9f)]===0x0)return this;const _0x3ae880=this['_getHeatCanvas']();return this['_updateGraphic'](_0x3ae880),this;}['getRectangle'](_0x4c58f0){const _0x31f635={_0x53acda:0xd3},_0xf5ccc7=_0x172fe9;return _0x4c58f0!==null&&_0x4c58f0!==void 0x0&&_0x4c58f0['isFormat']&&this['_rectangle']?mars3d__namespace[_0xf5ccc7(_0x31f635._0x53acda)]['formatRectangle'](this['_rectangle']):this['_rectangle'];}['_getBounds'](_0x391acf){const _0xfb7185={_0x515fbe:0xb7,_0x302812:0xb2,_0x1d2494:0xd2,_0x109412:0xaa,_0x129700:0xb7,_0x12fb5f:0xa4,_0x1083f4:0x95},_0x4c8b54={_0x379d23:0x82,_0x2ebd37:0xbf},_0x1a0713=_0x172fe9;let _0x3d6bbe,_0x18d2df,_0x45b930,_0x3f5540;this[_0x1a0713(0xb2)]['rectangle']?(_0x3d6bbe=this['options'][_0x1a0713(_0xfb7185._0x515fbe)]['xmin'],_0x18d2df=this['options']['rectangle']['xmax'],_0x45b930=this[_0x1a0713(_0xfb7185._0x302812)][_0x1a0713(0xb7)][_0x1a0713(_0xfb7185._0x1d2494)],_0x3f5540=this['options'][_0x1a0713(0xb7)]['ymax']):_0x391acf[_0x1a0713(0xad)]((_0x225dc3,_0x3bfe0d)=>{const _0x4ee512=_0x1a0713;_0x3bfe0d===0x0?(_0x3d6bbe=_0x225dc3[_0x4ee512(0xd4)],_0x18d2df=_0x225dc3['lng'],_0x45b930=_0x225dc3[_0x4ee512(_0x4c8b54._0x379d23)],_0x3f5540=_0x225dc3['lat']):(_0x3d6bbe=Math[_0x4ee512(0xb3)](_0x3d6bbe,_0x225dc3['lng']),_0x18d2df=Math['max'](_0x18d2df,_0x225dc3['lng']),_0x45b930=Math['min'](_0x45b930,_0x225dc3['lat']),_0x3f5540=Math[_0x4ee512(_0x4c8b54._0x2ebd37)](_0x3f5540,_0x225dc3['lat']));});let _0xba8307=_0x18d2df-_0x3d6bbe,_0x3340b5=_0x3f5540-_0x45b930;_0xba8307===0x0&&(_0xba8307=0x1);_0x3340b5===0x0&&(_0x3340b5=0x1);!this['options']['rectangle']&&(_0x3d6bbe-=_0xba8307/0xa,_0x45b930-=_0x3340b5/0xa,_0x18d2df+=_0xba8307/0xa,_0x3f5540+=_0x3340b5/0xa);_0x3d6bbe=Math['max'](_0x3d6bbe,-0xb4),_0x18d2df=Math['min'](_0x18d2df,0xb4),_0x45b930=Math['max'](_0x45b930,-0x5a),_0x3f5540=Math['min'](_0x3f5540,0x5a);const _0x21485a={'xmin':_0x3d6bbe,'xmax':_0x18d2df,'ymin':_0x45b930,'ymax':_0x3f5540};_0x21485a[_0x1a0713(_0xfb7185._0x109412)]=_0x18d2df-_0x3d6bbe,_0x21485a[_0x1a0713(0x86)]=_0x3f5540-_0x45b930,_0x21485a['rectangle']=Cesium['Rectangle']['fromDegrees'](_0x3d6bbe,_0x45b930,_0x18d2df,_0x3f5540);const _0x4439f1=Math['max'](_0x21485a['rectangle'][_0x1a0713(0xd6)],_0x21485a[_0x1a0713(_0xfb7185._0x129700)]['width']);return _0x21485a['granularity']=_0x4439f1,_0x21485a['radius']=Cesium[_0x1a0713(_0xfb7185._0x12fb5f)]['chordLength'](_0x4439f1,this[_0x1a0713(0x98)]['scene'][_0x1a0713(_0xfb7185._0x1083f4)][_0x1a0713(0x7e)]['maximumRadius']),_0x21485a;}['_getHeatCanvas'](){const _0x3c2b02={_0x290b3c:0xad,_0x34051c:0x8e,_0x412df8:0xcf,_0x301796:0xb0,_0x2a3e36:0xa6,_0x4c22fc:0x91,_0x3832c5:0xb2,_0x2d1e0b:0xb6},_0x2b2b9b={_0x22c921:0xa1,_0x3981db:0x86},_0x4b3f57=_0x172fe9,_0x27b181=this['_positions'],_0x4baa2d=[];_0x27b181[_0x4b3f57(_0x3c2b02._0x290b3c)](_0x120187=>{const _0x13c5f4=mars3d__namespace['LngLatPoint']['parse'](_0x120187);if(!_0x13c5f4)return;_0x13c5f4['value']=_0x120187['value']||0x1,_0x4baa2d['push'](_0x13c5f4);}),this[_0x4b3f57(0xa0)]=this[_0x4b3f57(_0x3c2b02._0x34051c)](_0x4baa2d),this['_rectangle']=this[_0x4b3f57(0xa0)]['rectangle'];const _0x5b303d=this['options'][_0x4b3f57(_0x3c2b02._0x412df8)],_0x20a571=mars3d__namespace[_0x4b3f57(0xa2)][_0x4b3f57(_0x3c2b02._0x301796)](_0x5b303d/this['_bounds']['diffX']*this['_bounds']['diffY']);this['_canvasWidth']=_0x5b303d,this['_canvasHeight']=_0x20a571,this['_container']['style'][_0x4b3f57(0x97)]='width:'+_0x5b303d+_0x4b3f57(_0x3c2b02._0x2a3e36)+_0x20a571+_0x4b3f57(_0x3c2b02._0x4c22fc);const _0x4c985c={...this['heatStyle'],'container':this['_container']};this['_heat']?this[_0x4b3f57(0xb6)][_0x4b3f57(0xcc)](_0x4c985c):this['_heat']=h337__namespace['create'](_0x4c985c);let _0x42f51c=_0x4baa2d[0x0]['value']??0x1,_0x43db7d=_0x4baa2d[0x0]['value']??0x0;const _0x4a4ee8=[];_0x4baa2d['forEach'](_0x51319d=>{const _0x1378f5=_0x4b3f57,_0x574c09=Math[_0x1378f5(_0x2b2b9b._0x22c921)]((_0x51319d['lng']-this[_0x1378f5(0xa0)]['xmin'])/this['_bounds']['diffX']*_0x5b303d),_0x36160d=Math['round']((this['_bounds'][_0x1378f5(0x83)]-_0x51319d['lat'])/this['_bounds'][_0x1378f5(_0x2b2b9b._0x3981db)]*_0x20a571),_0x23772a=_0x51319d['value']||0x1;_0x42f51c=Math['max'](_0x42f51c,_0x23772a),_0x43db7d=Math['min'](_0x43db7d,_0x23772a),_0x4a4ee8['push']({'x':_0x574c09,'y':_0x36160d,'value':_0x23772a});});const _0xcddce4={'min':this[_0x4b3f57(_0x3c2b02._0x3832c5)]['min']??_0x43db7d,'max':this['options']['max']??_0x42f51c,'data':_0x4a4ee8};this[_0x4b3f57(0xb6)]['setData'](_0xcddce4);const _0x4d61a5=this[_0x4b3f57(_0x3c2b02._0x2d1e0b)]['_renderer']['canvas']['toDataURL']('image/png',0x1);return _0x4d61a5;}['_getArcHeatCanvas'](){const _0x61c42={_0x141b86:0xa5},_0x4b7d01=_0x172fe9;this['_heat'][_0x4b7d01(0xcc)]({'radius':this['heatStyle']['radius']*this[_0x4b7d01(0x84)]['arcRadiusScale'],'blur':this[_0x4b7d01(0xa8)][_0x4b7d01(_0x61c42._0x141b86)]*this['style']['arcBlurScale'],'gradient':this['heatStyle']['gradientArc']||{0.25:'rgb(0,0,0)',0.55:'rgb(140,140,140)',0.85:'rgb(216,216,216)',0x1:'rgb(255,255,255)'}});const _0x44fff6=this['_heat']['_renderer']['canvas']['toDataURL']('image/png',0x1);return this['_heat']['configure'](this['options']['heatStyle']),_0x44fff6;}['updateRadius'](_0x2c4d90){const _0x22f06e={_0x24b15c:0x9a},_0x1681ee=_0x172fe9,_0x1210d1=this['_heat']['getData']();if(_0x1210d1!==null&&_0x1210d1!==void 0x0&&_0x1210d1['data'])for(const _0x5cbffd in _0x1210d1[_0x1681ee(_0x22f06e._0x24b15c)]){const _0x34f968=_0x1210d1[_0x1681ee(_0x22f06e._0x24b15c)][_0x5cbffd];_0x34f968['radius']=_0x2c4d90;}this['_heat']['setData'](_0x1210d1);const _0xc6cbdb=this['_heat']['getDataURL']();this['_updateGraphic'](_0xc6cbdb);}['getPointData'](_0x54e82c){const _0x4198a3={_0x2f4ed7:0xc1,_0x991d9b:0xce,_0x77b79a:0x82,_0x4f7675:0xa0,_0xc63325:0xca},_0x52f84c=_0x172fe9,_0x408585=mars3d__namespace['LngLatPoint'][_0x52f84c(_0x4198a3._0x2f4ed7)](_0x54e82c);if(!_0x408585)return{};const _0x22f1cb=(_0x408585['lng']-this['_bounds']['xmin'])/(this['_bounds'][_0x52f84c(0x80)]-this['_bounds']['xmin'])*this[_0x52f84c(_0x4198a3._0x991d9b)],_0x53fa1a=(this[_0x52f84c(0xa0)][_0x52f84c(0x83)]-_0x408585[_0x52f84c(_0x4198a3._0x77b79a)])/(this['_bounds']['ymax']-this[_0x52f84c(_0x4198a3._0x4f7675)][_0x52f84c(0xd2)])*this[_0x52f84c(0x7f)],_0x5701fc=this['_heat']['getValueAt']({'x':_0x22f1cb,'y':_0x53fa1a}),_0x1b85a3=this[_0x52f84c(0xb6)][_0x52f84c(0x9d)]['ctx'][_0x52f84c(_0x4198a3._0xc63325)](_0x22f1cb-0x1,_0x53fa1a-0x1,0x1,0x1)['data'];return{'x':_0x22f1cb,'y':_0x53fa1a,'value':_0x5701fc,'color':'rgba('+_0x1b85a3[0x0]+','+_0x1b85a3[0x1]+','+_0x1b85a3[0x2]+','+_0x1b85a3[0x3]+')'};}['_updateGraphic'](_0x5a34bb){const _0x5b0f8b={_0x29d6e3:0x84,_0xe4e363:0xb7,_0x4bfe1f:0x96,_0x451535:0xc7},_0x143c71=_0x172fe9;if(this[_0x143c71(_0x5b0f8b._0x29d6e3)]['type']===_0x143c71(0xbd))this[_0x143c71(0xc5)]['setOptions']({'url':_0x5a34bb,'rectangle':this[_0x143c71(0x8c)],'opacity':this[_0x143c71(0x84)]['opacity']});else this[_0x143c71(0x84)]['arc']?this['_graphic']&&this[_0x143c71(0xc4)][_0x143c71(_0x5b0f8b._0xe4e363)][_0x143c71(_0x5b0f8b._0x4bfe1f)](this['_rectangle'])?(this['_graphic']['uniforms']['image']=_0x5a34bb,this[_0x143c71(0xc4)][_0x143c71(0xc7)][_0x143c71(0xb9)]=this['_getArcHeatCanvas'](),this[_0x143c71(0xb8)]&&(this[_0x143c71(0xb8)][_0x143c71(_0x5b0f8b._0x451535)][_0x143c71(0xbd)]=_0x5a34bb,this['_graphic2'][_0x143c71(0xc7)][_0x143c71(0xb9)]=this['_graphic']['uniforms'][_0x143c71(0xb9)])):this['_createArcGraphic'](_0x5a34bb):this[_0x143c71(0xc4)]&&this['_graphic']['rectangle'][_0x143c71(0x96)](this['_rectangle'])?this['_graphic']['uniforms']['image']=_0x5a34bb:this[_0x143c71(0xc9)](_0x5a34bb);}['_createGraphic'](_0x29b492){const _0x4c1e8b={_0x1b36e9:0x8c,_0x52f4cf:0xac},_0x1d7a58=_0x172fe9;this[_0x1d7a58(0xa3)](),this['_graphic']=new mars3d__namespace['graphic']['RectanglePrimitive']({...this['options'],'rectangle':this[_0x1d7a58(_0x4c1e8b._0x1b36e9)],'appearance':new Cesium[(_0x1d7a58(0x8d))]({'material':mars3d__namespace['MaterialUtil']['createMaterial'](mars3d__namespace[_0x1d7a58(_0x4c1e8b._0x52f4cf)]['Image2'],{'image':_0x29b492}),'flat':!![]})}),this['_layer']['addGraphic'](this['_graphic']);}[_0x172fe9(0x87)](_0x52f39b){const _0x594dc8={_0x4eb1b0:0xcb,_0x3f693a:0x8a,_0x57fb66:0x88,_0x1739a1:0xb5,_0x1a9388:0x84,_0x57d5ee:0xc6,_0x61e362:0x84,_0x3f6c82:0xd0},_0x52f144=_0x172fe9;this[_0x52f144(0xa3)]();const _0x4e487f=Cesium[_0x52f144(_0x594dc8._0x4eb1b0)]['fromCache']({'cull':{'enabled':!![]},'depthTest':{'enabled':!![]},'stencilTest':{'enabled':!![],'frontFunction':Cesium['StencilFunction']['ALWAYS'],'frontOperation':{'fail':Cesium['StencilOperation']['KEEP'],'zFail':Cesium[_0x52f144(0x94)]['KEEP'],'zPass':Cesium[_0x52f144(0x94)]['REPLACE']},'backFunction':Cesium['StencilFunction']['ALWAYS'],'backOperation':{'fail':Cesium['StencilOperation']['KEEP'],'zFail':Cesium[_0x52f144(0x94)]['KEEP'],'zPass':Cesium['StencilOperation']['REPLACE']},'reference':0x2,'mask':0x2},'blending':Cesium[_0x52f144(_0x594dc8._0x3f693a)]['ALPHA_BLEND']}),_0x2a7a65=Math['floor'](this['style']['diffHeight']??this['_bounds'][_0x52f144(_0x594dc8._0x57fb66)]*0.05)+0.1;this['style']['diffHeight']&&delete this['style'][_0x52f144(_0x594dc8._0x1739a1)];this[_0x52f144(_0x594dc8._0x1a9388)][_0x52f144(_0x594dc8._0x57d5ee)]=this['_bounds'][_0x52f144(0xc6)]/(this['style'][_0x52f144(0x81)],0x64);const _0x3c904b=new Cesium['Material']({'fabric':{'uniforms':{'image':_0x52f39b,'repeat':new Cesium['Cartesian2'](0x1,0x1),'color':new Cesium['Color'](0x1,0x1,0x1,0x0),'bumpMap':this['_getArcHeatCanvas']()},'source':HeatMaterial},'translucent':!![]}),_0xc006bc=this[_0x52f144(_0x594dc8._0x61e362)]['arcDirection']||0x1;this['_graphic']=new mars3d__namespace[(_0x52f144(_0x594dc8._0x3f6c82))]['RectanglePrimitive']({...this[_0x52f144(0xb2)],'rectangle':this['_rectangle'],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'flat':!![],'aboveGround':!![],'renderState':_0x4e487f,'material':_0x3c904b,'vertexShaderSource':getVertexShaderSource(_0x2a7a65*_0xc006bc)})}),this['_layer']['addGraphic'](this['_graphic']),this['style']['arcDirection']===0x0&&(this['_graphic2']=new mars3d__namespace['graphic']['RectanglePrimitive']({...this[_0x52f144(0xb2)],'rectangle':this[_0x52f144(0x8c)],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'flat':!![],'aboveGround':!![],'renderState':_0x4e487f,'material':_0x3c904b,'vertexShaderSource':getVertexShaderSource(-_0x2a7a65)})}),this['_layer']['addGraphic'](this['_graphic2']));}}mars3d__namespace['LayerUtil']['register'](_0x172fe9(0x89),HeatLayer),mars3d__namespace['layer'][_0x172fe9(0xaf)]=HeatLayer,mars3d__namespace[_0x172fe9(0x99)]=h337__namespace;function getVertexShaderSource(_0x1bf0c9){return'in\x20vec3\x20position3DHigh;\x0a\x20\x20in\x20vec3\x20position3DLow;\x0a\x20\x20in\x20vec2\x20st;\x0a\x20\x20in\x20float\x20batchId;\x0a\x20\x20uniform\x20sampler2D\x20bumpMap_3;\x0a\x20\x20out\x20vec3\x20v_positionMC;\x0a\x20\x20out\x20vec3\x20v_positionEC;\x0a\x20\x20out\x20vec2\x20v_st;\x0a\x0a\x20\x20void\x20main()\x0a\x20\x20{\x0a\x20\x20\x20\x20vec4\x20p\x20=\x20czm_computePosition();\x0a\x20\x20\x20\x20v_positionMC\x20=\x20position3DHigh\x20+\x20position3DLow;\x0a\x20\x20\x20\x20v_positionEC\x20=\x20(czm_modelViewRelativeToEye\x20*\x20p).xyz;\x0a\x20\x20\x20\x20v_st\x20=\x20st;\x0a\x20\x20\x20\x20vec4\x20color\x20=\x20texture(bumpMap_3,\x20v_st);\x0a\x20\x20\x20\x20float\x20centerBump\x20=\x20distance(vec3(0.0),color.rgb);\x0a\x20\x20\x20\x20vec3\x20upDir\x20=\x20normalize(v_positionMC.xyz);\x0a\x20\x20\x20\x20vec3\x20disPos\x20=\x20upDir\x20*\x20centerBump\x20*\x20'+_0x1bf0c9+';\x0a\x20\x20\x20\x20p\x20+=vec4(disPos,0.0);\x0a\x20\x20\x20\x20gl_Position\x20=\x20czm_modelViewProjectionRelativeToEye\x20*\x20p;\x0a\x20\x20}\x0a';}exports['HeatLayer']=HeatLayer,Object['defineProperty'](exports,_0x172fe9(0xbe),{'value':!![]});function _0x1073(){const _0x4f5ba4=['equals','cssText','_map','h337','data','20SPLAfU','38208084DkmWno','_renderer','1277890AfQuRg','length','_bounds','round','Util','clear','Math','blur','px;height:','Cesium','heatStyle','28554MhymcF','diffX','_updateGraphic','MaterialType','forEach','defineProperty','HeatLayer','formatNum','90NKITXE','options','min','7133725IUtvqV','diffHeight','_heat','rectangle','_graphic2','bumpMap','1788496uYGidZ','496473SrrHaO','getOwnPropertyDescriptor','image','__esModule','max','removeLayer','parse','positions','mars3d-heatmap\x20mars3d-hideDiv','_graphic','_layer','granularity','uniforms','_updatePositionsHook','_createGraphic','getImageData','RenderState','configure','12401njemHh','_canvasWidth','canvasWidth','graphic','11eVfnEW','ymin','PolyUtil','lng','yellow','height','_setOptionsHook','ellipsoid','_canvasHeight','xmax','splitNum','lat','ymax','style','259xUfHEp','diffY','_createArcGraphic','radius','heat','BlendingState','13aTdzMl','_rectangle','EllipsoidSurfaceAppearance','_getBounds','removeGraphic','redrawRatio','px;display:none;','_positions','18vAfrFn','StencilOperation','globe'];_0x1073=function(){return _0x4f5ba4;};return _0x1073();}
}));
