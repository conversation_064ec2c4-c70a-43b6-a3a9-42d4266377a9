#!/usr/bin/python3.9
# 定义文件编码为 UTF - 8
# -*- coding: utf-8 -*-
# 记录代码创建时间
# @Time    :  2023/1/4 14:15
# 作者信息
# <AUTHOR> chenxw
# 作者邮箱
# @Email   : <EMAIL>
# 文件名
# @File    : sysmanUtility.py
# 描述信息，此处为空，可补充该文件的主要功能
# @Descr   :
# 开发使用的软件
# @Software: PyCharm

from django.db.models import Q

from my_app.models import SysRole, SysUserRole, TtServiceData


class SysmanHelper:
    def __init__(self):
        # 类的初始化方法，目前为空，可根据需求添加初始化逻辑
        pass

    @staticmethod
    # 导入服务数据查询
    def get_service_data_query_result(data, request):
        """
        根据传入的数据和请求对象查询服务数据。

        :param data: 包含查询条件的字典，可能包含服务名称和服务接口类型
        :param request: Django的请求对象
        :return: 符合查询条件的服务数据查询结果集，按创建时间降序排列
        """
        # 创建一个查询条件对象，用于组合多个查询条件
        query_conditions = Q()

        # 从传入的数据字典中获取服务名称
        service_ename = data.get('service_ename')
        # 从传入的数据字典中获取服务接口类型
        service_interface_type = data.get('service_interface_type')

        # 如果服务名称不为空
        if service_ename is not None:
            # 添加查询条件：服务名称包含传入的服务名称（不区分大小写）
            query_conditions &= Q(service_ename__icontains=service_ename)
        # 如果服务接口类型不为空
        if service_interface_type is not None:
            # 添加查询条件：服务接口类型等于传入的服务接口类型
            query_conditions &= Q(**{'service_interface_type': service_interface_type})

        # 根据组合的查询条件进行数据库查询，并按创建时间降序排列结果
        return TtServiceData.objects.filter(query_conditions).order_by("-create_time")

    # 获取当前登录用户角色
    @staticmethod
    def getRoleOfLoginUser(request):
        """
        获取当前登录用户的角色名称。

        :param request: Django的请求对象，用于获取当前用户的ID
        :return: 当前登录用户的角色名称
        """
        # 从请求对象中获取当前用户的ID
        user_id = request.auth.user_id
        # 根据用户ID查询用户角色关联表，获取角色ID
        role_id = SysUserRole.objects.filter(user_id=user_id)[0].role_id
        # 根据角色ID查询角色表，获取角色名称
        role_name = SysRole.objects.filter(role_id=role_id)[0].role_name
        return role_name

    # 获取部门层级信息
    @staticmethod
    def getFullDepartName(department_id, connection):
        """
        根据部门ID获取部门的完整层级名称。

        :param department_id: 部门ID
        :param connection: 数据库连接对象
        :return: 部门的完整层级名称，各级部门名称用 - 连接
        """
        # 初始化完整部门名称为空字符串
        full_department_name = ""
        # 调用 getDepartInfo 方法获取部门的基本信息
        department_id, department_name, parent_id = SysmanHelper.getDepartInfo(department_id, connection)
        # 将当前部门名称赋值给完整部门名称
        full_department_name = department_name
        # 当父部门ID不为1时，继续向上查找父部门
        while parent_id != 1:
            # 将父部门ID赋值给当前部门ID
            department_id = parent_id
            # 再次调用 getDepartInfo 方法获取父部门的基本信息
            department_id, department_name, parent_id = SysmanHelper.getDepartInfo(department_id, connection)
            # 将父部门名称添加到完整部门名称的前面，并用 - 连接
            full_department_name = department_name + "-" + full_department_name
        return full_department_name

    # 获取部门信息
    @staticmethod
    def getDepartInfo(department_id, connection):
        """
        根据部门ID从数据库中获取部门的基本信息。

        :param department_id: 部门ID
        :param connection: 数据库连接对象
        :return: 部门ID、部门名称、父部门ID，如果查询结果为空则返回 None
        """
        # 构建SQL查询语句，查询指定部门ID的部门信息
        sql = 'select department_id,department_name,parent_id from "WJYY_GZ_SYS_DEPARTMENT" where department_id={}'.format(
            department_id)
        # 获取数据库连接的游标
        cursor = connection.cursor()
        # 执行SQL查询语句
        cursor.execute(sql)
        # 获取查询结果的第一行
        records = cursor.fetchone()
        if records is not None:
            # 如果查询结果不为空，将结果转换为整数和字符串类型并返回
            return int(records[0]), str(records[1]), int(records[2])
        else:
            # 如果查询结果为空，返回 None
            return None, None, None

    # 获取同级部门的order_num
    @staticmethod
    def getDepartOrderNum(parent_id, connection):
        """
        根据父部门ID获取同级部门中的最大 order_num 值。

        :param parent_id: 父部门ID
        :param connection: 数据库连接对象
        :return: 同级部门中的最大 order_num 值，如果没有查询到结果则返回 - 1
        """
        # 构建SQL查询语句，查询指定父部门ID下的所有部门的 order_num
        sql = 'select order_num from "WJYY_GZ_SYS_DEPARTMENT" where parent_id={}'.format(
            parent_id)
        # 获取数据库连接的游标
        cursor = connection.cursor()
        # 执行SQL查询语句
        cursor.execute(sql)
        # 获取所有查询结果
        records = cursor.fetchall()
        if records is None:
            # 如果查询结果为空，将最大 order_num 值初始化为 - 1
            max_num = -1
        else:
            # 如果查询结果不为空，将最大 order_num 值初始化为第一条记录的 order_num
            max_num = records[0][0]
        # 遍历所有查询结果
        for record in records:
            if record[0] > max_num:
                # 如果当前记录的 order_num 大于最大 order_num 值，更新最大 order_num 值
                max_num = record[0]
        return max_num

    # 获取部门信息
    @staticmethod
    def getDepartByParent(parent_id, connection):
        """
        根据父部门ID获取其所有子部门的ID列表。

        :param parent_id: 父部门ID
        :param connection: 数据库连接对象
        :return: 子部门的ID列表
        """
        # 构建SQL查询语句，查询指定父部门ID下的所有部门的部门ID
        sql = 'select department_id,department_name,parent_id from "WJYY_GZ_SYS_DEPARTMENT" where parent_id={}'.format(
            parent_id)
        # 获取数据库连接的游标
        cursor = connection.cursor()
        # 执行SQL查询语句
        cursor.execute(sql)
        # 获取所有查询结果
        records = cursor.fetchall()
        # 初始化部门ID列表为空列表
        department_id_list = []
        if records is not None:
            # 如果查询结果不为空，遍历所有记录，将部门ID添加到部门ID列表中
            for record in records:
                department_id_list.append(int(record[0]))
        return department_id_list

    # 获取部门及下级部门的department_id
    # 暂时支持三级部门
    @staticmethod
    def getDepartIdAllLevel(department_id, connection):
        """
        根据部门ID获取该部门及其所有下级部门的ID列表，暂时支持三级部门。

        :param department_id: 部门ID
        :param connection: 数据库连接对象
        :return: 该部门及其所有下级部门的ID列表
        """
        # 初始化结果部门ID列表，将当前部门ID添加到列表中
        result_department_id_list = []
        result_department_id_list.append(department_id)
        # 调用 getDepartByParent 方法获取当前部门的所有子部门ID列表
        sub_department_id_list = SysmanHelper.getDepartByParent(department_id, connection)
        if len(sub_department_id_list) > 0:
            # 如果子部门ID列表不为空，将子部门ID列表添加到结果部门ID列表中
            result_department_id_list = result_department_id_list + sub_department_id_list
            # 遍历子部门ID列表
            for sub_department_id in sub_department_id_list:
                # 调用 getDepartByParent 方法获取子部门的所有子部门ID列表
                sub_sub_department_id_list = SysmanHelper.getDepartByParent(sub_department_id, connection)
                if len(sub_sub_department_id_list) > 0:
                    # 如果子部门的子部门ID列表不为空，将其添加到结果部门ID列表中
                    result_department_id_list = result_department_id_list + sub_sub_department_id_list

        return result_department_id_list

    # 获取角色信息
    @staticmethod
    def getRoleByUser(user_id, connection):
        """
        根据用户ID获取该用户的角色ID列表和角色名称列表。

        :param user_id: 用户ID
        :param connection: 数据库连接对象
        :return: 角色ID列表和角色名称列表
        """
        # 构建SQL查询语句，通过用户角色关联表和角色表关联查询用户的角色信息
        sql = 'select tablea.role_id,tableb.role_name from "WJYY_GZ_SYS_USER_ROLE" tablea ,"WJYY_GZ_SYS_ROLE" tableb where tablea.role_id=tableb.role_id and tablea.user_id={}'.format(
            user_id)
        # 获取数据库连接的游标
        cursor = connection.cursor()
        # 执行SQL查询语句
        cursor.execute(sql)
        # 获取所有查询结果
        records = cursor.fetchall()
        # 初始化角色ID列表和角色名称列表为空列表
        role_id_list = []
        role_name_list = []
        # 遍历所有查询结果
        for record in records:
            # 将角色ID添加到角色ID列表中
            role_id_list.append(int(record[0]))
            # 将角色名称添加到角色名称列表中
            role_name_list.append(str(record[1]))
        return role_id_list, role_name_list

    @staticmethod
    def getMenuByRole_ids(role_id_list, connection):
        """
        根据角色ID列表获取这些角色拥有的菜单ID列表。

        :param role_id_list: 角色ID列表
        :param connection: 数据库连接对象
        :return: 去重后的菜单ID列表
        """
        # 检查角色ID列表是否为空
        if not role_id_list:
            return []

        # 将角色ID列表转换为逗号分隔的字符串
        role_ids_str = ', '.join(map(str, role_id_list))

        # 构建SQL查询语句，查询指定角色ID列表下的所有菜单ID
        sql = f'select menu_id from  "WJYY_GZ_SYS_ROLE_MENU" where role_id in ({role_ids_str})'

        # 获取数据库连接的游标
        cursor = connection.cursor()

        # 执行SQL查询语句
        cursor.execute(sql)

        # 获取所有查询结果
        records = cursor.fetchall()

        # 初始化菜单ID列表为空列表
        menu_id_list = []

        # 遍历所有查询结果
        for record in records:
            # 将菜单ID添加到菜单ID列表中
            menu_id_list.append(int(record[0]))

        # 去重并保持顺序
        menu_id_list = list(dict.fromkeys(menu_id_list))

        return menu_id_list

    # 获取菜单信息
    @staticmethod
    def getMenuByRole(role_id, connection):
        """
        根据角色ID获取该角色拥有的菜单ID列表。

        :param role_id: 角色ID
        :param connection: 数据库连接对象
        :return: 菜单ID列表
        """
        # 构建SQL查询语句，查询指定角色ID下的所有菜单ID
        sql = 'select menu_id from  "WJYY_GZ_SYS_ROLE_MENU" where role_id={}'.format(
            role_id)
        # 获取数据库连接的游标
        cursor = connection.cursor()
        # 执行SQL查询语句
        cursor.execute(sql)
        # 获取所有查询结果
        records = cursor.fetchall()
        # 初始化菜单ID列表为空列表
        menu_id_list = []
        # 遍历所有查询结果
        for record in records:
            # 将菜单ID添加到菜单ID列表中
            menu_id_list.append(int(record[0]))
        return menu_id_list

    # 获取同级菜单的order_num
    @staticmethod
    def getMenuOrderNum(parent_id, connection):
        """
        根据父菜单ID获取同级菜单中的最大 order_num 值。

        :param parent_id: 父菜单ID
        :param connection: 数据库连接对象
        :return: 同级菜单中的最大 order_num 值，如果没有查询到结果则返回 - 1
        """
        # 构建SQL查询语句，查询指定父菜单ID下的所有菜单的 order_num，并按 order_num 降序排列
        sql = 'select order_num from "WJYY_GZ_SYS_MENU" where parent_id={} order by order_num desc '.format(
            parent_id)
        # 获取数据库连接的游标
        cursor = connection.cursor()
        # 执行SQL查询语句
        cursor.execute(sql)
        # 获取所有查询结果
        records = cursor.fetchall()
        # 获取查询结果的列名
        titile = [title[0] for title in cursor.description]
        # 将查询结果转换为字典列表
        data_list = [dict(list(zip(titile, item))) for item in records]
        if len(data_list) == 0:
            # 如果查询结果为空，将最大 order_num 值初始化为 - 1
            max_num = -1
        else:
            # 如果查询结果不为空，将最大 order_num 值初始化为第一条记录的 order_num
            max_num = data_list[0]['order_num']
        return max_num