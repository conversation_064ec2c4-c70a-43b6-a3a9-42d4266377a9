import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import App from './App.vue'
import router from './router'
import { Icon } from '@iconify/vue'

// 引入 Element Plus 暗色主题
import 'element-plus/theme-chalk/dark/css-vars.css'
import 'element-plus/dist/index.css'

// 引入 Element Plus 主题样式
import './styles/element-plus-theme.scss'

const app = createApp(App)

// 设置暗色主题
document.documentElement.classList.add('dark')

app.use(createPinia())
app.use(router)
app.use(ElementPlus, { locale: zhCn })
app.component('UIcon', Icon)
app.mount('#app')
