<template>
  <div class="warning-list-item-card" @click="handleItemClick">
    <div class="card-left">
      <el-image :src="item.imageUrl" fit="cover" class="warning-image" />
      <div class="actions">
        <UIcon name="mdi-map-marker-outline" class="button-icon" size="1rem" color="#00fffe" />
        <span class="button-text">定位</span>
        <UIcon name="mdi-text-box-search-outline" class="button-icon" size="1rem" color="#00fffe" />
        <span class="button-text">详情</span>
      </div>
    </div>
    <div class="card-right">
      <div class="info-row type-status-row">
        <div class="type-info">
          <UIcon :name="getWarningTypeIcon(getWarningTypeName(item.type))" class="type-icon" />
          <span class="type-name">{{ getWarningTypeName(item.type) }}</span>
        </div>
        <el-tag :type="getWarningStatusTagType(item.status)" size="small" class="status-tag">{{
          item.status
        }}</el-tag>
      </div>
      <div class="info-row">
        <span class="label">检测时间：</span>
        <span class="value">{{ item.detectionTime }}</span>
      </div>
      <div class="info-row">
        <span class="label">巡检航线：</span>
        <span class="value">{{ item.inspectionRoute }}</span>
      </div>
      <div class="info-row">
        <span class="label">地点：</span>
        <span class="value">{{ item.location }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import UIcon from '@/components/common/UIcon/UIcon.vue'
import { useWarningStore } from '@/stores/warningStore'
import type { PropType } from 'vue'
import type { WarningItemData } from '@/types/ui'
import {
  getWarningTypeIcon,
  getWarningStatusTagType,
  getWarningTypeTagType,
} from '@/utils/warningIconMap'
import { getWarningTypeName } from '@/utils/warningTypeMap'

// 使用预警Store
const warningStore = useWarningStore()

const props = defineProps({
  item: {
    type: Object as PropType<WarningItemData>,
    required: true,
  },
})

// 处理点击事件，切换到详情视图
const handleItemClick = () => {
  warningStore.switchToDetail(props.item.id)
}
</script>

<style scoped lang="scss">
.warning-list-item-card {
  display: flex;
  background-color: $bg-card;
  border-radius: $border-radius-base;
  padding: 0.5rem; // 调整内边距，更紧凑
  margin-bottom: 0.5rem; // 调整外边距，更紧凑
  border: 1px solid $border-color-light;
  transition: all 0.2s ease-in-out;
  cursor: pointer;

  &:hover {
    border-color: $primary-color;
    box-shadow: 0 0 10px rgba($primary-color, 0.3);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.card-left {
  position: relative;
  width: 5rem; // 进一步减小宽度 (80px)
  margin-right: 0.4rem; // 略微减小右边距

  .warning-image {
    width: 100%;
    height: 3.75rem; // 调整高度以保持比例 (60px)
    border-radius: $border-radius-small;
    object-fit: cover;
    display: block;
  }

  .actions {
    position: absolute;
    bottom: 0.15rem; // 调整定位
    left: 0.15rem;
    right: 0.15rem;
    display: flex;
    justify-content: space-between;
    padding: 0.15rem; // 减小内边距
    background-color: rgba(0, 0, 0, 0.45);
    border-radius: 0 0 $border-radius-small $border-radius-small;
    .button-icon {
      margin-right: 0.1rem; // 进一步减小图标右边距
      font-size: 0.5rem; // 使用更小的字体
    }
    .button-text {
      font-size: $font-size-panel-normal; // 使用更小的字体
    }
  }
}

.card-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: $font-size-panel-label; // 应用面板特定变量
  color: $text-secondary;
  min-width: 0; // 防止内容溢出时撑开父元素

  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 0.2rem; // 减小信息行间距
    line-height: 1.3; // 减小行高

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      white-space: nowrap;
      margin-right: 0.15rem; // 减小标签右边距
      color: $text-inactive;
    }
    .value {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: $text-default;
    }
  }

  .type-status-row {
    justify-content: space-between;
    margin-bottom: 0.3rem; // 减小类型行与其他行间距
  }

  .type-info {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: $text-default;
    font-size: $font-size-panel-normal; // 应用面板特定变量, 类型名称使用面板常规大小
    .type-icon {
      margin-right: 0.2rem; // 减小类型图标右边距
      color: $primary-color;
      font-size: $font-size-panel-normal;
    }
    .type-name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: $font-size-panel-normal; // 应用面板特定变量
    }
  }

  .status-tag {
    font-size: $font-size-panel-caption; // 应用面板特定变量
    padding: 0 0.4rem; // 微调padding
    height: auto; // 确保高度自适应内容
    line-height: 1.4; // 调整行高
    // el-tag 默认样式已适配，这里可以根据需要微调
    // 例如，对于自定义状态，可以设置特定背景色
    &.el-tag--danger {
      // 新发现
      background-color: rgba($error-color, 0.15);
      border-color: rgba($error-color, 0.3);
      color: $error-color;
    }
    &.el-tag--warning {
      // 反馈处理
      background-color: rgba($warning-color, 0.15);
      border-color: rgba($warning-color, 0.3);
      color: $warning-color;
    }
    &.el-tag--success {
      // 已办结
      background-color: rgba($success-color, 0.15);
      border-color: rgba($success-color, 0.3);
      color: $success-color;
    }
  }
}
</style>
