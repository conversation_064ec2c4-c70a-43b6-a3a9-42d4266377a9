import { ref, onMounted, onBeforeUnmount, nextTick, computed, readonly, type Ref } from 'vue'

/**
 * 图表尺寸监听组合函数
 * 监听DOM元素的尺寸变化，确保图表在有了正确的高宽后再渲染
 *
 * @param componentName 组件名称，用于调试日志
 * @param minWidth 最小宽度阈值，默认为0
 * @param minHeight 最小高度阈值，默认为0
 * @returns 包含容器引用、准备状态和尺寸信息的对象
 */
export function useChartResize(
  componentName: string = 'Chart',
  minWidth: number = 0,
  minHeight: number = 0,
) {
  // 图表容器DOM元素引用
  const chartContainerRef = ref<HTMLElement | null>(null)

  // 图表是否准备好渲染
  const chartReady = ref(false)

  // 当前容器尺寸
  const containerSize = ref({
    width: 0,
    height: 0,
  })

  // ResizeObserver实例
  let resizeObserver: ResizeObserver | null = null

  /**
   * 检查尺寸是否满足渲染条件
   */
  const checkDimensions = (width: number, height: number): boolean => {
    return width > minWidth && height > minHeight
  }

  /**
   * 处理尺寸变化
   */
  const handleResize = (entries: ResizeObserverEntry[]) => {
    for (const entry of entries) {
      const { width, height } = entry.contentRect

      // 更新容器尺寸
      containerSize.value = { width, height }

      // 如果图表还未准备好且尺寸满足条件，则标记为准备就绪
      if (!chartReady.value && checkDimensions(width, height)) {
        console.log(
          `[${componentName}] Container dimensions are now ${width}x${height}. Initializing chart.`,
        )
        chartReady.value = true

        // 图表准备就绪后，可以选择停止观察或继续观察尺寸变化
        // 这里继续观察，以便在容器尺寸变化时更新尺寸信息
        // 如果只需要初始化时的尺寸检查，可以取消注释下面的代码：
        /*
        if (resizeObserver && chartContainerRef.value) {
          resizeObserver.unobserve(chartContainerRef.value)
        }
        */
      }

      // 如果尺寸变为无效，重置准备状态
      if (chartReady.value && !checkDimensions(width, height)) {
        console.warn(
          `[${componentName}] Container dimensions became invalid: ${width}x${height}. Resetting chart.`,
        )
        // 重置chartReady状态，避免在无效尺寸时渲染
        chartReady.value = false
      }
    }
  }

  /**
   * 初始化ResizeObserver
   */
  const initializeObserver = () => {
    if (!chartContainerRef.value) {
      console.error(`[${componentName}] chartContainerRef is not available on mount to observe.`)
      return
    }

    resizeObserver = new ResizeObserver(handleResize)
    resizeObserver.observe(chartContainerRef.value)

    // 立即检查当前尺寸
    const rect = chartContainerRef.value.getBoundingClientRect()
    if (rect.width > 0 || rect.height > 0) {
      handleResize([
        {
          contentRect: rect,
          target: chartContainerRef.value,
        } as ResizeObserverEntry,
      ])
    }
  }

  /**
   * 清理ResizeObserver
   */
  const cleanupObserver = () => {
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    }
  }

  /**
   * 手动重置图表状态
   */
  const resetChart = () => {
    chartReady.value = false
    containerSize.value = { width: 0, height: 0 }
  }

  /**
   * 手动触发尺寸检查
   */
  const checkSize = () => {
    if (chartContainerRef.value) {
      const rect = chartContainerRef.value.getBoundingClientRect()
      handleResize([
        {
          contentRect: rect,
          target: chartContainerRef.value,
        } as ResizeObserverEntry,
      ])
    }
  }

  // 生命周期钩子
  onMounted(() => {
    // 使用nextTick确保DOM已经渲染
    nextTick(() => {
      initializeObserver()
    })
  })

  onBeforeUnmount(() => {
    cleanupObserver()
  })

  return {
    // 响应式引用
    chartContainerRef,
    chartReady,
    containerSize: readonly(containerSize),

    // 方法
    resetChart,
    checkSize,
    cleanupObserver,

    // 计算属性
    isValidSize: computed(() =>
      checkDimensions(containerSize.value.width, containerSize.value.height),
    ),
  }
}

/**
 * 专门用于ECharts的组合函数
 * 提供了ECharts特定的默认配置
 */
export function useEChartsResize(componentName: string = 'EChart') {
  return useChartResize(componentName, 10, 10) // ECharts最小需要10x10像素
}
