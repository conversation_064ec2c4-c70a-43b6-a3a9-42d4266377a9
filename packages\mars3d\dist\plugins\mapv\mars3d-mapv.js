/**
 * Mars3D平台插件,结合mapv可视化功能插件  mars3d-mapv
 *
 * 版本信息：v3.5.19
 * 编译日期：2024-10-29 13:42
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：维璟（北京）科技有限公司 ，2023-06-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mapv || require('mapv')), (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mapv', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-mapv"] = {}, global.mapv, global.mars3d));
})(this, (function (exports, mapv, mars3d) { 
'use strict';const _0x153831=_0x39fe;function _0x39fe(_0x2a6695,_0x1ea5e5){const _0x1df057=_0x1df0();return _0x39fe=function(_0x39febd,_0x11852d){_0x39febd=_0x39febd-0x9d;let _0xa6d4d5=_0x1df057[_0x39febd];return _0xa6d4d5;},_0x39fe(_0x2a6695,_0x1ea5e5);}(function(_0x4ef396,_0x232646){const _0x3e4fb2={_0x56997b:0xaf,_0x6cb568:0x9e,_0x259748:0xe0,_0x28ea99:0xef,_0x3bd258:0xf5,_0x1cfe0b:0xfc},_0x56758d=_0x39fe,_0xe555f4=_0x4ef396();while(!![]){try{const _0x589a5d=-parseInt(_0x56758d(0xe8))/0x1*(parseInt(_0x56758d(0xe6))/0x2)+-parseInt(_0x56758d(0xd4))/0x3+-parseInt(_0x56758d(_0x3e4fb2._0x56997b))/0x4+parseInt(_0x56758d(_0x3e4fb2._0x6cb568))/0x5*(-parseInt(_0x56758d(_0x3e4fb2._0x259748))/0x6)+-parseInt(_0x56758d(_0x3e4fb2._0x28ea99))/0x7+parseInt(_0x56758d(_0x3e4fb2._0x3bd258))/0x8*(-parseInt(_0x56758d(0xa7))/0x9)+parseInt(_0x56758d(_0x3e4fb2._0x1cfe0b))/0xa*(parseInt(_0x56758d(0xec))/0xb);if(_0x589a5d===_0x232646)break;else _0xe555f4['push'](_0xe555f4['shift']());}catch(_0x24cd96){_0xe555f4['push'](_0xe555f4['shift']());}}}(_0x1df0,0x68404));function _interopNamespace(_0x54e06b){const _0x416db7={_0x17d8b9:0xbc,_0x1b1e3a:0xa6},_0x2fb7e6={_0x917ad2:0xb6,_0x4d6245:0xc7},_0x94562b=_0x39fe;if(_0x54e06b&&_0x54e06b['__esModule'])return _0x54e06b;var _0x3e42d0=Object['create'](null);return _0x54e06b&&Object[_0x94562b(_0x416db7._0x17d8b9)](_0x54e06b)[_0x94562b(_0x416db7._0x1b1e3a)](function(_0x46043b){const _0x1834c3=_0x94562b;if(_0x46043b!=='default'){var _0x57ed2d=Object[_0x1834c3(_0x2fb7e6._0x917ad2)](_0x54e06b,_0x46043b);Object[_0x1834c3(_0x2fb7e6._0x4d6245)](_0x3e42d0,_0x46043b,_0x57ed2d['get']?_0x57ed2d:{'enumerable':!![],'get':function(){return _0x54e06b[_0x46043b];}});}}),_0x3e42d0['default']=_0x54e06b,_0x3e42d0;}function _0x1df0(){const _0x55f5b9=['dataSet','methods','16AVLbGc','animatorMovestartEvent','removeAllData','remove','windowPosition','DomUtil','push','353590FsagNr','removeChild','rgba(0,\x200,\x200,\x20.1)','isEnabledTime','_pointerEvents','get','clearData','_mapVRenderer','mouseDown','auto','_setOptionsHook','mapvAutoHeight','3589595WXZtpm','position','init','postRender','MultiLineString','fromDegrees','getHeight','devicePixelRatio','forEach','2486718dtBwwP','setZIndex','width','_onMapMouseMove','_reset','getContext','scale','render','2000180OWGTGN','bind','add','default','animator','COLOR_BUFFER_BIT','geometry','getOwnPropertyDescriptor','clickEvent','0px','processData','off','update','keys','register','cartesianToCanvasCoordinates','pointerEvents','hasOwnProperty','removeData','steps','_data','length','zIndex','canvas','defineProperty','BaseLayer','_canvasUpdate','coordinates','canvasLayer','MapVLayer','restore','context','addData','__proto__','animation','argCheck','scene','2452815qBidzh','mouseMove','clampToGround','updateData','EventType','mode','height','clear','DataSet','block','_cache_event','style','6sORIRr','ymin','_map','click','function','resize','2SRRfRN','left','760352lMyAhU','animatorMoveendEvent','filter','options','1397TAbwDO','mapvFixedHeight','_onMapClick','5005392sxCPtP','cameraMoveEnd','FeatureCollection','updateCallback'];_0x1df0=function(){return _0x55f5b9;};return _0x1df0();}var mapv__namespace=_interopNamespace(mapv),mars3d__namespace=_interopNamespace(mars3d);const Cesium$1=mars3d__namespace['Cesium'],baiduMapLayer=mapv__namespace?mapv__namespace['baiduMapLayer']:null,BaseLayer$1=baiduMapLayer?baiduMapLayer[_0x153831(0xd0)]:Function;class MapVRenderer extends BaseLayer$1{constructor(_0x171372,_0x3de607,_0x1f5da4,_0x2b6921){const _0x5d10fe={_0x5b5ebf:0xf3},_0x24621d=_0x153831;super(_0x171372,_0x3de607,_0x1f5da4);if(!BaseLayer$1)return;this['map']=_0x171372,this[_0x24621d(0xd3)]=_0x171372['scene'],this[_0x24621d(_0x5d10fe._0x5b5ebf)]=_0x3de607,_0x1f5da4=_0x1f5da4||{},this['init'](_0x1f5da4),this[_0x24621d(0xd2)](_0x1f5da4),this['initDevicePixelRatio'](),this['canvasLayer']=_0x2b6921,this['stopAniamation']=!0x1,this['animation']=_0x1f5da4['animation'];}['initDevicePixelRatio'](){const _0x302591=_0x153831;this['devicePixelRatio']=window[_0x302591(0xa5)]||0x1;}['addAnimatorEvent'](){}['animatorMovestartEvent'](){const _0x55cba8=_0x153831,_0x1161e4=this['options']['animation'];this[_0x55cba8(0xff)]()&&this[_0x55cba8(0xb3)]&&(this[_0x55cba8(0xc2)]['step']=_0x1161e4['stepsRange']['start']);}[_0x153831(0xe9)](){const _0x10a64e={_0x4c4c59:0xff},_0x2a8bb5=_0x153831;this[_0x2a8bb5(_0x10a64e._0x4c4c59)]()&&this['animator'];}[_0x153831(0xac)](){const _0x420b80={_0x2087bc:0xc6},_0x4fa28f=_0x153831;return this['canvasLayer'][_0x4fa28f(_0x420b80._0x2087bc)]['getContext'](this['context']);}[_0x153831(0xa0)](_0x31ca2a){const _0x1f3e8a={_0x1c70bf:0xce,_0x47d8c4:0xcb},_0x3b6987=_0x153831;this[_0x3b6987(0xeb)]=_0x31ca2a,this['initDataRange'](_0x31ca2a),this[_0x3b6987(_0x1f3e8a._0x1c70bf)]=this['options']['context']||'2d',this['options']['zIndex']&&this['canvasLayer']&&this[_0x3b6987(0xcb)]['setZIndex']&&this[_0x3b6987(_0x1f3e8a._0x47d8c4)][_0x3b6987(0xa8)](this['options']['zIndex']),this['initAnimator']();}['_canvasUpdate'](_0x2abfa0){const _0x2879a1={_0x20f2d5:0xd1,_0x11dd01:0xff,_0x40543d:0xc6,_0x1e3bc9:0xa9,_0x1a5f02:0xcd,_0x47f04f:0xeb,_0x13f182:0xf3,_0xd9dec0:0xeb,_0x50ca9d:0xa3,_0x5c104d:0xf2},_0x2e64f4={_0x1e969b:0xed,_0x235fe5:0x9d,_0x1da5d7:0xa4,_0x47334d:0xd9},_0x3ff3c3=_0x153831,_0x5b55f1=this['scene'];if(this['canvasLayer']&&!this['stopAniamation']){const _0xf90b0d=this['options'][_0x3ff3c3(_0x2879a1._0x20f2d5)],_0x39b6c8=this[_0x3ff3c3(0xac)]();if(this[_0x3ff3c3(_0x2879a1._0x11dd01)]()){if(void 0x0===_0x2abfa0)return void this['clear'](_0x39b6c8);this['context']==='2d'&&(_0x39b6c8['save'](),_0x39b6c8['globalCompositeOperation']='destination-out',_0x39b6c8['fillStyle']=_0x3ff3c3(0xfe),_0x39b6c8['fillRect'](0x0,0x0,_0x39b6c8[_0x3ff3c3(_0x2879a1._0x40543d)][_0x3ff3c3(_0x2879a1._0x1e3bc9)],_0x39b6c8[_0x3ff3c3(0xc6)]['height']),_0x39b6c8[_0x3ff3c3(_0x2879a1._0x1a5f02)]());}else this['clear'](_0x39b6c8);if(this['context']==='2d')for(const _0x33d789 in this[_0x3ff3c3(_0x2879a1._0x47f04f)]){_0x39b6c8[_0x33d789]=this[_0x3ff3c3(0xeb)][_0x33d789];}else _0x39b6c8['clear'](_0x39b6c8[_0x3ff3c3(0xb4)]);const _0x45f45c={'transferCoordinate':function(_0x19ab50){const _0x414f3e=_0x3ff3c3,_0x33f969=null;let _0x4b5380=_0x5b55f1[_0x414f3e(_0x2e64f4._0x1e969b)];_0x5b55f1[_0x414f3e(_0x2e64f4._0x235fe5)]&&(_0x4b5380=_0x5b55f1['globe'][_0x414f3e(_0x2e64f4._0x1da5d7)](Cesium$1['Cartographic'][_0x414f3e(0xa3)](_0x19ab50[0x0],_0x19ab50[0x1])));const _0x28e468=Cesium$1['Cartesian3']['fromDegrees'](_0x19ab50[0x0],_0x19ab50[0x1],_0x4b5380);if(!_0x28e468)return _0x33f969;const _0x4762ff=_0x5b55f1['cartesianToCanvasCoordinates'](_0x28e468);if(!_0x4762ff)return _0x33f969;if(_0x5b55f1['mapvDepthTest']&&_0x5b55f1[_0x414f3e(_0x2e64f4._0x47334d)]===Cesium$1['SceneMode']['SCENE3D']){const _0x43c3ff=new Cesium$1['EllipsoidalOccluder'](_0x5b55f1['globe']['ellipsoid'],_0x5b55f1['camera']['positionWC']),_0x3379f3=_0x43c3ff['isPointVisible'](_0x28e468);if(!_0x3379f3)return _0x33f969;}return[_0x4762ff['x'],_0x4762ff['y']];}};void 0x0!==_0x2abfa0&&(_0x45f45c[_0x3ff3c3(0xea)]=function(_0x43e977){const _0x224ee7=_0xf90b0d['trails']||0xa;return!!(_0x2abfa0&&_0x43e977['time']>_0x2abfa0-_0x224ee7&&_0x43e977['time']<_0x2abfa0);});const _0x5c854f=this[_0x3ff3c3(_0x2879a1._0x13f182)][_0x3ff3c3(0x101)](_0x45f45c);this[_0x3ff3c3(0xb9)](_0x5c854f),this[_0x3ff3c3(_0x2879a1._0xd9dec0)]['unit']==='m'&&this['options']['size'],this['options']['_size']=this['options']['size'];const _0xe56645=_0x5b55f1[_0x3ff3c3(0xbe)](Cesium$1['Cartesian3'][_0x3ff3c3(_0x2879a1._0x50ca9d)](0x0,0x0));if(!_0xe56645)return;this['drawContext'](_0x39b6c8,new mapv__namespace[(_0x3ff3c3(0xdc))](_0x5c854f),this['options'],_0xe56645),this[_0x3ff3c3(_0x2879a1._0x47f04f)][_0x3ff3c3(_0x2879a1._0x5c104d)]&&this['options']['updateCallback'](_0x2abfa0);}}[_0x153831(0xd7)](_0xe929b4,_0x3d1767){const _0x161209={_0x5bd97d:0xf3},_0x1463b7=_0x153831;let _0x37d5f3=_0xe929b4;_0x37d5f3&&_0x37d5f3[_0x1463b7(0x101)]&&(_0x37d5f3=_0x37d5f3['get']()),void 0x0!==_0x37d5f3&&this[_0x1463b7(_0x161209._0x5bd97d)]['set'](_0x37d5f3),super['update']({'options':_0x3d1767});}[_0x153831(0xcf)](_0x10962c,_0x221798){const _0x5bc4ed=_0x153831;let _0x43ebe9=_0x10962c;_0x10962c&&_0x10962c['get']&&(_0x43ebe9=_0x10962c['get']()),this['dataSet'][_0x5bc4ed(0xb1)](_0x43ebe9),this['update']({'options':_0x221798});}['getData'](){const _0x4e507c={_0x213dbe:0xf3},_0x25c624=_0x153831;return this[_0x25c624(_0x4e507c._0x213dbe)];}['removeData'](_0x1561d7){const _0x46ede3={_0x39003f:0x101},_0x594f0a=_0x153831;if(this['dataSet']){const _0x71e11d=this['dataSet'][_0x594f0a(_0x46ede3._0x39003f)]({'filter':function(_0x20ea7c){const _0x46f527=_0x594f0a;return _0x1561d7==null||typeof _0x1561d7!==_0x46f527(0xe4)||!_0x1561d7(_0x20ea7c);}});this['dataSet']['set'](_0x71e11d),this['update']({'options':null});}}['clearData'](){const _0x309b8b=_0x153831;this['dataSet']&&this['dataSet']['clear'](),this[_0x309b8b(0xbb)]({'options':null});}['draw'](){const _0x56f2f5={_0xe74570:0xcb},_0x2fe60d=_0x153831;this[_0x2fe60d(_0x56f2f5._0xe74570)]['draw']();}[_0x153831(0xdb)](_0x2b1587){_0x2b1587&&_0x2b1587['clearRect']&&_0x2b1587['clearRect'](0x0,0x0,_0x2b1587['canvas']['width'],_0x2b1587['canvas']['height']);}['destroy'](){const _0x304e3c={_0x2b194a:0x102},_0x365105=_0x153831;this['clear'](this['getContext']()),this[_0x365105(_0x304e3c._0x2b194a)](),this['animator']&&this[_0x365105(0xb3)]['stop'](),this['animator']=null,this['canvasLayer']=null;}}if(mapv__namespace!==null&&mapv__namespace!==void 0x0&&mapv__namespace['DataSet'])mapv__namespace['DataSet']['prototype']['transferCoordinate']=function(_0x1fa88c,_0x54f51c,_0xc0536,_0x187337){const _0x5627d5={_0x2d9a68:0xca,_0x85f7c1:0xb5,_0x5e467a:0xa2,_0x499d02:0xc4,_0x3519de:0xc4},_0x115fc8={_0x594b71:0xfb},_0x4e1c27=_0x153831;_0x187337=_0x187337||'_coordinates',_0xc0536=_0xc0536||_0x4e1c27(_0x5627d5._0x2d9a68);for(let _0x40eeb2=0x0;_0x40eeb2<_0x1fa88c['length'];_0x40eeb2++){const _0x206edb=_0x1fa88c[_0x40eeb2][_0x4e1c27(_0x5627d5._0x85f7c1)],_0x424cd9=_0x206edb[_0xc0536];switch(_0x206edb['type']){case'Point':{const _0x289038=_0x54f51c(_0x424cd9);_0x289038?_0x206edb[_0x187337]=_0x289038:_0x206edb[_0x187337]=[-0x3e7,-0x3e7];}break;case'LineString':{const _0x245279=[];for(let _0x5bb4f8=0x0;_0x5bb4f8<_0x424cd9['length'];_0x5bb4f8++){const _0xbda9d8=_0x54f51c(_0x424cd9[_0x5bb4f8]);_0xbda9d8&&_0x245279['push'](_0xbda9d8);}_0x206edb[_0x187337]=_0x245279;}break;case _0x4e1c27(_0x5627d5._0x5e467a):case'Polygon':{const _0x248536=_0x2f8be3(_0x424cd9);_0x206edb[_0x187337]=_0x248536;}break;case'MultiPolygon':{const _0x256c8f=[];for(let _0x1752f9=0x0;_0x1752f9<_0x424cd9[_0x4e1c27(_0x5627d5._0x499d02)];_0x1752f9++){const _0x3af90b=_0x2f8be3(_0x424cd9[_0x1752f9]);_0x3af90b[_0x4e1c27(_0x5627d5._0x3519de)]>0x0&&_0x256c8f['push'](_0x3af90b);}_0x206edb[_0x187337]=_0x256c8f;}break;}}function _0x2f8be3(_0x304a9e){const _0x5ea6b0=_0x4e1c27,_0xdd3871=[];for(let _0x38f4cf=0x0;_0x38f4cf<_0x304a9e['length'];_0x38f4cf++){const _0x40ac7d=_0x304a9e[_0x38f4cf],_0xc77f2a=[];for(let _0x2db438=0x0;_0x2db438<_0x40ac7d['length'];_0x2db438++){const _0x2cb8a0=_0x54f51c(_0x40ac7d[_0x2db438]);_0x2cb8a0&&_0xc77f2a['push'](_0x2cb8a0);}_0xc77f2a['length']>0x0&&_0xdd3871[_0x5ea6b0(_0x115fc8._0x594b71)](_0xc77f2a);}return _0xdd3871;}return _0x1fa88c;};else throw new Error('请引入\x20mapv\x20库\x20');const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer'][_0x153831(0xc8)];class MapVLayer extends BaseLayer{constructor(_0x17a55f,_0x292bc7){const _0xfe7f1f=_0x153831;super(_0x17a55f),this['_pointerEvents']=this['options']['pointerEvents'],this['dataSet']=_0x292bc7||new mapv__namespace['DataSet'](_0x17a55f['data']),this[_0xfe7f1f(0xc6)]=null;}get[_0x153831(0xbf)](){return this['_pointerEvents'];}set[_0x153831(0xbf)](_0x2d7946){const _0xe1714e={_0x4bc88b:0x100},_0x2148e0=_0x153831;this[_0x2148e0(_0xe1714e._0x4bc88b)]=_0x2d7946,this['canvas']&&(_0x2d7946?this['canvas']['style']['pointerEvents']='all':this['canvas']['style']['pointerEvents']='none');}['_showHook'](_0x1d5b9a){const _0x5287dc={_0x2e7fb1:0xdd},_0x1624fe=_0x153831;_0x1d5b9a?this[_0x1624fe(0xc6)][_0x1624fe(0xdf)]['display']=_0x1624fe(_0x5287dc._0x2e7fb1):this['canvas']['style']['display']='none';}['_mountedHook'](){const _0x4276ea={_0x1e488a:0xd3,_0x4dcd76:0xe2},_0x4c2509=_0x153831;this['_map'][_0x4c2509(_0x4276ea._0x1e488a)]['mapvDepthTest']=this['options']['depthTest']??!![],this[_0x4c2509(_0x4276ea._0x4dcd76)]['scene']['mapvAutoHeight']=this['options'][_0x4c2509(0xd6)]??![],this[_0x4c2509(_0x4276ea._0x4dcd76)][_0x4c2509(0xd3)]['mapvFixedHeight']=this[_0x4c2509(0xeb)]['fixedHeight']??0x0;}['_addedHook'](){const _0x589e47={_0x9a50ea:0xc3,_0x23a0e0:0xf3},_0x33870f=_0x153831;this[_0x33870f(0xf3)]&&(!this['dataSet'][_0x33870f(_0x589e47._0x9a50ea)]||this[_0x33870f(_0x589e47._0x23a0e0)]['_data']['length']===0x0)&&(this['dataSet']['_data']=[]['concat'](this['dataSet']['_dataCache'])),this['_mapVRenderer']=new MapVRenderer(this['_map'],this['dataSet'],this['options'],this),this['initDevicePixelRatio'](),this['canvas']=this['_createCanvas'](),this['render']=this['render']['bind'](this),this['bindEvent'](),this['_reset']();}['_removedHook'](){const _0x11371f={_0x1a67da:0x103,_0x23225a:0xc6,_0x202cc1:0xc6},_0x4b3a91=_0x153831;this['unbindEvent'](),this[_0x4b3a91(_0x11371f._0x1a67da)]&&(this[_0x4b3a91(_0x11371f._0x1a67da)]['destroy'](),this['_mapVRenderer']=null),this[_0x4b3a91(_0x11371f._0x23225a)]['parentElement']['removeChild'](this[_0x4b3a91(_0x11371f._0x202cc1)]);}['initDevicePixelRatio'](){this['devicePixelRatio']=window['devicePixelRatio']||0x1;}['bindEvent'](){const _0x3a8487={_0xb98d97:0x104,_0x31310b:0xd8,_0x4d5988:0xf4},_0x247b3f=_0x153831;var _0x952140,_0x31b563;this['_map']['on'](mars3d__namespace['EventType'][_0x247b3f(_0x3a8487._0xb98d97)],this['_onMoveStartEvent'],this),this['_map']['on'](mars3d__namespace['EventType']['cameraMoveStart'],this['_onMoveStartEvent'],this),this['_map']['on'](mars3d__namespace[_0x247b3f(_0x3a8487._0x31310b)][_0x247b3f(0xf0)],this['_onMoveEndEvent'],this),(_0x952140=this[_0x247b3f(0xeb)])!==null&&_0x952140!==void 0x0&&(_0x952140=_0x952140[_0x247b3f(_0x3a8487._0x4d5988)])!==null&&_0x952140!==void 0x0&&_0x952140['click']&&this['_map']['on'](mars3d__namespace['EventType']['click'],this['_onMapClick'],this),(_0x31b563=this['options'])!==null&&_0x31b563!==void 0x0&&(_0x31b563=_0x31b563['methods'])!==null&&_0x31b563!==void 0x0&&_0x31b563['mousemove']&&this['_map']['on'](mars3d__namespace[_0x247b3f(_0x3a8487._0x31310b)]['mouseMove'],this[_0x247b3f(0xaa)],this);}['unbindEvent'](){const _0x41c1d5={_0x248837:0xf0,_0x4ebacb:0xe2,_0x247de8:0xd8},_0x19d8d0=_0x153831;var _0x1eb5a0,_0x220e7f;this['_map']['off'](mars3d__namespace['EventType'][_0x19d8d0(0x104)],this['_onMoveStartEvent'],this),this['_map']['off'](mars3d__namespace[_0x19d8d0(0xd8)]['cameraMoveStart'],this['_onMoveStartEvent'],this),this['_map'][_0x19d8d0(0xba)](mars3d__namespace['EventType'][_0x19d8d0(_0x41c1d5._0x248837)],this['_onMoveEndEvent'],this),this[_0x19d8d0(_0x41c1d5._0x4ebacb)]['off'](mars3d__namespace[_0x19d8d0(0xd8)]['postRender'],this[_0x19d8d0(0xab)],this),(_0x1eb5a0=this['options'])!==null&&_0x1eb5a0!==void 0x0&&(_0x1eb5a0=_0x1eb5a0['methods'])!==null&&_0x1eb5a0!==void 0x0&&_0x1eb5a0['click']&&this['_map']['off'](mars3d__namespace['EventType'][_0x19d8d0(0xe3)],this['_onMapClick'],this),(_0x220e7f=this['options'])!==null&&_0x220e7f!==void 0x0&&(_0x220e7f=_0x220e7f[_0x19d8d0(0xf4)])!==null&&_0x220e7f!==void 0x0&&_0x220e7f['mousemove']&&this[_0x19d8d0(0xe2)]['off'](mars3d__namespace[_0x19d8d0(_0x41c1d5._0x247de8)]['mouseMove'],this['_onMapMouseMove'],this);}['_onMoveStartEvent'](){const _0x975315={_0xbb92a9:0xf6,_0x1c2b61:0xe2},_0x10aa97=_0x153831;this['_mapVRenderer']&&(this['_mapVRenderer'][_0x10aa97(_0x975315._0xbb92a9)](),this['_map'][_0x10aa97(0xba)](mars3d__namespace[_0x10aa97(0xd8)]['postRender'],this['_reset'],this),this[_0x10aa97(_0x975315._0x1c2b61)]['on'](mars3d__namespace['EventType']['postRender'],this['_reset'],this));}['_onMoveEndEvent'](){const _0x168316={_0x47cfa0:0xa1},_0x4c3871=_0x153831;this['_mapVRenderer']&&(this['_map']['off'](mars3d__namespace[_0x4c3871(0xd8)][_0x4c3871(_0x168316._0x47cfa0)],this['_reset'],this),this['_mapVRenderer']['animatorMoveendEvent'](),this['_reset']());}[_0x153831(0x106)](_0x4db185,_0x35ef5c){this['_removedHook'](),this['_addedHook']();}[_0x153831(0xcf)](_0x3d90cd){const _0x208d3c={_0x4e4ba6:0xeb},_0x26cbff=_0x153831;this['_mapVRenderer']&&this['_mapVRenderer']['addData'](_0x3d90cd,this[_0x26cbff(_0x208d3c._0x4e4ba6)]);}['updateData'](_0x31d09){const _0x3ebf03={_0x187214:0x103},_0x55e777=_0x153831;this['_mapVRenderer']&&this[_0x55e777(_0x3ebf03._0x187214)]['updateData'](_0x31d09,this['options']);}['getData'](){return this['_mapVRenderer']&&(this['dataSet']=this['_mapVRenderer']['getData']()),this['dataSet'];}[_0x153831(0xc1)](_0x15c6b6){const _0x530bbc={_0x38939d:0x103},_0x39192a=_0x153831;this[_0x39192a(_0x530bbc._0x38939d)]&&this['_mapVRenderer']['removeData'](_0x15c6b6);}[_0x153831(0xf7)](){const _0xb8ad7e=_0x153831;this['_mapVRenderer']&&this[_0xb8ad7e(0x103)]['clearData']();}['_createCanvas'](){const _0x16bf6f={_0x4e9912:0xfa,_0x543a44:0x9f,_0x4fb6d3:0xdf,_0x504ca2:0xdf,_0x495cb8:0x105,_0x317840:0xc5,_0x1366db:0xeb,_0x934417:0xc5,_0x1aa617:0xad},_0x2fba1a=_0x153831,_0x257735=mars3d__namespace[_0x2fba1a(_0x16bf6f._0x4e9912)]['create'](_0x2fba1a(0xc6),'mars3d-mapv',this['_map']['container']);_0x257735['id']=this['id'],_0x257735['style'][_0x2fba1a(_0x16bf6f._0x543a44)]='absolute',_0x257735[_0x2fba1a(_0x16bf6f._0x4fb6d3)]['top']=_0x2fba1a(0xb8),_0x257735[_0x2fba1a(0xdf)][_0x2fba1a(0xe7)]='0px',_0x257735['width']=parseInt(this['_map'][_0x2fba1a(0xc6)][_0x2fba1a(0xa9)]),_0x257735[_0x2fba1a(0xda)]=parseInt(this['_map']['canvas'][_0x2fba1a(0xda)]),_0x257735['style']['width']=this['_map']['canvas']['style']['width'],_0x257735['style']['height']=this['_map']['canvas'][_0x2fba1a(_0x16bf6f._0x504ca2)][_0x2fba1a(0xda)],_0x257735['style'][_0x2fba1a(0xbf)]=this[_0x2fba1a(0x100)]?_0x2fba1a(_0x16bf6f._0x495cb8):'none',_0x257735[_0x2fba1a(0xdf)][_0x2fba1a(_0x16bf6f._0x317840)]=this[_0x2fba1a(_0x16bf6f._0x1366db)][_0x2fba1a(_0x16bf6f._0x934417)]??0x9;if(this['options']['context']==='2d'){const _0x42e4dc=this['devicePixelRatio'];_0x257735['getContext'](this['options']['context'])[_0x2fba1a(_0x16bf6f._0x1aa617)](_0x42e4dc,_0x42e4dc);}return _0x257735;}[_0x153831(0xab)](){this['resize'](),this['render']();}['draw'](){const _0x28b77e={_0x536118:0xab},_0x282c8f=_0x153831;this[_0x282c8f(_0x28b77e._0x536118)]();}[_0x153831(0xf8)](){const _0x45facd={_0x821229:0x103,_0x44cfe3:0x103,_0x5b684d:0xc6},_0x3ada0c=_0x153831;this[_0x3ada0c(_0x45facd._0x821229)]&&(this['_mapVRenderer']['destroy'](),this[_0x3ada0c(_0x45facd._0x44cfe3)]=null),this[_0x3ada0c(_0x45facd._0x5b684d)]['parentElement'][_0x3ada0c(0xfd)](this['canvas']);}[_0x153831(0xae)](){const _0x42d4ff={_0x1f2224:0xc9},_0x108913=_0x153831;this['_mapVRenderer'][_0x108913(_0x42d4ff._0x1f2224)]();}[_0x153831(0xe5)](){const _0x2d4c74={_0x444d06:0xc6,_0x507acc:0xdf,_0x17b153:0xa9,_0x16b6a3:0xa9},_0x69e5dc=_0x153831;if(this[_0x69e5dc(_0x2d4c74._0x444d06)]){const _0x5520a1=this['canvas'];_0x5520a1[_0x69e5dc(_0x2d4c74._0x507acc)]['position']='absolute',_0x5520a1[_0x69e5dc(_0x2d4c74._0x507acc)]['top']='0px',_0x5520a1['style']['left']='0px',_0x5520a1[_0x69e5dc(_0x2d4c74._0x17b153)]=parseInt(this['_map'][_0x69e5dc(0xc6)][_0x69e5dc(_0x2d4c74._0x16b6a3)]),_0x5520a1[_0x69e5dc(0xda)]=parseInt(this['_map']['canvas']['height']),_0x5520a1['style']['width']=this['_map']['canvas']['style']['width'],_0x5520a1['style']['height']=this['_map']['canvas']['style']['height'];}}['getRectangle'](_0x14120d){const _0x1ce7b8={_0x2749b7:0xf3,_0x4af41f:0xf1,_0x29dda9:0xe1},_0xcd9dc8=_0x153831;if(!this[_0xcd9dc8(_0x1ce7b8._0x2749b7)]||!this['dataSet']['_data'])return;const _0x20c49f=mars3d__namespace['Util']['getExtentByGeoJSON']({'type':_0xcd9dc8(_0x1ce7b8._0x4af41f),'features':this[_0xcd9dc8(0xf3)]['_data']});if(!_0x20c49f)return;return _0x14120d!==null&&_0x14120d!==void 0x0&&_0x14120d['isFormat']?_0x20c49f:Cesium['Rectangle'][_0xcd9dc8(0xa3)](_0x20c49f['xmin'],_0x20c49f[_0xcd9dc8(_0x1ce7b8._0x29dda9)],_0x20c49f['xmax'],_0x20c49f['ymax']);}['_onMapClick'](_0x27c4e6){const _0x170c4f=_0x153831;this['_cache_event']=_0x27c4e6,this[_0x170c4f(0x103)]&&this['_mapVRenderer'][_0x170c4f(0xb7)](_0x27c4e6['windowPosition'],_0x27c4e6);}['_onMapMouseMove'](_0x4010c4){const _0x9dcc02={_0x75b08d:0xf9},_0x4fc446=_0x153831;this['_cache_event']=_0x4010c4,this['_mapVRenderer']&&this['_mapVRenderer']['mousemoveEvent'](_0x4010c4[_0x4fc446(_0x9dcc02._0x75b08d)],_0x4010c4);}['on'](_0x3140ab,_0x12d9fd,_0x4b6d39){const _0x14fe12={_0x12679c:0xf4,_0x5c6467:0xeb,_0xe959bf:0xd8,_0x2f7dff:0xe3},_0xb14955=_0x153831;this['options'][_0xb14955(_0x14fe12._0x12679c)]=this[_0xb14955(_0x14fe12._0x5c6467)][_0xb14955(_0x14fe12._0x12679c)]||{};if(_0x3140ab===mars3d__namespace[_0xb14955(_0x14fe12._0xe959bf)][_0xb14955(0xe3)])this['options']['methods'][_0xb14955(_0x14fe12._0x2f7dff)]=_0x3af910=>{const _0x2e62ef=_0xb14955;_0x3af910&&_0x12d9fd[_0x2e62ef(0xb0)](_0x4b6d39)({...this['_cache_event'],'layer':this,'data':_0x3af910});},this['_map']['on'](mars3d__namespace['EventType'][_0xb14955(0xe3)],this['_onMapClick'],this);else _0x3140ab===mars3d__namespace['EventType']['mouseMove']&&(this['options'][_0xb14955(0xf4)]['mousemove']=_0x416e6a=>{const _0x2d96e0=_0xb14955;_0x416e6a&&_0x12d9fd['bind'](_0x4b6d39)({...this[_0x2d96e0(0xde)],'layer':this,'data':_0x416e6a});},this['_map']['on'](mars3d__namespace[_0xb14955(0xd8)][_0xb14955(0xd5)],this['_onMapMouseMove'],this));return this;}['off'](_0x332d95,_0x375144){const _0x59ca76={_0x3f9385:0xe2,_0x3cd30c:0xba},_0x3bd95f=_0x153831;if(_0x332d95==='click'){var _0x4eba12;this['_map']['off'](_0x332d95,this[_0x3bd95f(0xee)],this),(_0x4eba12=this[_0x3bd95f(0xeb)]['methods'])!==null&&_0x4eba12!==void 0x0&&_0x4eba12['mousemove']&&delete this['options']['methods'][_0x3bd95f(0xe3)];}else{if(_0x332d95==='mouseMove'){var _0x3dae6a;this[_0x3bd95f(_0x59ca76._0x3f9385)][_0x3bd95f(_0x59ca76._0x3cd30c)](_0x332d95,this['_onMapMouseMove'],this),(_0x3dae6a=this[_0x3bd95f(0xeb)][_0x3bd95f(0xf4)])!==null&&_0x3dae6a!==void 0x0&&_0x3dae6a['mousemove']&&delete this['options'][_0x3bd95f(0xf4)]['mousemove'];}}return this;}}mars3d__namespace['LayerUtil'][_0x153831(0xbd)]('mapv',MapVLayer),mars3d__namespace['layer'][_0x153831(0xcc)]=MapVLayer,mars3d__namespace['mapv']=mapv__namespace,exports[_0x153831(0xcc)]=MapVLayer,Object['keys'](mapv)['forEach'](function(_0x1bbe02){const _0x2d1279={_0x421815:0xb2},_0x363c43=_0x153831;if(_0x1bbe02!==_0x363c43(_0x2d1279._0x421815)&&!exports[_0x363c43(0xc0)](_0x1bbe02))Object[_0x363c43(0xc7)](exports,_0x1bbe02,{'enumerable':!![],'get':function(){return mapv[_0x1bbe02];}});}),Object['defineProperty'](exports,'__esModule',{'value':!![]});
}));
