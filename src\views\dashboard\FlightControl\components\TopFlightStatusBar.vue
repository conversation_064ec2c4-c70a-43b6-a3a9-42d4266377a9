<script setup lang="ts">
import { computed } from 'vue'
import { useUIStore } from '@/stores/uiStore'
import { useDroneStore } from '@/stores/droneStore'
import { ElMessageBox } from 'element-plus'

// 使用Store
const uiStore = useUIStore()
const droneStore = useDroneStore()

// 飞控环境数据 - 直接从Store获取
const flightData = computed(() => {
  return (
    droneStore.flightRealTimeData?.environment || {
      windSpeed: '2级',
      windLevel: '微风',
      weather: '多云',
      humidity: '46%',
      temperature: '26°C',
    }
  )
})

// 防抖处理，避免多次点击
const isExiting = ref(false)

// 退出飞控模式
const handleExitFlight = async () => {
  // 防止重复点击
  if (isExiting.value) return

  try {
    isExiting.value = true

    await ElMessageBox.confirm('确定要退出飞控模式吗？', '退出确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      center: true,
    })

    // 确认退出，回到正常模式
    uiStore.exitFlightMode()
  } catch {
    // 用户取消，不做任何操作
  } finally {
    // 重置防抖状态
    isExiting.value = false
  }
}
</script>

<template>
  <div class="top-flight-status-bar">
    <div class="status-container">
      <!-- 左侧：三个主要数据 -->
      <div class="main-data">
        <!-- 风速风力 -->
        <div class="data-item">
          <UIcon name="mdi:weather-windy" size="1rem" color="#2196F3" />
          <div class="data-info">
            <span class="data-label">风速</span>
            <span class="data-value">{{ flightData.windSpeed }}</span>
          </div>
        </div>

        <!-- 天气状况 -->
        <div class="data-item">
          <UIcon name="mdi:weather-partly-cloudy" size="1rem" color="#FF9800" />
          <div class="data-info">
            <span class="data-label">天气</span>
            <span class="data-value">{{ flightData.weather }}</span>
          </div>
        </div>

        <!-- 湿度温度 -->
        <div class="data-item">
          <UIcon name="mdi:water-percent" size="1rem" color="#4CAF50" />
          <div class="data-info">
            <span class="data-label">湿度</span>
            <span class="data-value">{{ flightData.humidity }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧：退出按钮 -->
      <div class="exit-section">
        <el-button
          type="default"
          size="small"
          @click="handleExitFlight"
          class="exit-button"
          :disabled="isExiting"
          :loading="isExiting"
        >
          <UIcon v-if="!isExiting" name="mdi:exit-to-app" size="0.8rem" />
          <span>{{ isExiting ? '退出中...' : '退出飞控' }}</span>
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.top-flight-status-bar {
  // 增加宽度，使用更宽的设计
  width: 100%;
  max-width: 50rem; // 限制最大宽度
  height: $dashboard-flight-top-bar-height;
  background: $bg-panel;
  border-radius: $border-radius-base;
  box-shadow: inset 0 0 1rem rgba($glow-color, 0.3);
  display: flex;
  align-items: center;
  padding: 0 2rem; // 增加左右内边距
  margin: 0 auto; // 居中显示

  .status-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 3rem; // 在主数据和退出按钮之间增加间距

    .main-data {
      display: flex;
      align-items: center;
      gap: 3rem; // 增加数据项之间的间距
      flex: 1; // 占据剩余空间

      .data-item {
        display: flex;
        align-items: center;
        gap: 0.6rem; // 稍微增加图标和文字的间距

        .data-info {
          display: flex;
          flex-direction: column;
          gap: 0.1rem;

          .data-label {
            font-size: $font-size-panel-micro;
            color: $text-secondary;
            line-height: 1;
          }

          .data-value {
            font-size: $font-size-panel-caption;
            color: $text-default;
            font-weight: bold;
            line-height: 1;
          }
        }
      }
    }

    .exit-section {
      flex-shrink: 0; // 防止按钮被压缩

      .exit-button {
        display: flex;
        align-items: center;
        gap: 0.3rem;
        font-size: $font-size-panel-caption;
        padding: 0.4rem 1rem; // 稍微增加按钮内边距
        border-radius: $border-radius-small;

        // 使用主题色样式
        background: rgba($primary-color, 0.1);
        border: 1px solid rgba($primary-color, 0.3);
        color: $primary-color;

        &:hover {
          background: rgba($primary-color, 0.2);
          border-color: rgba($primary-color, 0.5);
          color: $primary-color;
        }

        &:active {
          background: rgba($primary-color, 0.3);
        }

        &:disabled {
          background: rgba($primary-color, 0.05);
          border-color: rgba($primary-color, 0.2);
          color: rgba($primary-color, 0.6);
        }
      }
    }
  }
}

// MessageBox样式已移至全局样式文件 src/styles/element-plus-theme.scss
</style>
