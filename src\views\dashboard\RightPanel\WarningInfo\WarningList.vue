<template>
  <div class="warning-list-container">
    <div class="warning-list-header">
      <div class="header-left">
        <h3 class="list-title">预警列表</h3>
        <UIcon name="mdi-cached" class="header-icon refresh-icon" @click="handleRefresh" />
      </div>
      <div class="header-controls">
        <el-dropdown @command="handleSortChange" trigger="click">
          <span class="el-dropdown-link">
            {{ currentSortLabel }}<UIcon name="mdi-chevron-down" class="dropdown-icon" />
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="option in sortOptions"
                :key="option.value"
                :command="option.value"
                >{{ option.label }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown @command="handleStatusFilterChange" trigger="click" style="margin-left: 1rem">
          <span class="el-dropdown-link">
            {{ currentStatusFilterLabel }}<UIcon name="mdi-chevron-down" class="dropdown-icon" />
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="option in statusFilterOptions"
                :key="option.value"
                :command="option.value"
                >{{ option.label }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <el-scrollbar class="warning-list-body">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <template v-else-if="filteredAndSortedWarnings.length > 0">
        <WarningListItem
          v-for="warning in filteredAndSortedWarnings"
          :key="warning.id"
          :item="warning"
          @click="handleWarningClick(warning.id)"
        />
      </template>
      <el-empty v-else description="暂无预警信息" class="empty-list" />
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import type { WarningItemData } from '@/types/ui'
import UIcon from '@/components/common/UIcon/UIcon.vue'
import WarningListItem from './WarningListItem.vue'

// 定义props
interface Props {
  warnings: WarningItemData[]
  loading: boolean
  error: string | null
}

const props = defineProps<Props>()

// 定义emits
const emit = defineEmits<{
  'warning-selected': [warningId: string]
  refresh: []
}>()

// 排序选项
const sortOptions = ref([
  { label: '默认排序', value: 'default' },
  { label: '按时间正序', value: 'time_asc' },
  { label: '按时间倒序', value: 'time_desc' },
])
const currentSort = ref('default')
const currentSortLabel = computed(
  () => sortOptions.value.find((opt) => opt.value === currentSort.value)?.label || '默认排序',
)

// 状态筛选选项
const statusFilterOptions = ref([
  { label: '全部状态', value: 'all' },
  { label: '新发现', value: '新发现' },
  { label: '反馈处理', value: '反馈处理' },
  { label: '已办结', value: '已办结' },
])
const currentStatusFilter = ref('all')
const currentStatusFilterLabel = computed(
  () =>
    statusFilterOptions.value.find((opt) => opt.value === currentStatusFilter.value)?.label ||
    '全部状态',
)

// 计算属性：获取筛选和排序后的预警列表
const filteredAndSortedWarnings = computed(() => {
  let warnings = [...props.warnings]

  // 根据本地状态筛选进行筛选
  if (currentStatusFilter.value !== 'all') {
    warnings = warnings.filter((w) => w.status === currentStatusFilter.value)
  }

  // 根据排序选项进行排序
  if (currentSort.value === 'time_asc') {
    warnings.sort(
      (a, b) => new Date(a.detectionTime).getTime() - new Date(b.detectionTime).getTime(),
    )
  } else if (currentSort.value === 'time_desc') {
    warnings.sort(
      (a, b) => new Date(b.detectionTime).getTime() - new Date(a.detectionTime).getTime(),
    )
  }

  return warnings
})

// 事件处理函数
const handleSortChange = (command: string) => {
  currentSort.value = command
}

const handleStatusFilterChange = async (command: string) => {
  currentStatusFilter.value = command
}

const handleRefresh = () => {
  emit('refresh')
}

// 处理预警项点击
const handleWarningClick = (warningId: string) => {
  emit('warning-selected', warningId)
}
</script>

<style scoped lang="scss">
.warning-list-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%; // 占满父容器高度
}

.warning-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem; // 上下0.5rem, 左右0.75rem
  border-bottom: 1px solid $border-color;
  flex-shrink: 0; // 防止头部被压缩
  // height: 40px; // 固定头部高度
  box-sizing: border-box;

  .header-left {
    display: flex;
    align-items: center;
    .list-title {
      font-size: $font-size-panel-title; // 应用面板特定变量
      color: $text-inactive;
      margin: 0;
      margin-right: 0.5rem;
    }
    .refresh-icon {
      font-size: $font-size-panel-title; // 应用面板特定变量
      color: $text-secondary;
      cursor: pointer;
      &:hover {
        color: $primary-color;
      }
    }
  }

  .header-controls {
    display: flex;
    align-items: center;
    .el-dropdown-link {
      cursor: pointer;
      color: $text-secondary;
      font-size: $font-size-panel-title; // 应用面板特定变量
      display: flex;
      align-items: center;
      &:hover {
        color: $primary-color;
      }
      .dropdown-icon {
        margin-left: 0.2rem;
        font-size: $font-size-panel-label; // 应用面板特定变量
      }
    }
  }
}

.warning-list-body {
  flex: 1; // 占据剩余空间
  padding: 0.2rem;
  height: 100%;
  padding-top: 0.5rem; // 列表项与头部有点间距
  overflow-y: auto; // 内部滚动
  .empty-list {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    :deep(.el-empty__description p) {
      color: $text-inactive;
      font-size: $font-size-panel-label; // 应用面板特定变量
    }
    :deep(.el-empty__image) {
      width: 80px; // 缩小empty图片
    }
  }
  .loading-container {
    padding: 1rem;
  }
}

// Dropdown menu item styling (globally for this component's dropdowns if needed)
:deep(.el-dropdown-menu__item) {
  font-size: $font-size-panel-label; // 应用面板特定变量
  padding: 0.25rem 1rem;
  &:hover {
    background-color: rgba($primary-color, 0.1);
    color: $primary-color;
  }
  &.is-active,
  &.selected {
    // Element Plus might use different classes for active/selected
    background-color: rgba($primary-color, 0.2);
    color: $primary-color;
    font-weight: bold;
  }
}

// 自定义 el-skeleton 样式
:deep(.el-skeleton) {
  .el-skeleton__item {
    background-color: $bg-medium; // 使用中等背景色作为骨架项的基础色
    border-radius: 2px; // 轻微的圆角
  }

  // 针对动画效果的颜色调整
  &.is-animated {
    .el-skeleton__item {
      // Element Plus 动画使用 background-image 实现闪烁效果
      // 将默认的浅色渐变替换为基于主题色的渐变
      // 从 $bg-medium -> $bg-light (更亮一些用于闪烁) -> $bg-medium
      background-image: linear-gradient(90deg, $bg-medium 25%, $bg-light 37%, $bg-medium 63%);
      background-size: 400% 100%; // 这是Element Plus动画的典型background-size
    }
  }
}
</style>
