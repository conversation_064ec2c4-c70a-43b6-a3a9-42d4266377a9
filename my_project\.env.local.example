# .env.local 示例文件
# 复制此文件为 .env.local 并根据本地开发需求修改配置
# .env.local 文件会覆盖 .env 中的同名配置

# 开发环境特定配置
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 本地数据库配置（如果与 .env 不同）
# 数据库配置 - PostgreSQL
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=123
DB_NAME=WRJ2
DB_CONN_MAX_AGE=60

# 本地 Redis 配置（如果与 .env 不同）
# REDIS_HOST=localhost
# CELERY_BROKER_URL=redis://localhost:6379/0
# CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 本地许可证路径（如果与 .env 不同）
WINDOWS_LICENSE_PATH= 你的windows许可证路径
LINUX_LICENSE_PATH= 你的linux许可证路径

# 开发环境特定的业务配置
# AUTH_TOKEN_AGE=86400
# IS_USE_VERIFICATION_CODE=False
