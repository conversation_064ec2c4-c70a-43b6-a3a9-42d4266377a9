<script setup lang="ts">
import { ref, computed } from 'vue'
import { useTaskStore } from '@/stores/taskStore'
import { useUIStore } from '@/stores/uiStore'

// 使用Store
const taskStore = useTaskStore()
const uiStore = useUIStore()

// 计算属性：判断当前是否在创建任务状态
const isCreatingTask = computed(() => {
  return taskStore.currentViewMode === 'create'
})

// 计算属性：按钮文本
const createTaskButtonText = computed(() => {
  return isCreatingTask.value ? '退出' : '新建任务'
})

// 无人机状态数据
interface DroneStatus {
  name: string
  count: number
  color: string
}

const droneStatuses = ref<DroneStatus[]>([
  { name: '在线', count: 4, color: '#4CAF50' },
  { name: '工作中', count: 1, color: '#2196F3' },
  { name: '离线', count: 3, color: '#9E9E9E' },
  { name: '异常', count: 0, color: '#F44336' },
  { name: '维护', count: 2, color: '#FF9800' },
])

// 功能按钮数据
const functionButtons = ref<string[]>(['空城', '全景', '三维', '正射', '图层'])

// 事件处理
const handleCreateTaskToggle = () => {
  if (isCreatingTask.value) {
    // 当前在创建任务状态，点击退出
    console.log('退出创建任务')
    taskStore.cancelCreateTask()
  } else {
    // 当前不在创建任务状态，点击新建任务
    console.log('点击新建任务')
    // 首先切换到飞行任务tab
    uiStore.switchToFlightTaskTab()
    // 然后触发任务Store的创建任务流程
    taskStore.startCreateTask()
  }
}

const handleFunctionClick = (buttonName: string) => {
  console.log('点击功能按钮:', buttonName)
}
</script>

<template>
  <div class="bottom-panel">
    <!-- 第一行：新建任务按钮 -->
    <div class="create-task-row">
      <div
        class="create-task-btn"
        :class="{ 'exit-mode': isCreatingTask }"
        @click="handleCreateTaskToggle"
      >
        {{ createTaskButtonText }}
      </div>
    </div>

    <!-- 第二行：无人机状态标签 -->
    <div class="status-row">
      <el-tag
        v-for="status in droneStatuses"
        :key="status.name"
        size="small"
        effect="plain"
        class="status-tag"
        :style="{ '--tag-color': status.color }"
      >
        {{ status.name }} {{ status.count }}
      </el-tag>
    </div>

    <!-- 第三行：功能按钮 -->
    <div class="function-row">
      <el-button
        v-for="btnName in functionButtons"
        :key="btnName"
        size="small"
        plain
        class="function-btn"
        @click="handleFunctionClick(btnName)"
      >
        {{ btnName }}
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use 'sass:color';
.bottom-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

// 第一行：新建任务按钮
.create-task-row {
  margin-bottom: 0.8rem; // 与下面两行拉开距离

  .create-task-btn {
    padding: 0.5rem 1.5rem;
    font-size: $font-size-panel-normal;
    font-weight: 500;
    color: rgba(255, 193, 7, 0.9);
    background: rgba(6, 31, 56, 0.5);
    border: 1px solid rgba(255, 193, 7, 0.6);
    border-radius: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      color: rgba(255, 193, 7, 1);
      border-color: rgba(255, 193, 7, 0.8);
      background: rgba(6, 31, 56, 0.6);
      box-shadow: 0 0 0.8rem rgba(255, 193, 7, 0.4);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    // 退出模式样式
    &.exit-mode {
      color: rgba(244, 67, 54, 0.9);
      border-color: rgba(244, 67, 54, 0.6);

      &:hover {
        color: rgba(244, 67, 54, 1);
        border-color: rgba(244, 67, 54, 0.8);
        box-shadow: 0 0 0.8rem rgba(244, 67, 54, 0.4);
      }
    }
  }
}

// 第二行：状态标签
.status-row {
  display: flex;
  gap: 0.5rem;
  align-items: center;

  .status-tag {
    font-size: $font-size-panel-label;
    font-weight: 400;
    background: rgba(6, 31, 56, 0.5) !important;
    border: 1px solid var(--tag-color) !important;
    color: var(--tag-color) !important;

    :deep(.el-tag__content) {
      font-size: $font-size-panel-label;
    }
  }
}

// 第三行：功能按钮
.function-row {
  display: flex;
  gap: 0.7rem;
  align-items: center;
  color: $text-secondary !important;

  .function-btn {
    font-size: $font-size-panel-label;
    padding: 0.3rem 0.6rem;
    background: rgba(6, 31, 56, 0.8) !important;
    border-color: rgba($primary-color, 0.4) !important;
    color: $text-default !important;

    :deep(.el-button__text) {
      font-size: $font-size-panel-label;
    }

    &:hover {
      background: rgba(6, 31, 56, 0.9) !important;
      border-color: rgba($primary-color, 0.6) !important;
      color: $primary-color !important;
      box-shadow: 0 0 0.3rem rgba($primary-color, 0.4);
    }
  }
}
</style>
