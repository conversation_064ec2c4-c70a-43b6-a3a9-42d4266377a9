/**
 * 时间日期相关的组合函数
 */
import { ref, onMounted, onUnmounted } from 'vue'

/**
 * 实时时间显示组合函数
 */
export function useCurrentTime() {
  const currentTime = ref('')
  let timer: number | null = null

  // 格式化时间（标准格式）
  const formatTime = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  // 更新时间
  const updateTime = () => {
    currentTime.value = formatTime(new Date())
  }

  // 启动定时器
  const startTimer = () => {
    updateTime() // 立即更新一次
    timer = setInterval(updateTime, 1000)
  }

  // 停止定时器
  const stopTimer = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }

  // 组件挂载时启动定时器
  onMounted(() => {
    startTimer()
  })

  // 组件卸载时清理定时器
  onUnmounted(() => {
    stopTimer()
  })

  return {
    currentTime,
    startTimer,
    stopTimer,
    updateTime,
  }
}

/**
 * 中文格式的实时时间显示组合函数
 * 专门用于Dashboard头部显示
 */
export function useChineseDateTime() {
  const chineseTime = ref('')
  let timer: number | null = null

  // 格式化中文时间
  const formatChineseTime = (date: Date): string => {
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    // 获取星期
    const weekDays = ['日', '一', '二', '三', '四', '五', '六']
    const weekDay = weekDays[date.getDay()]

    return `${year}年${month}月${day}日 星期${weekDay} ${hours}:${minutes}:${seconds}`
  }

  // 更新时间
  const updateTime = () => {
    chineseTime.value = formatChineseTime(new Date())
  }

  // 启动定时器
  const startTimer = () => {
    updateTime() // 立即更新一次
    timer = window.setInterval(updateTime, 1000)
  }

  // 停止定时器
  const stopTimer = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }

  // 组件挂载时启动定时器
  onMounted(() => {
    startTimer()
  })

  // 组件卸载时清理定时器
  onUnmounted(() => {
    stopTimer()
  })

  return {
    chineseTime,
    startTimer,
    stopTimer,
    updateTime,
  }
}

/**
 * 日期格式化工具函数
 */
export function formatDate(
  date: Date | string | number,
  format: string = 'YYYY-MM-DD HH:mm:ss',
): string {
  const d = new Date(date)

  const formatMap: Record<string, string> = {
    YYYY: d.getFullYear().toString(),
    MM: String(d.getMonth() + 1).padStart(2, '0'),
    DD: String(d.getDate()).padStart(2, '0'),
    HH: String(d.getHours()).padStart(2, '0'),
    mm: String(d.getMinutes()).padStart(2, '0'),
    ss: String(d.getSeconds()).padStart(2, '0'),
  }

  let result = format
  Object.entries(formatMap).forEach(([key, value]) => {
    result = result.replace(new RegExp(key, 'g'), value)
  })

  return result
}

/**
 * 相对时间显示（如：2小时前、3天前）
 */
export function getRelativeTime(date: Date | string | number): string {
  const now = new Date()
  const target = new Date(date)
  const diff = now.getTime() - target.getTime()

  const minute = 60 * 1000
  const hour = minute * 60
  const day = hour * 24
  const week = day * 7
  const month = day * 30
  const year = day * 365

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`
  } else {
    return `${Math.floor(diff / year)}年前`
  }
}
