<template>
  <div class="create-task-form">
    <!-- 表单头部 -->
    <div class="form-header">
      <div class="header-left">
        <el-button size="small" :icon="ArrowLeft" @click="handleBack" class="back-btn">
          新建任务
        </el-button>
      </div>
    </div>

    <!-- 表单内容区域 - 暂时用占位元素 -->
    <div class="form-content">
      <el-scrollbar class="form-scrollbar">
        <el-form
          ref="formRef"
          :model="editableFormData"
          :rules="formRules"
          label-width="3rem"
          label-position="left"
          size="small"
          class="task-form"
        >
          <!-- 巡检任务部分 -->
          <div class="form-section">
            <div class="section-title">巡检地块</div>
            <div class="section-content">
              <el-form-item label="名称:" prop="name">
                <el-input
                  :model-value="props.formData?.name || ''"
                  placeholder="从地图拾取的任务名称"
                  readonly
                  disabled
                />
              </el-form-item>
              <el-form-item label="形状:" prop="shape">
                <el-select
                  :model-value="props.formData?.shape || ''"
                  placeholder="从地图拾取的形状"
                  disabled
                >
                  <el-option label="面状" value="面状" />
                  <el-option label="线状" value="线状" />
                  <el-option label="点状" value="点状" />
                </el-select>
              </el-form-item>
              <el-form-item label="周长:" prop="perimeter">
                <el-input
                  :model-value="props.formData?.perimeter?.toString() || ''"
                  placeholder="从地图拾取的周长"
                  readonly
                  disabled
                />
              </el-form-item>
              <el-form-item label="面积:" prop="area">
                <el-input
                  :model-value="props.formData?.area?.toString() || ''"
                  placeholder="从地图拾取的面积(km²)"
                  readonly
                  disabled
                />
              </el-form-item>
              <el-form-item label="中心经度:" prop="centerLongitude">
                <el-input
                  :model-value="props.formData?.centerLongitude?.toString() || ''"
                  placeholder="从地图拾取的中心经度"
                  readonly
                  disabled
                />
              </el-form-item>
              <el-form-item label="中心纬度:" prop="centerLatitude">
                <el-input
                  :model-value="props.formData?.centerLatitude?.toString() || ''"
                  placeholder="从地图拾取的中心纬度"
                  readonly
                  disabled
                />
              </el-form-item>
              <el-form-item label="位置详情:" prop="locationDetail">
                <el-input
                  :model-value="props.formData?.locationDetail || ''"
                  placeholder="从地图拾取的位置详情"
                  type="textarea"
                  :rows="2"
                  readonly
                  disabled
                />
              </el-form-item>
            </div>
          </div>

          <!-- 巡检用途部分 -->
          <div class="form-section">
            <div class="section-title">巡检用途</div>
            <div class="section-content">
              <el-form-item label="用途:" prop="purpose">
                <el-select v-model="editableFormData.purpose" placeholder="请选择用途">
                  <el-option label="拍摄" value="拍摄" />
                  <el-option label="全景" value="全景" />
                  <el-option label="三维" value="三维" />
                  <el-option label="正射" value="正射" />
                  <el-option label="视频" value="视频" />
                </el-select>
              </el-form-item>
              <el-form-item label="紧急度:" prop="urgency">
                <el-select v-model="editableFormData.urgency" placeholder="请选择紧急度">
                  <el-option label="紧急" value="紧急" />
                  <el-option label="不紧急" value="不紧急" />
                  <el-option label="一般" value="一般" />
                </el-select>
              </el-form-item>
              <el-form-item label="备注:">
                <el-input
                  v-model="editableFormData.remark"
                  placeholder="请输入备注信息"
                  type="textarea"
                  :rows="2"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </div>
          </div>

          <!-- 无人机状态部分 -->
          <div class="form-section">
            <div class="section-title">
              {{ props.taskDroneEstimate?.droneName || '未选择无人机' }}
            </div>
            <div class="section-content">
              <!-- 无人机预计信息 -->
              <div class="drone-info" v-if="props.taskDroneEstimate">
                <div class="drone-item">
                  <span class="drone-label">状态:</span>
                  <span class="drone-value">{{ props.taskDroneEstimate.status }}</span>
                </div>
                <div class="drone-item">
                  <span class="drone-label">电量:</span>
                  <span class="drone-value">{{ props.taskDroneEstimate.batteryLevel }}%</span>
                </div>
                <div class="drone-item">
                  <span class="drone-label">机巢距离:</span>
                  <span class="drone-value">{{ props.taskDroneEstimate.nestDistance }}km</span>
                </div>
                <div class="drone-item">
                  <span class="drone-label">预计耗时:</span>
                  <span class="drone-value">{{ props.taskDroneEstimate.estimatedTime }}分</span>
                </div>
                <div class="drone-item">
                  <span class="drone-label">预计里程:</span>
                  <span class="drone-value">{{ props.taskDroneEstimate.estimatedMileage }}km</span>
                </div>
                <div class="drone-item">
                  <span class="drone-label">飞行速度:</span>
                  <span class="drone-value">{{ props.taskDroneEstimate.flightSpeed }}米/秒</span>
                </div>
                <div class="drone-item">
                  <span class="drone-label">照片数量:</span>
                  <span class="drone-value">{{ props.taskDroneEstimate.photoCount }}张</span>
                </div>
              </div>
              <div v-else class="drone-info">
                <div class="drone-item">
                  <span class="drone-label">状态:</span>
                  <span class="drone-value">未选择无人机</span>
                </div>
              </div>
            </div>
          </div>
        </el-form>
      </el-scrollbar>
    </div>

    <!-- 底部操作按钮 -->
    <div class="form-footer">
      <el-button size="small" @click="handleCancel">取消</el-button>
      <el-button type="primary" size="small" @click="handleSubmit">提交任务</el-button>
      <el-button type="success" size="small" @click="handleQuickReview">一键审核</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ArrowLeft } from '@element-plus/icons-vue'
import type { CreateTaskRequest, TaskPurpose, TaskUrgency, TaskDroneEstimate } from '@/types/ui'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps<{
  formData?: Partial<CreateTaskRequest>
  loading?: boolean
  taskDroneEstimate?: TaskDroneEstimate | null // 任务无人机预计信息
}>()

console.log('props', props)

// Emits
const emit = defineEmits<{
  submit: [data: CreateTaskRequest]
  cancel: []
  back: []
  quickReview: [data: CreateTaskRequest]
}>()

// 表单引用
const formRef = ref<FormInstance>()

// 表单验证规则（只验证可编辑字段）
const formRules: FormRules = {
  purpose: [{ required: true, message: '请选择用途', trigger: 'change' }],
  urgency: [{ required: true, message: '请选择紧急度', trigger: 'change' }],
  // 备注字段不设置必填验证
  // 地图拾取的字段不需要验证，因为它们是只读的
}

// 可编辑的表单数据（巡检用途部分）
const editableFormData = ref({
  purpose: '拍摄' as TaskPurpose,
  urgency: '不紧急' as TaskUrgency,
  remark: '',
  selectedDroneId: '', // 无人机选择
})

// 监听props变化，初始化可编辑数据
watch(
  () => props.formData,
  (newFormData) => {
    if (newFormData) {
      editableFormData.value.purpose = newFormData.purpose || '拍摄'
      editableFormData.value.urgency = newFormData.urgency || '不紧急'
      editableFormData.value.remark = newFormData.remark || ''
      editableFormData.value.selectedDroneId = newFormData.selectedDroneId || ''
    }
  },
  { immediate: true },
)

// 合并后的完整表单数据
const mergedFormData = computed(() => {
  return {
    // 从props获取的地图数据（只读）
    name: props.formData?.name || '',
    shape: props.formData?.shape || '面状',
    perimeter: props.formData?.perimeter || 0,
    area: props.formData?.area || 0,
    centerLongitude: props.formData?.centerLongitude || 0,
    centerLatitude: props.formData?.centerLatitude || 0,
    locationDetail: props.formData?.locationDetail || '',

    // 用户可编辑的数据
    purpose: editableFormData.value.purpose,
    urgency: editableFormData.value.urgency,
    remark: editableFormData.value.remark,
    selectedDroneId: editableFormData.value.selectedDroneId,
  } as CreateTaskRequest
})

// 事件处理
const handleBack = () => {
  console.log('返回任务列表')
  emit('back')
}

const handleCancel = () => {
  console.log('取消新建任务')
  emit('cancel')
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    // 验证可编辑字段
    await formRef.value.validate()

    // 验证props中的必要数据
    if (!props.formData?.name) {
      ElMessage.error('任务名称不能为空')
      return
    }
    if (!props.formData?.selectedDroneId) {
      ElMessage.error('请选择无人机')
      return
    }

    console.log('提交任务', mergedFormData.value)

    // 通过emit向父组件传递提交事件
    emit('submit', mergedFormData.value)
    ElMessage.success('任务提交成功')
  } catch (error) {
    console.log('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否完整')
  }
}

const handleQuickReview = async () => {
  if (!formRef.value) return

  try {
    // 验证可编辑字段
    await formRef.value.validate()

    // 验证props中的必要数据
    if (!props.formData?.name) {
      ElMessage.error('任务名称不能为空')
      return
    }
    if (!props.formData?.selectedDroneId) {
      ElMessage.error('请选择无人机')
      return
    }

    console.log('一键审核', mergedFormData.value)

    // 通过emit向父组件传递一键审核事件
    emit('quickReview', mergedFormData.value)
    ElMessage.success('一键审核提交成功')
  } catch (error) {
    console.log('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否完整')
  }
}
</script>

<style scoped lang="scss">
.create-task-form {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba($bg-card, 0.1);
  border-radius: $border-radius-base;
}

// 表单头部
.form-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid $border-color-light;

  .back-btn {
    background: transparent !important;
    border: none !important;
    color: $text-active !important;
    font-size: $font-size-panel-title;
    padding: 0 !important;

    &:hover {
      color: $primary-color !important;
    }
  }
}

// 表单内容
.form-content {
  flex: 1;
  min-height: 0;

  .form-scrollbar {
    height: 100%;
  }

  .task-form {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
}

// 表单分组
.form-section {
  .section-title {
    font-size: $font-size-panel-normal;
    color: $text-active;
    font-weight: 500;
    margin-bottom: 0.5rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid rgba($primary-color, 0.3);
  }

  .section-content {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
  }
}

// Element Plus 表单样式覆盖
.task-form {
  :deep(.el-form-item) {
    margin-bottom: 0.4rem;

    .el-form-item__label {
      font-size: $font-size-panel-label;
      color: $text-secondary;
      padding: 0;
      line-height: 1.5rem;
    }

    .el-form-item__content {
      line-height: 1.5rem;
    }
  }

  :deep(.el-input) {
    .el-input__wrapper {
      background: rgba($bg-medium, 0.3);
      border: 1px solid $border-color-light;
      border-radius: $border-radius-small;
      box-shadow: none;

      &:hover {
        border-color: rgba($primary-color, 0.4);
      }

      &.is-focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      }
    }

    .el-input__inner {
      color: $text-default;
      font-size: $font-size-panel-label;

      &::placeholder {
        color: $text-inactive;
      }
    }
  }

  :deep(.el-select) {
    .el-select__wrapper {
      background: rgba($bg-medium, 0.3);
      border: 1px solid $border-color-light;
      border-radius: $border-radius-small;
      box-shadow: none;

      &:hover {
        border-color: rgba($primary-color, 0.4);
      }

      &.is-focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      }
    }

    .el-select__selected-item {
      color: $text-default;
      font-size: $font-size-panel-label;
    }

    .el-select__placeholder {
      color: $text-inactive;
      font-size: $font-size-panel-label;
    }
  }

  :deep(.el-textarea) {
    .el-textarea__inner {
      background: rgba($bg-medium, 0.3);
      border: 1px solid $border-color-light;
      border-radius: $border-radius-small;
      color: $text-default;
      font-size: $font-size-panel-label;
      box-shadow: none;

      &:hover {
        border-color: rgba($primary-color, 0.4);
      }

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      }

      &::placeholder {
        color: $text-inactive;
      }
    }
  }
}

// 无人机信息显示
.drone-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 0.5rem;

  .drone-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background: rgba($bg-medium, 0.2);
    border-radius: $border-radius-small;
    font-size: $font-size-panel-label;

    .drone-label {
      color: $text-secondary;
    }

    .drone-value {
      color: $text-default;
      font-weight: 500;
    }
  }
}

// 占位元素样式（保留用于参考）
.placeholder-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;

  .placeholder-label {
    flex-shrink: 0;
    width: 4rem;
    font-size: $font-size-panel-label;
    color: $text-secondary;
  }

  .placeholder-input,
  .placeholder-select,
  .placeholder-textarea {
    flex: 1;
    padding: 0.25rem 0.5rem;
    background: rgba($bg-medium, 0.3);
    border: 1px solid $border-color-light;
    border-radius: $border-radius-small;
    font-size: $font-size-panel-label;
    color: $text-default;
    min-height: 1.5rem;
    display: flex;
    align-items: center;
  }

  .placeholder-textarea {
    min-height: 3rem;
    align-items: flex-start;
    padding-top: 0.5rem;
  }

  .placeholder-select {
    cursor: pointer;

    &:hover {
      border-color: rgba($primary-color, 0.4);
    }
  }
}

// 无人机列表
.drone-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  .drone-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background: rgba($bg-medium, 0.2);
    border-radius: $border-radius-small;
    font-size: $font-size-panel-label;

    .drone-name {
      color: $text-secondary;
    }

    .drone-status {
      color: $text-default;
      font-weight: 500;
    }
  }
}

// 表单底部
.form-footer {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-top: 1px solid $border-color-light;
}
</style>
