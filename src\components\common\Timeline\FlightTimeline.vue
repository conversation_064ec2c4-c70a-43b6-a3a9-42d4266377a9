<template>
  <div class="flight-timeline">
    <el-timeline>
      <el-timeline-item
        v-for="(task, index) in tasks"
        :key="index"
        :type="getTaskTypeColor(task.status)"
        :hollow="task.status === 'planned'"
        :timestamp="task.time"
        :icon="getTaskStatusIcon(task.status)"
        placement="top"
      >
        <div class="task-card">
          <div class="task-header">
            <span class="task-title">{{ task.title }}</span>
            <span class="task-tag" :class="task.status">{{ getStatusText(task.status) }}</span>
          </div>
          <!-- 暂时不渲染卡片内容 -->
          <!-- <div class="task-info" v-if="task.description">
            {{ task.description }}
          </div> -->
          <div class="task-meta">
            <span v-if="task.location" class="location">
              <el-icon><Location /></el-icon>
              {{ task.location }}
            </span>
            <span v-if="task.photoCount !== undefined" class="photo-count">
              <el-icon><Picture /></el-icon>
              {{ task.photoCount }}
            </span>
            <span v-if="task.alertCount !== undefined" class="alert-count">
              <el-icon><Warning /></el-icon>
              {{ task.alertCount }}
            </span>
            <!-- <span v-if="task.duration" class="duration">
              <el-icon><Timer /></el-icon>
              {{ task.duration }}
            </span> -->
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script lang="ts" setup name="FlightTimeline">
import { Check, Loading, Calendar, Location, Picture, Warning } from '@element-plus/icons-vue'

// 定义任务状态类型
type TaskStatus = 'planned' | 'in-progress' | 'completed'

// 定义任务接口
interface Task {
  title: string
  description?: string
  time: string
  status: TaskStatus
  location?: string
  duration?: string
  photoCount?: number
  alertCount?: number
}

// 组件属性
defineProps<{
  tasks: Task[]
}>()

// 根据任务状态获取图标
const getTaskStatusIcon = (status: TaskStatus) => {
  switch (status) {
    case 'completed':
      return Check
    case 'in-progress':
      return Loading
    case 'planned':
    default:
      return Calendar
  }
}

// 根据任务状态获取颜色类型
const getTaskTypeColor = (status: TaskStatus) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'in-progress':
      return 'primary'
    case 'planned':
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: TaskStatus) => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'in-progress':
      return '执行中'
    case 'planned':
    default:
      return '已计划'
  }
}
</script>

<style scoped lang="scss">
.flight-timeline {
  width: 100%;

  :deep(.el-timeline) {
    padding: 0;

    .el-timeline-item {
      padding-bottom: 1.5rem;

      .el-timeline-item__tail {
        border-left: 2px solid rgba(0, 255, 254, 0.15);
      }

      .el-timeline-item__node {
        background-color: transparent;
        border-color: #00fffe;
        border-width: 2px;
      }

      .el-timeline-item__icon {
        color: #00fffe;
        font-size: 1rem;

        &.el-icon.is-info {
          color: #909399;
        }

        &.el-icon.is-primary {
          color: #00fffe;
        }

        &.el-icon.is-success {
          color: #67c23a;
        }
      }

      .el-timeline-item__wrapper {
        padding-left: 1.5rem;

        .el-timeline-item__timestamp {
          color: #00fffe;
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
          display: inline-block;
          background: rgba(0, 255, 254, 0.1);
          padding: 2px 8px;
          border-radius: 4px;
        }
      }

      .task-card {
        background: rgba(0, 255, 254, 0.1);
        border: 1px solid rgba(0, 255, 254, 0.15);
        border-radius: 3px;
        padding: 0.5rem 0.6rem;
        transition: all 0.25s ease;
        position: relative;
        margin-bottom: 0.2rem;

        &:hover {
          background: rgba(0, 255, 254, 0.2);
          border-color: rgba(0, 255, 254, 0.3);
          transform: translateY(-1px);
          box-shadow: 0 3px 8px rgba(0, 255, 254, 0.15);
        }

        .task-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.3rem;

          .task-title {
            color: $text-default;
            font-size: 0.7rem;
            font-weight: 500;
          }

          .task-tag {
            font-size: 0.6rem;
            padding: 1px 4px;
            border-radius: 2px;
            font-weight: 500;

            &.completed {
              background: rgba(103, 194, 58, 0.15);
              color: #67c23a;
              border: 1px solid rgba(103, 194, 58, 0.25);
            }

            &.in-progress {
              background: rgba(0, 255, 254, 0.1);
              color: #00fffe;
              border: 1px solid rgba(0, 255, 254, 0.2);
            }

            &.planned {
              background: rgba(144, 147, 153, 0.15);
              color: #909399;
              border: 1px solid rgba(144, 147, 153, 0.25);
            }
          }
        }

        .task-info {
          color: rgba($text-default, 0.7);
          font-size: 0.75rem;
          line-height: 1.3;
          margin-bottom: 0.4rem;
        }

        .task-meta {
          display: flex;
          gap: 0.6rem;
          font-size: 0.65rem;
          color: rgba($text-default, 0.5);
          margin-top: 0.3rem;

          .location,
          .duration,
          .photo-count,
          .alert-count {
            display: flex;
            align-items: center;
            gap: 2px;
            background: rgba(0, 0, 0, 0.2);
            padding: 1px 4px;
            border-radius: 3px;

            .el-icon {
              font-size: 0.7rem;
            }
          }

          .location .el-icon {
            color: rgba(0, 255, 254, 0.7);
          }

          .duration .el-icon {
            color: rgba($text-default, 0.6);
          }

          .photo-count .el-icon {
            color: rgba($text-default, 0.7);
          }

          .alert-count .el-icon {
            color: rgba(230, 162, 60, 0.7);
          }
        }
      }
    }
  }
}
</style>
