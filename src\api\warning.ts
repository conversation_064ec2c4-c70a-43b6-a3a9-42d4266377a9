/**
 * 预警信息相关API接口
 */
import type { WarningDetailData } from '@/types/ui'

/**
 * 预警信息API - 后端只有一个查询预警详情列表的接口
 */
export const warningApi = {
  /**
   * 获取预警详情列表（分页查询）- 后端唯一接口
   * @param page 页码
   * @param pageSize 每页数量
   * @param startDate 开始时间
   * @param endDate 结束时间
   */
  getWarningDetailList: async (
    page: number = 1,
    pageSize: number = 10,
    startDate?: Date,
    endDate?: Date,
  ): Promise<{ data: WarningDetailData[]; total: number }> => {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 返回模拟详情数据列表
    return generateMockWarningDetailList(page, pageSize, startDate, endDate)
  },

  /**
   * 更新预警状态
   * @param warningId 预警ID
   * @param status 新状态
   */
  updateWarningStatus: async (warningId: string, status: string): Promise<void> => {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 300))

    console.log(`更新预警 ${warningId} 状态为: ${status}`)
  },

  /**
   * 设置为典型案例
   * @param warningId 预警ID
   */
  setAsTypical: async (warningId: string): Promise<void> => {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 300))

    console.log(`设置预警 ${warningId} 为典型案例`)
  },
}

/**
 * 生成模拟预警详情列表数据
 * @param page 页码
 * @param pageSize 每页数量
 * @param startDate 开始时间
 * @param endDate 结束时间
 */
function generateMockWarningDetailList(
  page: number = 1,
  pageSize: number = 10,
  startDate?: Date,
  endDate?: Date,
): { data: WarningDetailData[]; total: number } {
  // 完整的模拟数据
  const allData: WarningDetailData[] = [
    {
      id: 'warning_001',
      type: '1', // 暴露垃圾
      status: '新发现',
      detectionTime: '2024-06-20 14:30:25',
      inspectionRoute: '【全覆盖】常山01-辉埠工业园',
      location: {
        name: '狮子口水库东侧',
        address: '浙江省衢州市常山县狮子口水库附近',
        coordinates: {
          longitude: 118.5123,
          latitude: 28.9012,
          altitude: 156,
        },
      },
      imageTimeline: [
        {
          id: 'img_001_1',
          imageUrl: 'https://picsum.photos/400/300?random=1',
          captureTime: '2024-06-20 14:30:25',
          description: 'AI检测发现的暴露垃圾',
          imageType: '检测图',
        },
        {
          id: 'img_001_2',
          imageUrl: 'https://picsum.photos/400/300?random=2',
          captureTime: '2024-06-20 15:00:00',
          description: '处理前现场情况',
          imageType: '处理前',
        },
      ],
      processTimeline: [
        {
          id: 'process_001_1',
          title: '预警发现',
          status: '新发现',
          timestamp: '2024-06-20 14:30:25',
          operator: '系统AI',
          description: 'AI巡检系统自动检测发现暴露垃圾',
        },
      ],
      isTypical: false,
    },
    {
      id: 'warning_002',
      type: '2', // 违章建筑
      status: '反馈处理',
      detectionTime: '2024-06-20 13:15:10',
      inspectionRoute: '【重点区域】城区巡检路线',
      location: {
        name: '城关镇解放街',
        address: '浙江省衢州市常山县城关镇解放街',
        coordinates: {
          longitude: 118.5089,
          latitude: 28.8956,
          altitude: 145,
        },
      },
      imageTimeline: [
        {
          id: 'img_002_1',
          imageUrl: 'https://picsum.photos/400/300?random=3',
          captureTime: '2024-06-20 13:15:10',
          description: '违章建筑检测图',
          imageType: '检测图',
        },
        {
          id: 'img_002_2',
          imageUrl: 'https://picsum.photos/400/300?random=4',
          captureTime: '2024-06-20 14:00:00',
          description: '现场勘查照片',
          imageType: '处理前',
        },
      ],
      processTimeline: [
        {
          id: 'process_002_1',
          title: '预警发现',
          status: '新发现',
          timestamp: '2024-06-20 13:15:10',
          operator: '系统AI',
          description: 'AI巡检发现疑似违章建筑',
        },
        {
          id: 'process_002_2',
          title: '现场核实',
          status: '反馈处理',
          timestamp: '2024-06-20 14:00:00',
          operator: '城管执法队',
          description: '现场核实确认为违章建筑，已下达整改通知',
        },
      ],
      isTypical: true,
    },
    {
      id: 'warning_003',
      type: '3', // 水域污染
      status: '已办结',
      detectionTime: '2024-06-20 11:45:33',
      inspectionRoute: '【环保专线】水域监测路线',
      location: {
        name: '钱塘江支流监测点',
        address: '浙江省衢州市常山县钱塘江支流',
        coordinates: {
          longitude: 118.4967,
          latitude: 28.8834,
          altitude: 98,
        },
      },
      imageTimeline: [
        {
          id: 'img_003_1',
          imageUrl: 'https://picsum.photos/400/300?random=5',
          captureTime: '2024-06-20 11:45:33',
          description: '水域污染检测',
          imageType: '检测图',
        },
        {
          id: 'img_003_2',
          imageUrl: 'https://picsum.photos/400/300?random=6',
          captureTime: '2024-06-20 15:30:00',
          description: '清理完成后',
          imageType: '处理后',
        },
      ],
      processTimeline: [
        {
          id: 'process_003_1',
          title: '预警发现',
          status: '新发现',
          timestamp: '2024-06-20 11:45:33',
          operator: '系统AI',
          description: '检测到水域异常污染',
        },
        {
          id: 'process_003_2',
          title: '处理完成',
          status: '已办结',
          timestamp: '2024-06-20 16:30:00',
          operator: '环保部门',
          description: '污染源已清理，水质恢复正常',
        },
      ],
      isTypical: false,
      completedAt: '2024-06-20 16:30:00',
    },
    {
      id: 'warning_004',
      type: '4', // 道路损坏
      status: '新发现',
      detectionTime: '2024-06-20 09:20:15',
      inspectionRoute: '【基础设施】市政道路巡检',
      location: {
        name: '解放路与人民路交叉口',
        address: '浙江省衢州市常山县解放路与人民路交叉口',
        coordinates: {
          longitude: 118.5156,
          latitude: 28.8978,
          altitude: 152,
        },
      },
      imageTimeline: [
        {
          id: 'img_004_1',
          imageUrl: 'https://picsum.photos/400/300?random=7',
          captureTime: '2024-06-20 09:20:15',
          description: '路面坑洼检测',
          imageType: '检测图',
        },
      ],
      processTimeline: [
        {
          id: 'process_004_1',
          title: '预警发现',
          status: '新发现',
          timestamp: '2024-06-20 09:20:15',
          operator: '系统AI',
          description: '检测到路面存在坑洼损坏',
        },
      ],
      isTypical: false,
    },
    {
      id: 'warning_005',
      type: '8', // 松材线虫病
      status: '反馈处理',
      detectionTime: '2024-06-19 16:45:20',
      inspectionRoute: '【生态保护】富足山林场巡检',
      location: {
        name: '富足山林场A区',
        address: '浙江省衢州市常山县富足山林场A区',
        coordinates: {
          longitude: 118.4823,
          latitude: 28.9234,
          altitude: 245,
        },
      },
      imageTimeline: [
        {
          id: 'img_005_1',
          imageUrl: 'https://picsum.photos/400/300?random=8',
          captureTime: '2024-06-19 16:45:20',
          description: '疑似感染松材线虫病的松树',
          imageType: '检测图',
        },
        {
          id: 'img_005_2',
          imageUrl: 'https://picsum.photos/400/300?random=9',
          captureTime: '2024-06-20 08:30:00',
          description: '专家现场确认',
          imageType: '处理前',
        },
      ],
      processTimeline: [
        {
          id: 'process_005_1',
          title: '预警发现',
          status: '新发现',
          timestamp: '2024-06-19 16:45:20',
          operator: '系统AI',
          description: 'AI识别疑似松材线虫病感染树木',
        },
        {
          id: 'process_005_2',
          title: '专家确认',
          status: '反馈处理',
          timestamp: '2024-06-20 08:30:00',
          operator: '林业专家组',
          description: '专家现场确认为松材线虫病，制定处理方案',
        },
      ],
      isTypical: true,
    },
    {
      id: 'warning_006',
      type: '6', // 车辆违停
      status: '已办结',
      detectionTime: '2024-06-20 12:30:45',
      inspectionRoute: '【交通管理】城区主干道巡检',
      location: {
        name: '常山大道商业街段',
        address: '浙江省衢州市常山县常山大道商业街段',
        coordinates: {
          longitude: 118.5067,
          latitude: 28.8912,
          altitude: 148,
        },
      },
      imageTimeline: [
        {
          id: 'img_006_1',
          imageUrl: 'https://picsum.photos/400/300?random=10',
          captureTime: '2024-06-20 12:30:45',
          description: '车辆违停占用消防通道',
          imageType: '检测图',
        },
        {
          id: 'img_006_2',
          imageUrl: 'https://picsum.photos/400/300?random=11',
          captureTime: '2024-06-20 13:15:00',
          description: '违停车辆已移走',
          imageType: '处理后',
        },
      ],
      processTimeline: [
        {
          id: 'process_006_1',
          title: '预警发现',
          status: '新发现',
          timestamp: '2024-06-20 12:30:45',
          operator: '系统AI',
          description: '检测到车辆违停占用消防通道',
        },
        {
          id: 'process_006_2',
          title: '处理完成',
          status: '已办结',
          timestamp: '2024-06-20 13:15:00',
          operator: '交警大队',
          description: '违停车辆已移走，消防通道恢复畅通',
        },
      ],
      isTypical: false,
      completedAt: '2024-06-20 13:15:00',
    },
  ]

  // 按时间筛选
  let filteredData = [...allData]
  if (startDate && endDate) {
    filteredData = filteredData.filter((item) => {
      const itemDate = new Date(item.detectionTime)
      return itemDate >= startDate && itemDate <= endDate
    })
  }

  // 分页处理
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const paginatedData = filteredData.slice(start, end)

  return {
    data: paginatedData,
    total: filteredData.length,
  }
}
