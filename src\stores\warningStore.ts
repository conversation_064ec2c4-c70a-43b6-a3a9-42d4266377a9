/**
 * 预警信息状态管理 Store
 * 管理预警列表、详情、筛选等状态
 */
import { defineStore } from 'pinia'
import type { WarningDetailData } from '@/types/ui'
import { warningApi } from '@/api/warning'
import { getWarningTypeName, getAllWarningTypeOptions } from '@/utils/warningTypeMap'

// 视图模式类型
export type WarningViewMode = 'list' | 'detail'

export const useWarningStore = defineStore('warning', {
  state: () => ({
    // 视图模式
    currentViewMode: 'list' as WarningViewMode,

    // 预警详情列表数据（后端返回的完整数据）
    warningDetails: [] as WarningDetailData[],

    // 当前选中的预警详情
    selectedWarning: null as WarningDetailData | null,

    // 筛选条件
    filters: {
      dateRange: null as { startDate: Date; endDate: Date } | null,
      warningType: 'all' as string,
      status: 'all' as string,
    },

    // 分页信息
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    },

    // 加载状态
    loading: false,

    // 错误信息
    error: null as string | null,
  }),

  getters: {
    // 筛选后的预警列表（按类型进行前端筛选）
    filteredWarnings: (state) => {
      let filtered = [...state.warningDetails]

      // 按类型筛选（前端过滤）
      if (state.filters.warningType !== 'all') {
        filtered = filtered.filter((item) => item.type === state.filters.warningType)
      }

      return filtered
    },

    // 转换为列表项数据格式（用于UI展示）
    warningListItems: (state) => {
      // 先按类型筛选
      let filtered = [...state.warningDetails]
      if (state.filters.warningType !== 'all') {
        filtered = filtered.filter((item) => item.type === state.filters.warningType)
      }

      // 转换为列表项格式
      return filtered.map((detail) => ({
        id: detail.id,
        imageUrl: detail.imageTimeline?.[0]?.imageUrl || '',
        type: detail.type,
        status: detail.status,
        detectionTime: detail.detectionTime,
        inspectionRoute: detail.inspectionRoute,
        location: detail.location.address,
        latitude: detail.location.coordinates.latitude,
        longitude: detail.location.coordinates.longitude,
        isTypical: detail.isTypical,
        completedAt: detail.completedAt,
      }))
    },

    // 预警统计（从详情数据中计算）
    warningStats: (state) => {
      const details = state.warningDetails
      return {
        total: details.length,
        newCount: details.filter((w) => w.status === '新发现').length,
        processingCount: details.filter((w) => w.status === '反馈处理').length,
        completedCount: details.filter((w) => w.status === '已办结').length,
        typicalCount: details.filter((w) => w.isTypical).length,
      }
    },

    // 按类型统计（从详情数据中计算）
    warningTypeStats: (state) => {
      const stats: Record<string, number> = {}
      state.warningDetails.forEach((warning) => {
        const typeName = getWarningTypeName(warning.type)
        stats[typeName] = (stats[typeName] || 0) + 1
      })
      return stats
    },

    // 获取所有可用的预警类型选项（从数据中提取）
    availableWarningTypes: (state) => {
      const typeSet = new Set(state.warningDetails.map((w) => w.type))
      const allOptions = getAllWarningTypeOptions()

      return [
        { id: 'all', name: '全部类型', count: state.warningDetails.length },
        ...allOptions
          .filter((option) => typeSet.has(option.code))
          .map((option) => ({
            id: option.code,
            name: option.name,
            count: state.warningDetails.filter((w) => w.type === option.code).length,
          })),
      ]
    },

    // 是否在详情视图
    isDetailView: (state) => state.currentViewMode === 'detail',

    // 是否在列表视图
    isListView: (state) => state.currentViewMode === 'list',

    // 获取当前筛选条件的显示文本
    currentFiltersText: (state) => {
      const filters = []
      if (state.filters.warningType !== 'all') {
        const typeName = getWarningTypeName(state.filters.warningType)
        filters.push(`类型: ${typeName}`)
      }
      if (state.filters.status !== 'all') {
        filters.push(`状态: ${state.filters.status}`)
      }
      if (state.filters.dateRange) {
        filters.push('已设置日期范围')
      }
      return filters.length > 0 ? filters.join(', ') : '无筛选条件'
    },
  },

  actions: {
    // 设置视图模式
    setViewMode(mode: WarningViewMode) {
      this.currentViewMode = mode
    },

    // 切换到列表视图
    switchToList() {
      this.setViewMode('list')
      this.selectedWarning = null
    },

    // 切换到详情视图（从已有数据中获取）
    switchToDetail(warningId: string) {
      const detailData = this.warningDetails.find((w) => w.id === warningId)
      if (detailData) {
        this.selectedWarning = detailData
        this.setViewMode('detail')
      } else {
        this.error = '未找到预警详情数据'
        console.error('未找到预警详情数据:', warningId)
      }
    },

    // 获取预警详情列表
    async fetchWarningDetails() {
      try {
        this.loading = true
        this.error = null

        // 调用API获取详情列表数据
        const result = await warningApi.getWarningDetailList(
          this.pagination.currentPage,
          this.pagination.pageSize,
          this.filters.dateRange?.startDate,
          this.filters.dateRange?.endDate,
        )

        this.warningDetails = result.data
        this.pagination.total = result.total
      } catch (error) {
        this.error = error instanceof Error ? error.message : '获取预警数据失败'
        console.error('获取预警数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 清除筛选条件
    async clearFilters() {
      this.filters = {
        dateRange: null,
        warningType: 'all',
        status: 'all',
      }
      this.pagination.currentPage = 1
      await this.fetchWarningDetails()
    },

    // 设置分页
    setPagination(pagination: Partial<typeof this.pagination>) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 设置当前页并重新获取数据
    async setCurrentPage(page: number) {
      this.setPagination({ currentPage: page })
      await this.fetchWarningDetails()
    },

    // 设置页大小并重新获取数据
    async setPageSize(size: number) {
      this.setPagination({ pageSize: size, currentPage: 1 })
      await this.fetchWarningDetails()
    },

    // 设置加载状态
    setLoading(loading: boolean) {
      this.loading = loading
    },

    // 设置错误信息
    setError(error: string | null) {
      this.error = error
    },

    // 设为典型案例
    async setAsTypical(id: string) {
      try {
        await warningApi.setAsTypical(id)

        // 更新本地数据
        const warningIndex = this.warningDetails.findIndex((w) => w.id === id)
        if (warningIndex !== -1) {
          this.warningDetails[warningIndex].isTypical = true
        }

        // 如果当前选中的预警被更新，也更新选中状态
        if (this.selectedWarning?.id === id) {
          this.selectedWarning.isTypical = true
        }

        console.log('设为典型:', id)
      } catch (error) {
        this.error = error instanceof Error ? error.message : '设置典型案例失败'
        console.error('设置典型案例失败:', error)
      }
    },

    // 完成处理
    async completeWarning(id: string) {
      try {
        await warningApi.updateWarningStatus(id, '已办结')

        // 更新本地数据
        const warningIndex = this.warningDetails.findIndex((w) => w.id === id)
        if (warningIndex !== -1) {
          this.warningDetails[warningIndex].status = '已办结'
          this.warningDetails[warningIndex].completedAt = new Date().toISOString()
        }

        console.log('完成处理:', id)

        // 处理完成后返回列表视图
        this.switchToList()
      } catch (error) {
        this.error = error instanceof Error ? error.message : '完成处理失败'
        console.error('完成处理失败:', error)
      }
    },

    // 初始化数据
    async initializeData() {
      await this.fetchWarningDetails()
    },

    // 重置状态
    resetState() {
      this.currentViewMode = 'list'
      this.selectedWarning = null
      this.warningDetails = []
      this.filters = {
        dateRange: null,
        warningType: 'all',
        status: 'all',
      }
      this.pagination = {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      }
      this.loading = false
      this.error = null
    },
  },
})
