#!/usr/bin/env python3
# license_generator.py - 许可证生成工具

import os
import platform
import base64
import argparse
import sys
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
from datetime import datetime, timedelta

class LicenseGenerator:
    
    def get_cpu_id(self):
        """获取当前机器的CPU ID"""
        sysstr = platform.system()
        if sysstr == "Windows":
            cmd = "wmic cpu get ProcessorId"
            output = os.popen(cmd, "r")
            info = output.readlines()
            for i, line in enumerate(info):
                if i == 2:  # 第3行是CPU ID
                    return line.replace("\n", "").replace(" ", "")
        elif sysstr == "Linux":
            cmd = "dmidecode -t processor | grep 'ID'"
            output = os.popen(cmd, "r")
            info = output.readlines()
            for line in info:
                return line.replace("\n", "").replace(" ", "").split(":")[1]
        return None
    
    def generate_license(self, target_cpu_id=None, expiration_date="2025-12-31 23:59:59", 
                        product_name="默认产品", output_file="license.lic"):
        """
        生成许可证文件
        :param target_cpu_id: 目标机器CPU ID，如果为None则使用当前机器
        :param expiration_date: 过期时间
        :param product_name: 产品名称
        :param output_file: 输出文件名
        """
        
        # 1. 获取CPU ID
        if target_cpu_id is None:
            target_cpu_id = self.get_cpu_id()
            if target_cpu_id is None:
                print("错误: 无法获取当前机器的CPU ID")
                return False
            print(f"当前机器CPU ID: {target_cpu_id}")
        
        # 2. 构造许可证数据
        license_data = f"{target_cpu_id},{expiration_date}"
        print(f"许可证数据: {license_data}")
        
        # 3. 生成RSA密钥对
        key = RSA.generate(1024)
        private_key_pem = key.export_key().decode('utf-8')
        
        # 4. 使用公钥加密
        cipher = PKCS1_v1_5.new(key.publickey())
        encrypted_data = cipher.encrypt(license_data.encode('utf-8'))
        
        # 5. Base64编码
        encrypted_base64 = base64.b64encode(encrypted_data).decode('utf-8')
        
        # 6. 格式化私钥（去掉头尾标识）
        private_key_body = private_key_pem.replace('-----BEGIN RSA PRIVATE KEY-----\n', '')
        private_key_body = private_key_body.replace('\n-----END RSA PRIVATE KEY-----', '')
        private_key_body = private_key_body.replace('\n', '')
        
        # 7. 生成许可证文件内容
        license_content = f"""############################################################
# Vgis License
# License Product: {product_name}
# Expiration: {expiration_date}
# License SN: 
############################################################
{encrypted_base64}
------------------------------------------------------------
{private_key_body}
############################################################"""
        
        # 8. 保存文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(license_content)
            print(f"许可证已生成: {output_file}")
            return True
        except Exception as e:
            print(f"错误: 无法保存许可证文件 - {e}")
            return False

def main():
    parser = argparse.ArgumentParser(
        description='许可证生成工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 为当前机器生成许可证
  python license_generator.py -p "我的产品" -e "2025-12-31 23:59:59" -o my_license.lic
  
  # 为指定CPU ID生成许可证
  python license_generator.py -c BFEBFBFF000906EA -p "远程产品" -e "2025-12-31 23:59:59" -o remote_license.lic
  
  # 获取当前机器CPU ID
  python license_generator.py --show-cpu-id
        """
    )
    
    parser.add_argument('-c', '--cpu-id', 
                       help='目标机器的CPU ID（如果不指定则使用当前机器）')
    
    parser.add_argument('-p', '--product', 
                       default='默认产品',
                       help='产品名称 (默认: 默认产品)')
    
    # 计算默认过期时间（一年后）
    default_time = (datetime.now() + timedelta(days=365)).strftime('%Y-%m-%d %H:%M:%S')
    
    parser.add_argument('-e', '--expiration', 
                       default=default_time,
                       help=f'过期时间，格式: YYYY-MM-DD HH:MM:SS (默认: {default_time})')
    
    parser.add_argument('-o', '--output', 
                       default='license.lic',
                       help='输出文件名 (默认: license.lic)')
    
    parser.add_argument('--show-cpu-id', 
                       action='store_true',
                       help='显示当前机器的CPU ID并退出')
    
    args = parser.parse_args()
    
    generator = LicenseGenerator()
    
    # 如果只是要显示CPU ID
    if args.show_cpu_id:
        cpu_id = generator.get_cpu_id()
        if cpu_id:
            print(f"当前机器CPU ID: {cpu_id}")
        else:
            print("错误: 无法获取当前机器的CPU ID")
            sys.exit(1)
        return
    
    # 验证过期时间格式
    try:
        datetime.strptime(args.expiration, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        print("错误: 过期时间格式不正确，应为 YYYY-MM-DD HH:MM:SS")
        sys.exit(1)
    
    # 生成许可证
    print("=" * 50)
    print("许可证生成工具")
    print("=" * 50)
    print(f"产品名称: {args.product}")
    print(f"过期时间: {args.expiration}")
    print(f"输出文件: {args.output}")
    if args.cpu_id:
        print(f"目标CPU ID: {args.cpu_id}")
    else:
        print("目标CPU ID: 当前机器")
    print("-" * 50)
    
    success = generator.generate_license(
        target_cpu_id=args.cpu_id,
        expiration_date=args.expiration,
        product_name=args.product,
        output_file=args.output
    )
    
    if success:
        print("=" * 50)
        print("许可证生成成功！")
        print("=" * 50)
    else:
        print("许可证生成失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
