import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { ElMessageBox } from 'element-plus'
import DashboardView from '../views/dashboard/DashboardView.vue'
import LoginView from '../views/LoginView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: {
        title: '登录',
        requiresAuth: false,
      },
    },
    {
      path: '/',
      name: 'dashboard',
      component: DashboardView,
      meta: {
        title: '仪表盘',
        requiresAuth: false, // 开发阶段暂时关闭认证要求
      },
    },
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 首次访问时尝试从本地存储恢复认证状态
  if (!authStore.isAuthenticated) {
    authStore.restoreAuth()
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // 未登录，跳转到登录页
    console.log('未登录，跳转到登录页')
    next({ name: 'login', query: { redirect: to.fullPath } })
  } else if (to.name === 'login' && authStore.isAuthenticated) {
    // 已登录，访问登录页时弹出确认对话框
    try {
      const username = authStore.userInfo?.username || '当前用户'

      await ElMessageBox.confirm(`用户 ${username} 已经登录，是否退出当前账户？`, '确认退出', {
        confirmButtonText: '确认退出',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
      })

      // 用户确认退出，清除认证状态并允许访问登录页
      await authStore.logout()
      console.log('用户确认退出，跳转到登录页')
      next()
      // oxlint-disable-next-line no-unused-vars
    } catch (error) {
      // 用户取消退出，返回到来源页面或首页
      const fallbackRoute = from.name && from.name !== 'login' ? from : { name: 'dashboard' }
      console.log('用户取消退出，返回到:', fallbackRoute)
      next(fallbackRoute)
    }
  } else {
    next()
  }
})

export default router
