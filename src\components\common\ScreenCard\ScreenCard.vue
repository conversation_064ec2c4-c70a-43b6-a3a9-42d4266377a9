<script setup lang="ts">
import UIcon from '@/components/common/UIcon/UIcon.vue'
interface Props {
  title: string
  icon?: string // 可选的图标名称, MDI string e.g., 'mdi:home'
}

const props = defineProps<Props>()
</script>

<template>
  <div class="screen-card">
    <div class="screen-card-header">
      <div class="header-title">
        <UIcon v-if="props.icon" :name="props.icon" class="header-icon" />
        <span>{{ props.title }}</span>
      </div>
      <div class="header-slot">
        <slot name="header-control" />
      </div>
    </div>
    <div class="screen-card-body">
      <slot />
    </div>
  </div>
</template>

<style scoped lang="scss">
.screen-card {
  width: 100%;
  height: 100%;
  // position: relative;
  border-radius: 2px;
  background: $bg-card;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .screen-card-header {
    height: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 255, 254, 0.1);
    padding: 0 0.8rem;
    border-bottom: 2px solid rgba(0, 255, 254, 0.2);

    .header-icon {
      margin-right: 0.5rem;
      font-size: 1rem;
    }

    .header-title {
      display: flex;
      align-items: center;
      color: $text-active;
      font-size: 0.7rem;
      font-weight: 900;
    }

    .header-slot {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      gap: 0.2rem;
    }
  }

  .screen-card-body {
    // flex: 1;
    padding-top: 0.3rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    overflow-y: auto;
  }
}
</style>
