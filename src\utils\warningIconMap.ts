/**
 * 预警类型图标映射工具
 * 前端根据预警类型自动分配合适的图标
 */

// 预警类型图标映射表
const warningTypeIconMap: Record<string, string> = {
  // 环境卫生类
  '暴露垃圾': 'mdi-delete-outline',
  '积存建筑垃圾': 'mdi-delete-variant',
  '垃圾堆放': 'mdi-delete-circle-outline',
  
  // 建筑类
  '违章建筑': 'mdi-home-alert-outline',
  '在建农房': 'mdi-home-plus-outline',
  '建筑工地': 'mdi-crane',
  
  // 环境污染类
  '水域污染': 'mdi-water-alert-outline',
  '大气污染': 'mdi-weather-windy',
  '土壤污染': 'mdi-earth',
  
  // 交通类
  '车辆违停': 'mdi-car-off',
  '道路损坏': 'mdi-road-variant',
  '交通拥堵': 'mdi-traffic-cone',
  
  // 设施类
  '设施损坏': 'mdi-wrench-outline',
  '路灯故障': 'mdi-lightbulb-off-outline',
  '井盖缺失': 'mdi-circle-outline',
  
  // 商业类
  '流动摊贩': 'mdi-store-outline',
  '占道经营': 'mdi-storefront-outline',
  
  // 自然灾害类
  '松材线虫病': 'mdi-tree-outline',
  '病虫害': 'mdi-bug-outline',
  '森林火灾': 'mdi-fire',
  
  // 安全类
  '安全隐患': 'mdi-shield-alert-outline',
  '危险品': 'mdi-hazmat-diamond',
  '消防通道堵塞': 'mdi-fire-truck',
  
  // 其他
  '噪音污染': 'mdi-volume-high',
  '光污染': 'mdi-lightbulb-on-outline',
  '异常聚集': 'mdi-account-group-outline'
}

// 预警状态图标映射表
const warningStatusIconMap: Record<string, string> = {
  '新发现': 'mdi-alert-circle-outline',
  '反馈处理': 'mdi-progress-clock',
  '已办结': 'mdi-check-circle-outline',
  '典型案例': 'mdi-star-outline'
}

// 预警优先级图标映射表
const warningPriorityIconMap: Record<string, string> = {
  '低': 'mdi-chevron-down',
  '中': 'mdi-minus',
  '高': 'mdi-chevron-up',
  '紧急': 'mdi-chevron-double-up'
}

// 预警优先级颜色映射表
const warningPriorityColorMap: Record<string, string> = {
  '低': '#909399',
  '中': '#409EFF', 
  '高': '#E6A23C',
  '紧急': '#F56C6C'
}

// 预警状态颜色映射表
const warningStatusColorMap: Record<string, string> = {
  '新发现': '#F56C6C',
  '反馈处理': '#E6A23C',
  '已办结': '#67C23A',
  '典型案例': '#409EFF'
}

/**
 * 根据预警类型获取对应图标
 * @param type 预警类型
 * @returns MDI图标名称
 */
export function getWarningTypeIcon(type: string): string {
  return warningTypeIconMap[type] || 'mdi-alert-outline'
}

/**
 * 根据预警状态获取对应图标
 * @param status 预警状态
 * @returns MDI图标名称
 */
export function getWarningStatusIcon(status: string): string {
  return warningStatusIconMap[status] || 'mdi-help-circle-outline'
}

/**
 * 根据预警优先级获取对应图标
 * @param priority 预警优先级
 * @returns MDI图标名称
 */
export function getWarningPriorityIcon(priority: string): string {
  return warningPriorityIconMap[priority] || 'mdi-minus'
}

/**
 * 根据预警优先级获取对应颜色
 * @param priority 预警优先级
 * @returns 颜色值
 */
export function getWarningPriorityColor(priority: string): string {
  return warningPriorityColorMap[priority] || '#909399'
}

/**
 * 根据预警状态获取对应颜色
 * @param status 预警状态
 * @returns 颜色值
 */
export function getWarningStatusColor(status: string): string {
  return warningStatusColorMap[status] || '#909399'
}

/**
 * 根据预警状态获取Element Plus标签类型
 * @param status 预警状态
 * @returns Element Plus标签类型
 */
export function getWarningStatusTagType(status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' {
  const tagTypeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '新发现': 'danger',
    '反馈处理': 'warning',
    '已办结': 'success',
    '典型案例': 'primary'
  }
  return tagTypeMap[status] || 'info'
}

/**
 * 根据预警类型获取Element Plus标签类型
 * @param type 预警类型
 * @returns Element Plus标签类型
 */
export function getWarningTypeTagType(type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' {
  const tagTypeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '暴露垃圾': 'danger',
    '违章建筑': 'warning',
    '水域污染': 'info',
    '道路损坏': 'warning',
    '设施损坏': 'danger',
    '车辆违停': 'warning',
    '流动摊贩': 'info',
    '松材线虫病': 'danger',
    '在建农房': 'primary'
  }
  return tagTypeMap[type] || 'info'
}

/**
 * 获取所有支持的预警类型列表
 * @returns 预警类型数组
 */
export function getAllWarningTypes(): string[] {
  return Object.keys(warningTypeIconMap)
}

/**
 * 检查是否为支持的预警类型
 * @param type 预警类型
 * @returns 是否支持
 */
export function isSupportedWarningType(type: string): boolean {
  return type in warningTypeIconMap
}
