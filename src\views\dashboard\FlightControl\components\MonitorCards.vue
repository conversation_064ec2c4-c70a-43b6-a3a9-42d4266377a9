<script setup lang="ts">
import { computed } from 'vue'
import { useUIStore } from '@/stores/uiStore'
import type { FlightBackgroundType } from '@/stores/uiStore'
import UIcon from '@/components/common/UIcon/UIcon.vue'

// 使用UI Store
const uiStore = useUIStore()

// 当前视图模式
const viewMode = computed(() => uiStore.viewMode)
// 当前飞控背景类型
const flightBackgroundType = computed(() => uiStore.flightBackgroundType)

// 定义所有可用的显示选项
const allDisplayOptions = [
  { type: 'map' as FlightBackgroundType, title: '地图视图', icon: 'mdi:map' },
  { type: 'monitor' as FlightBackgroundType, title: '监控视频', icon: 'mdi:video' },
  { type: 'drone_video' as FlightBackgroundType, title: '无人机视频', icon: 'mdi:drone' },
]

// 动态计算当前应该显示的卡片（排除当前背景显示的选项）
const monitorCards = computed(() => {
  // if (viewMode.value !== 'flight') {
  //   // 正常模式下显示默认卡片
  //   return [
  //     { type: 'monitor' as FlightBackgroundType, title: '主监控', icon: 'mdi:video' },
  //     { type: 'drone_video' as FlightBackgroundType, title: '辅助监控', icon: 'mdi:video' },
  //   ]
  // }

  // 飞控模式下，显示非当前背景的两个选项
  const availableOptions = allDisplayOptions.filter(
    (option) => option.type !== flightBackgroundType.value,
  )

  // 确保地图卡片总是排在第一位（如果存在的话）
  const mapOption = availableOptions.find((option) => option.type === 'map')
  const otherOptions = availableOptions.filter((option) => option.type !== 'map')

  return mapOption ? [mapOption, ...otherOptions] : availableOptions
})

// 处理卡片点击事件
const handleCardClick = (cardType: FlightBackgroundType) => {
  console.log('点击卡片:', cardType)
  if (uiStore.viewMode !== 'flight') {
    // 进入飞控模式，会自动获取数据
    uiStore.enterFlightMode()
  } else {
    // 飞控模式下，切换背景显示
    console.log('切换背景显示到:', cardType)
    uiStore.switchFlightBackground(cardType)
  }
}
</script>

<template>
  <div class="monitor-cards-1">
    <div class="cards-container">
      <div
        v-for="(card, index) in monitorCards"
        :key="`${card.type}-${index}`"
        class="monitor-card"
      >
        <!-- 卡片头部 -->
        <div class="card-header">
          <span class="card-title">{{ card.title }}</span>
          <div class="card-actions">
            <!-- 切换背景按钮 -->
            <button
              class="action-btn switch-btn"
              @click="() => handleCardClick(card.type)"
              :title="`切换到${card.title}`"
            >
              <UIcon name="mdi-swap-horizontal" size="14px" color="currentColor" />
            </button>
          </div>
        </div>

        <!-- 卡片内容 -->
        <div class="card-content">
          <div class="content-area">
            <!-- 地图卡片 - 地图由DashboardView统一管理，这里只显示占位 -->
            <!-- <div v-if="card.type === 'map'" class="map-placeholder">
              <UIcon :name="card.icon" size="2rem" color="#00FFFE" />
              <span>{{ card.title }}</span>
            </div> -->

            <!-- 监控视频卡片 - 视频由DashboardView统一管理，这里只显示占位 -->
            <!-- 监控视频卡片 - 视频由DashboardView统一管理，这里显示半透明占位 -->
            <!-- <div v-if="card.type === 'monitor'" class="video-card">
              <div class="video-placeholder">
                <UIcon name="i-mdi-video" size="2rem" color="#00FFFE" />
                <span>{{ card.title }}</span>
              </div>
            </div> -->

            <!-- 无人机视频卡片 - 视频由DashboardView统一管理，这里显示半透明占位 -->
            <!-- <div v-else-if="card.type === 'drone_video'" class="video-card">
              <div class="video-placeholder">
                <UIcon name="i-mdi-video-outline" size="2rem" color="#00FFFE" />
                <span>{{ card.title }}</span>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.monitor-cards-1 {
  display: flex;
  flex-direction: column;

  .cards-container {
    display: flex;
    flex-direction: column;
    gap: $monitor-card-gap;
  }

  .monitor-card {
    width: $monitor-card-width;
    height: $monitor-card-height;
    background: $bg-panel;
    border-radius: $border-radius-base;
    // 组合阴影：内发光 + 外阴影 + 悬停发光效果
    box-shadow:
      inset 0 0 1rem rgba($glow-color, 0.3),
      0 2px 8px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba($primary-color, 0.2);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba($primary-color, 0.3);
    transition: all 0.3s ease;

    // 移除卡片整体的悬停效果，改为按钮级别的交互

    .card-header {
      height: 2rem;
      background: rgba($primary-color, 0.1);
      border-bottom: 1px solid rgba($primary-color, 0.2);
      display: flex;
      align-items: center;
      justify-content: space-between; // 左右分布
      padding: 0 0.75rem;

      .card-title {
        font-size: $font-size-panel-label;
        color: $text-active;
        font-weight: bold;
        flex: 1; // 占据剩余空间
      }

      .card-actions {
        display: flex;
        align-items: center;
        gap: 0.25rem; // 按钮之间的间距
        flex-shrink: 0; // 防止被压缩

        .action-btn {
          width: 1.5rem;
          height: 1.5rem;
          border: none;
          border-radius: $border-radius-small;
          background: rgba($primary-color, 0.1);
          color: $text-active;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          padding: 0;

          &:hover {
            background: rgba($primary-color, 0.2);
            color: $primary-color;
            box-shadow: 0 0 8px rgba($primary-color, 0.3);
            transform: scale(1.05);
          }

          &:active {
            transform: scale(0.95);
            box-shadow: 0 0 4px rgba($primary-color, 0.2);
          }

          // UIcon 样式
          :deep(.iconify) {
            transition: all 0.2s ease;
          }

          // 切换按钮特定样式
          &.switch-btn {
            &:hover :deep(.iconify) {
              transform: scaleX(-1); // 水平翻转效果，更适合交换图标
            }
          }
        }
      }
    }

    .card-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .content-area {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .map-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          color: $text-secondary;
          background: rgba(0, 0, 0, 0.3);
          border-radius: $border-radius-small;

          span {
            font-size: $font-size-panel-caption;
          }
        }

        .video-card {
          width: 100%;
          height: 100%;
          position: relative;
          overflow: hidden;
          border-radius: $border-radius-small;
          background: rgba(0, 0, 0, 0.3);
          display: flex;
          align-items: center;
          justify-content: center;

          .video-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: $text-active;
            opacity: 0.3; // 降低透明度，让视频能够覆盖在上面

            span {
              font-size: $font-size-panel-caption;
              text-align: center;
            }
          }
        }
      }
    }
  }
}
</style>
