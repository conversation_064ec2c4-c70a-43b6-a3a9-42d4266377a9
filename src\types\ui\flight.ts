/**
 * 飞控实时数据相关类型定义
 */

// 飞控实时数据类型（整体组件集合）
export interface FlightRealTimeData {
  droneId: string

  // 实时位置信息（用于地图轨迹展示）
  position: {
    longitude: number // 经度
    latitude: number // 纬度
    altitude: number // 海拔高度 m
    groundSpeed: number // 地面速度 m/s
    verticalSpeed: number // 垂直速度 m/s
    heading: number // 航向角 度 (0-360)
    timestamp: string // 位置数据时间戳
  }

  // 环境数据（多屏监控 + 飞控头部展示）
  environment: {
    windSpeed: string // "2级"
    windLevel: string // "微风"
    weather: string // "多云"
    humidity: string // "46%"
    temperature: string // "26°C"
  }

  // 机巢状态数据
  nestStatus: {
    droneStatus: '下线' | '在线' | '飞行中' | '准备起降' | '维护中' | '异常'
    doorStatus: '开启' | '关闭'
    platformStatus: '正常' | '异常'
    centeringStatus: '已归中' | '未归中'
    signals: {
      wifi: boolean
      battery: boolean
      satellite: boolean
    }
  }

  // 电池参数数据
  battery: {
    chargingStatus: '充电中' | '空闲' | '放电中'
    powerStatus: '已开机' | '已关机'
    current: number // 电流 A
    voltage: number // 电压 V
    cycleCount: number // 充放电次数
    controllerBattery: number // 遥控器电池百分比
    droneBattery: number // 无人机电池百分比
  }

  // 温度参数数据
  temperature: {
    airConditionerStatus: '开机' | '关机'
    cabinTemperature: number // 仓内温度 ℃
    batteryTemperature: number // 电池温度 ℃
  }

  // 飞行任务数据
  mission: {
    progress: number // 任务进度百分比
    location: string // 任务地点
    duration: string // 耗时
    speed: number // 飞行速度 m/s
    dataSync: '正常' | '异常' // 数据同步状态
    waypointCount: number // 航点数量
    estimatedPhotos: number // 预计拍照数量
    distance: string // 飞行距离 如"1.2km"
    altitude: string // 飞行高度 如"120m"
  }

  // 方向速度数据（仪表盘）
  navigation: {
    heading: number // 机头朝向 度
    gimbalAngle: number // 云台角度 度
    horizontalSpeed: number // 水平速度 m/s
    verticalSpeed: number // 垂直速度 m/s
  }

  // 飞行参数数据
  parameters: {
    groundAltitude: number // 离地高度 m
    droneAltitude: number // 离机高度 m
    droneDistance: number // 离机距离 km
    uploadSignal: string // 上传信号 如"-65dBm"
    downloadSignal: string // 下载信号
    gpsCount: number // GPS卫星数量
    flightMode: '自动' | '手动' | '返航'
    compassStatus: '正常' | '异常'
    channelInterference: '无' | '轻微' | '严重'
    videoSignal: '良好' | '一般' | '差'
  }
}

// 飞控组件UI数据（用于各个飞控子组件）
export interface FlightComponentsUIData {
  // 顶部状态栏数据
  topStatusBar: {
    environment: FlightRealTimeData['environment']
    isExiting: boolean
  }

  // 左侧面板数据
  leftPanel: {
    nestStatus: FlightRealTimeData['nestStatus']
    battery: FlightRealTimeData['battery']
    temperature: FlightRealTimeData['temperature']
    mission: FlightRealTimeData['mission']
  }

  // 右侧面板数据
  rightPanel: {
    navigation: FlightRealTimeData['navigation']
    parameters: FlightRealTimeData['parameters']
  }
}
