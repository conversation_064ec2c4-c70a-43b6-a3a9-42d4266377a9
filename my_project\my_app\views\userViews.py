import datetime
import logging
import os

from django.contrib import auth
from django.db import connection
from license_authorize import license_authorize
from rest_framework import viewsets
from rest_framework.authtoken.models import Token

from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from vgis_utils.vgis_datetime.datetimeTools import DateTimeHelper

# drf-yasg 相关导入
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from my_app.manage.userManager import UserOperator
from my_app.models import AuthUser, SysParam
from my_app.serializers import (
    AuthUserSerializer, SysUserRoleSerializer, SysRoleSerializer,
    LoginRequestSerializer, ForceLoginRequestSerializer, LoginResponseSerializer,
    SSOLoginRequestSerializer, LogoutRequestSerializer
)
from my_project.settings import AUTH_TOKEN_AGE, WINDOWS_LICENSE_PATH, LINUX_LICENSE_PATH

from my_app.views.response.baseRespone import Result
# 引入scheduler,使得每次启动Django框架时，定时任务都会同时启动
# from t231_app.scheduler.airQualityTask import scheduler
from my_app.utils.numberUtility import  NumberHelper
#日志
from vgis_log.logTools import LoggerHelper
import logging
#时间
import datetime
logger = logging.getLogger('django')
#日志模型
from my_app.models import SysLog
#重要目标分析查询类,此刻通过传入sql能给其他视图查询使用
# from my_app.manage.importantTargetAnalysisManage import ImportantTargetAnalysisManage
import json
#密码加密
from my_app.utils.passwordUtility import PasswordHelper
#用户角色数据模型
from my_app.models import SysUserRole
#角色模型
from my_app.models import SysRole
from my_app.tools.encrptAppKey import AES_de,AES_en
'''
ViewSets定义视图的行为,ModelViewSet默认支持以下action
list: /api/project_batch_data/  TYPE:GET
retrevie:/api/project_batch_data/1  TYPE:GET
create:/api/project_batch_data/  jsonbody   TYPE:POST
delete: /api/project_batch_data/1/  TYPE:DELETE
update: /api/project_batch_data/1/  json body TYPE:PUT
'''
#logger = logging.getLogger('django')
import platform

platformType = platform.system().lower()


# 用户相关操作类
class UserViewSet(viewsets.ModelViewSet):
    # loggerHelper = LoggerHelper()
    # 权限，登录操作对所有用户都可执行
    permission_classes = (AllowAny,)
    # 指定查询集
    queryset = AuthUser.objects.all()
    # 指定序列化器
    serializer_class = AuthUserSerializer

    def get_AUTH_TOKEN_AGE(self):
        """
        获取认证令牌的有效期。
        从系统参数表中获取名为'AUTH_TOKEN_AGE'的参数值，如果存在则转换为整数返回，不存在则返回默认值10800。

        :return: 认证令牌的有效期（秒）
        """
        obj = SysParam.objects.get(param_en_key='AUTH_TOKEN_AGE')
        if obj is not None:
            return int(obj.param_value)
        else:
            return 10800

    # 身份认证
    @swagger_auto_schema(
        method='post',
        operation_description="用户登录接口",
        operation_summary="用户登录",
        request_body=LoginRequestSerializer,
        responses={
            200: LoginResponseSerializer,
            400: openapi.Response(
                description="登录失败",
                schema=LoginResponseSerializer
            )
        },
        tags=['用户认证']
    )
    @action(detail=False, methods=['POST'], url_path='login')
    def login(self, request, *args, **kwargs):
        """
        用户登录接口，处理用户登录请求。
        包含许可授权检查、用户存在性检查、令牌过期检查等逻辑。

        :param request: 请求对象
        :param args: 可变参数
        :param kwargs: 关键字参数
        :return: 登录结果的响应
        """
        # 从请求数据中获取用户名、密码和验证码
        username = request.data.get('username')
        password = request.data.get('password')
        verifcation = request.data.get('verifcation')

        # 增加许可授权
        # client_time = 1684489627117
        client_time = request.data.get('client_time')
        # 根据操作系统类型选择许可文件路径
        lic_path = WINDOWS_LICENSE_PATH if platformType == 'windows' else LINUX_LICENSE_PATH
        if client_time is None:
            # 如果缺少client_time，返回错误响应
            res = {
                'success': False,
                'code': -1,
                'message': '接口请求失败，缺少client_time!'
            }
        else:
            if not os.path.exists(lic_path):
                # 如果未找到许可文件，返回错误响应
                res = {
                    'success': False,
                    'code': -1,
                    'message': '没有找到许可文件，请联系管理员!'
                }
            else:
                # 检查许可文件的有效性
                license_result = license_authorize.check_validity(client_time, lic_path)
                if license_result:
                    # 检查用户是否存在
                    if len(AuthUser.objects.filter(username=username)) > 0:
                        # 获取用户ID
                        user_id = AuthUser.objects.filter(username=username)[0].id
                        # 获取用户的旧令牌
                        oldToken = Token.objects.filter(user_id=user_id)
                        if len(oldToken) > 0:
                            # 获取当前时间戳
                            now = int(DateTimeHelper.string2time_stamp(str(datetime.datetime.now())))
                            # 获取旧令牌的创建时间戳
                            token_created = int(DateTimeHelper.string2time_stamp(str(oldToken[0].created)))
                            is_token_expired = False
                            # 检查令牌是否过期
                            if now - token_created > self.get_AUTH_TOKEN_AGE():
                                is_token_expired = True
                        if len(oldToken) > 0 and is_token_expired is False:
                            # 如果用户已在别处登录且令牌未过期，返回错误响应
                            res = {
                                'success': False,
                                'code': -2,
                                'message': '用户已在别处登录!'
                            }
                        else:
                            # 调用用户操作类的登录方法进行登录
                            userOperator = UserOperator(connection)
                            res = userOperator.login(request, username, password, verifcation, auth, Token)
                    else:
                        # 如果用户名不正确，返回错误响应
                        res = {
                            'success': False,
                            'code': -1,
                            'message': '用户名不正确!'
                        }
                else:
                    # 如果许可已过期，返回错误响应
                    res = {
                        'success': False,
                        'code': -1,
                        'message': '许可已过期，请联系管理员!'
                    }

        return Response(res)

    # 身份认证，强制登录，冲掉别的地方登录
    @swagger_auto_schema(
        method='post',
        operation_description="用户强制登录接口，会强制踢掉其他地方的登录",
        operation_summary="强制登录",
        request_body=ForceLoginRequestSerializer,
        responses={
            200: LoginResponseSerializer,
            400: openapi.Response(
                description="登录失败",
                schema=LoginResponseSerializer
            )
        },
        tags=['用户认证']
    )
    @action(detail=False, methods=['POST'], url_path='loginWithForce')
    def login_with_force(self, request, *args, **kwargs):
        """
        用户强制登录接口，处理用户强制登录请求。
        包含许可授权检查，不进行用户是否已在别处登录的检查。

        :param request: 请求对象
        :param args: 可变参数
        :param kwargs: 关键字参数
        :return: 登录结果的响应
        """
        # 从请求数据中获取用户名、密码、验证码和保险名称
        username = request.data.get('username')
        password = request.data.get('password')
        verifcation = request.data.get('verifcation')
        insuranceName = request.data.get('insuranceName')
        # key1 = 'miyaovgis0704gis'
        # username = AES_de(key1, username)
        # password = AES_de(key1, password)
        # verifcation = AES_de(key1, verifcation)


        # 增加许可授权
        # client_time = 1684489627117
        client_time = request.data.get('client_time')
        # 根据操作系统类型选择许可文件路径
        lic_path = WINDOWS_LICENSE_PATH if platformType == 'windows' else LINUX_LICENSE_PATH
        if client_time is None:
            # 如果缺少client_time，返回错误响应
            res = {
                'success': False,
                'code': -1,
                'message': '接口请求失败，缺少client_time!'
            }
        else:
            if not os.path.exists(lic_path):
                # 如果未找到许可文件，返回错误响应
                res = {
                    'success': False,
                    'code': -1,
                    'message': '没有找到许可文件，请联系管理员!'
                }
            else:
                # 检查许可文件的有效性
                license_result = license_authorize.check_validity(client_time, lic_path)
                # license_result = True
                if license_result:
                    # 调用用户操作类的登录方法进行登录
                    userOperator = UserOperator(connection)
                    res = userOperator.login(request, username, password, verifcation, auth,
                                             Token)
                else:
                    # 如果许可已过期，返回错误响应
                    res = {
                        'success': False,
                        'code': -1,
                        'message': '许可已过期，请联系管理员!'
                    }
        return Response(res)

    # 身份认证，传入单点登录的token
    @swagger_auto_schema(
        method='post',
        operation_description="通过单点登录令牌进行登录",
        operation_summary="单点登录",
        request_body=SSOLoginRequestSerializer,
        responses={
            200: LoginResponseSerializer,
            400: openapi.Response(
                description="登录失败",
                schema=LoginResponseSerializer
            )
        },
        tags=['用户认证']
    )
    @action(detail=False, methods=['POST'], url_path='loginByToken')
    def login_by_token(self, request, *args, **kwargs):
        """
        通过单点登录令牌进行登录的接口。

        :param request: 请求对象
        :param args: 可变参数
        :param kwargs: 关键字参数
        :return: 登录结果的响应
        """
        # 从请求数据中获取单点登录令牌
        ssoToken = request.data.get('ssoToken')
        # 创建用户操作类实例
        userOperator = UserOperator()
        # 调用用户操作类的通过令牌登录方法进行登录
        res = userOperator.login_by_token(request, ssoToken, auth, Token)
        return Response(res)

    # 退出登录
    @swagger_auto_schema(
        method='post',
        operation_description="用户退出登录接口",
        operation_summary="退出登录",
        request_body=LogoutRequestSerializer,
        responses={
            200: LoginResponseSerializer,
            400: openapi.Response(
                description="退出失败",
                schema=LoginResponseSerializer
            )
        },
        tags=['用户认证']
    )
    @action(detail=False, methods=['POST'], url_path='logout')
    def logout(self, request):
        """
        用户退出登录接口。

        :param request: 请求对象
        :return: 退出登录结果的响应
        """
        # 从请求数据中获取用户名和用户ID
        username = request.data.get('username')
        userid = request.data.get('userid')
        # 创建用户操作类实例
        userOperator = UserOperator(connection)
        # 调用用户操作类的退出登录方法进行退出操作
        res = userOperator.logout(request, Token, userid, auth)
        return Response(res)

    # 获取验证码
    @action(detail=False, methods=['GET'], url_path='verfication')
    def verfication(self, request):
        """
        获取验证码的接口。

        :param request: 请求对象
        :return: 验证码相关响应
        """
        # 创建用户操作类实例
        userOperator = UserOperator(connection)
        # 调用用户操作类的获取验证码方法
        return userOperator.get_verifaction_code(request)

    # 获取是否开启验证码
    @action(detail=False, methods=['GET'], url_path='is_use_verfication')
    def is_use_verfication(self, request):
        """
        获取是否开启验证码的接口。

        :param request: 请求对象
        :return: 是否开启验证码的响应
        """
        # 创建用户操作类实例
        userOperator = UserOperator(connection)
        # 调用用户操作类的返回是否使用验证码方法
        res = userOperator.return_is_use_verification_code(request)
        return Response(res)

    # 获取用户信息
    @action(detail=False, methods=['POST'])
    def getUserInfo(self, request):
        """
        获取用户信息的接口。
        从数据库中查询用户信息，并关联查询用户的角色信息。

        :param request: 请求对象
        :return: 用户信息的响应
        """
        # userid = request.data.get('userid')
        # userOperator = UserOperator(connection)
        # 定义API路径
        api_path = "/user/getUserInfo/"
        # 定义功能标题
        function_title = "获取用户信息"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 创建重要目标分析管理类实例
            # importantTargetAnalysisManage = ImportantTargetAnalysisManage(connection)
            # 构造查询用户信息的SQL语句
            sql = f'SELECT  id,fullname,username,email,mobile from "auth_user"  where 1=1 '
            # 执行SQL查询，获取用户信息
            # static_value = importantTargetAnalysisManage.get_zymbfx(request, sql)
            # LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)
            # 构造成功消息
            msg = "{}成功".format(function_title)
            # del static_value['data'][0]['password'] #删除账号密码，返回前端
            # 根据用户ID查找角色关联表
            roleId = SysUserRole.objects.filter(user_id=request.data["id"])
            # 对角色关联信息进行序列化
            roleIdInfo = SysUserRoleSerializer(roleId, many=True)
            json_data = roleIdInfo.data
            # 打印角色ID
            print(f'角色ID', dict(json_data[0])["role_id"])
            # 根据角色ID查询角色信息
            role = SysRole.objects.filter(role_id=dict(json_data[0])["role_id"])
            # 对角色信息进行序列化
            roleIdInfo = SysRoleSerializer(role, many=True)
            json_data2 = roleIdInfo.data
            # 打印角色中文名
            print(f'角色中文名', dict(json_data2[0])["role_name"])
            # print(type(static_value['data'][0]))
            # 将角色中文名添加到用户信息中
            # static_value['data'][0]["roleName"] = dict(json_data2[0])["role_name"]
            # 返回成功响应
            return Result.sucess(msg, None)

        except Exception as exp:
            # 记录异常日志信息
            # LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request,
            # function_title, None, exp)
            # 构造失败消息
            msg = "{}失败".format(function_title)
            # 返回失败响应
            return Result.fail(msg, str(exp))

#修改用户信息
    @action(detail=False, methods=['POST'])
    def updateUserInfo(self, request):
        """
        修改用户信息的接口。
        该接口接收用户信息修改请求，检查请求数据中是否包含密码，若包含则拒绝修改；
        若不包含，则根据用户 ID 查找用户，若用户存在则更新用户信息，否则提示用户不存在。

        :param request: 请求对象，包含要修改的用户信息
        :return: 修改结果的响应
        """
        # 定义 API 路径
        api_path = "/user/updateUserInfo/"
        # 定义功能标题
        function_title = "修改用户信息"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 检查请求数据中是否包含密码字段
            if "password" in request.data:
                # 若包含密码字段，返回修改失败信息
                return Result.sucess("修改失败，不能传入密码")
            else:
                # 从请求数据中获取用户 ID
                id = request.data["id"]
                # 检查是否存在该 ID 的用户
                if len(AuthUser.objects.filter(id=id)) > 0:
                    # 通过 ID 查找用户
                    tis = AuthUser.objects.filter(id=id)
                    # 使用请求数据更新用户信息
                    tis.update(**request.data)
                    # 记录日志结束信息（此处注释掉了原代码中的日志记录部分）
                    # LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                    #                               function_title)
                    # 返回修改成功信息
                    return Result.sucess("{}成功".format(function_title))
                else:
                    # 若用户不存在，返回修改失败信息
                    return Result.sucess("修改失败，这个用户不存在。")
        except Exception as exp:
            # 若出现异常，构造错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 记录异常日志信息（此处注释掉了原代码中的日志记录部分）
            # LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request,
            #                                            function_title, None, exp)
            # 返回修改失败的错误响应
            return Result.fail(error_info, json_data)

    # 修改用户密码
    @action(detail=False, methods=['POST'])
    def updateUserPassword(self, request):
        """
        修改用户密码的接口。
        该接口接收用户密码修改请求，根据用户 ID 查找用户，验证原始密码是否正确，
        若正确则更新为新密码，否则提示原始密码错误。

        :param request: 请求对象，包含用户 ID、原始密码和新密码
        :return: 修改结果的响应
        """
        # 定义 API 路径
        api_path = "/user/updateUserPassword/"
        # 从请求数据中获取用户 ID
        userId = request.data["id"]
        # 调用日志工具类的方法，记录操作开始的日志信息，例如开始时间等
        start = LoggerHelper.set_start_log_info(logger)
        function_title = "修改密码请求时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        username = NumberHelper.get_user(request)
        try:
            # 通过用户 ID 查找用户
            user = AuthUser.objects.filter(id=userId)

            # 检查是否找到该用户
            if len(user) > 0:
                # 对用户信息进行序列化
                # userInfo = AuthUserSerializer(user, many=True)
                # # 获取序列化后的数据
                # json_data = userInfo.data
                # # 进行用户认证，验证原始密码是否正确
                # res = auth.authenticate(username=dict(json_data[0])["username"],
                #                         password=request.data["password"])
                # # 打印认证返回值
                # print("验证返回值", res)
                # if res is None:
                #     # 若认证失败，返回原始密码错误的信息
                #     return Result.sucess("原始密码错误,修改失败！")
                # else:
                # 对新密码进行加密处理
                request.data["password"] = PasswordHelper.getEncrptPassword(request.data["newPassword"])
                # 删除请求数据中的新密码字段
                del request.data["newPassword"]
                # 更新用户密码
                user.update(**request.data)
                # 返回密码修改成功的信息
                function_title += "返回时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                # 此处注释掉的日志记录代码，可根据实际需求决定是否启用
                LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, username, request,
                                              function_title)

                return Result.sucess("密码修改成功！")
            else:
                return Result.sucess("用户不存在！")
        except Exception as exp:

            # 构建错误信息，包含操作功能标题和异常信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 若操作过程中出现异常，此处注释掉的日志记录代码，可根据实际需求决定是否启用
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, username, request,
                                                       function_title, error_info, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 返回失败结果，包含错误信息和异常的 JSON 字符串
            return Result.fail(error_info, json_data)



#  分页相关操作类
class MyPage(PageNumberPagination):
    page_size_query_param = "max_page"
    page_query_param = "page"
