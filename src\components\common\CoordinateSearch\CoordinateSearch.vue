<template>
  <div class="coordinate-search">
    <div class="search-input">
      <el-input
        v-model="coordinates"
        placeholder="请输入坐标"
        size="small"
        :class="{ 'input-error': hasError }"
        @input="handleInput"
        @keyup.enter="handleSearch"
      />
    </div>
    <div class="search-button">
      <el-button type="primary" size="small" :disabled="!isValidCoordinates" @click="handleSearch">
        <UIcon name="mdi:map-marker" />
        提交
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
/*
 * 坐标搜索组件
 * 1. 支持坐标输入和验证
 * 2. 支持回车键提交
 * 3. 支持坐标格式验证
 * 4. 发射搜索事件
 */

// 定义 props
const props = defineProps({
  // 坐标格式验证规则
  coordinatePattern: {
    type: RegExp,
    default: () => /^-?\d+\.?\d*,-?\d+\.?\d*$/,
  },
  // 占位符文本
  placeholder: {
    type: String,
    default: '请输入坐标 (经度,纬度)',
  },
})

// 定义要发射的事件
const emit = defineEmits(['search', 'coordinateChange'])

// 响应式数据
const coordinates = ref('')
const hasError = ref(false)

// 计算属性 - 验证坐标格式
const isValidCoordinates = computed(() => {
  if (!coordinates.value.trim()) return false
  return props.coordinatePattern.test(coordinates.value.trim())
})

// 处理输入
const handleInput = (value: string) => {
  hasError.value = false
  emit('coordinateChange', value)
}

// 处理搜索
const handleSearch = () => {
  if (!coordinates.value.trim()) {
    hasError.value = true
    return
  }

  if (!isValidCoordinates.value) {
    hasError.value = true
    return
  }

  // 解析坐标
  const coordArray = coordinates.value.trim().split(',')
  if (coordArray.length === 2) {
    const longitude = parseFloat(coordArray[0])
    const latitude = parseFloat(coordArray[1])

    if (!isNaN(longitude) && !isNaN(latitude)) {
      emit('search', { longitude, latitude, raw: coordinates.value.trim() })
      hasError.value = false
    } else {
      hasError.value = true
    }
  } else {
    hasError.value = true
  }
}

// 暴露方法给父组件
defineExpose({
  clearCoordinates: () => {
    coordinates.value = ''
    hasError.value = false
  },
  setCoordinates: (coords: string) => {
    coordinates.value = coords
    hasError.value = false
  },
})
</script>

<style scoped lang="scss">
.coordinate-search {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;

  .search-input {
    flex: 1;

    :deep(.el-input__wrapper) {
      transition: all 0.3s ease;
    }

    .input-error {
      :deep(.el-input__wrapper) {
        border-color: #f56c6c !important;
        box-shadow: 0 0 0 1px #f56c6c inset !important;
      }
    }
  }

  .search-button {
    flex-shrink: 0;

    :deep(.el-button) {
      display: flex;
      align-items: center;
      gap: 0.25rem;

      // 保持与项目主题色一致
      &.el-button--primary {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);

        &:hover {
          background-color: var(--el-color-primary-light-3);
          border-color: var(--el-color-primary-light-3);
        }

        &:disabled {
          background-color: var(--el-color-primary-light-5);
          border-color: var(--el-color-primary-light-5);
        }
      }
    }
  }
}
</style>
