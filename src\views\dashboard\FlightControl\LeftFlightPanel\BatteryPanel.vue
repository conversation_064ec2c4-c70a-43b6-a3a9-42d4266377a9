<script setup lang="ts">
import { computed } from 'vue'
import type { FlightRealTimeData } from '@/types/ui'

// 组件属性 - 从父组件接收数据
interface Props {
  data: FlightRealTimeData['battery']
}

const props = defineProps<Props>()

// 电池数据 - 直接使用props传入的数据
const batteryData = computed(() => {
  return props.data
})

// 计算充电状态显示
const chargingStatusDisplay = computed(() => {
  const status = batteryData.value.chargingStatus
  switch (status) {
    case '充电中':
      return { icon: 'mdi:battery-charging', color: '#4CAF50' }
    case '放电中':
      return { icon: 'mdi:battery-minus', color: '#FF9800' }
    case '空闲':
    default:
      return { icon: 'mdi:battery', color: '#00FFFE' }
  }
})

// 计算电源状态显示
const powerStatusDisplay = computed(() => {
  return batteryData.value.powerStatus === '已开机'
    ? { icon: 'mdi:power', color: '#4CAF50' }
    : { icon: 'mdi:power-off', color: '#666' }
})

// 计算电池百分比颜色
const getBatteryColor = (percentage: number) => {
  if (percentage < 20) return '#F44336' // 红色 - 低电量
  if (percentage < 50) return '#FF9800' // 橙色 - 中等电量
  return '#4CAF50' // 绿色 - 高电量
}
</script>

<template>
  <ScreenCard title="电池参数" icon="mdi:battery">
    <template #header-control>
      <!-- 电池电量指示器 -->
      <div class="battery-indicators">
        <div class="battery-item">
          <UIcon
            name="mdi:gamepad"
            size="0.8rem"
            :color="getBatteryColor(batteryData.controllerBattery)"
          />
          <span class="battery-percent">{{ batteryData.controllerBattery }}%</span>
        </div>
        <div class="battery-item">
          <UIcon
            name="mdi:airplane"
            size="0.8rem"
            :color="getBatteryColor(batteryData.droneBattery)"
          />
          <span class="battery-percent">{{ batteryData.droneBattery }}%</span>
        </div>
      </div>
    </template>

    <div class="battery-content">
      <!-- 充电状态和电源状态一行 - 使用DataDisplay -->
      <div class="battery-row data-display-row">
        <DataDisplay
          title="充电状态"
          :value="batteryData.chargingStatus"
          unit=""
          :icon="chargingStatusDisplay.icon"
          :iconColor="chargingStatusDisplay.color"
        />
        <DataDisplay
          title="电源状态"
          :value="batteryData.powerStatus"
          unit=""
          :icon="powerStatusDisplay.icon"
          :iconColor="powerStatusDisplay.color"
        />
      </div>

      <!-- 电流、电压、充放电次数一行 - 使用DataDisplay -->
      <div class="battery-row data-display-row">
        <DataDisplay
          title="电流"
          :value="batteryData.current"
          unit="A"
          icon="mdi:current-ac"
          iconColor="#00FFFE"
        />
        <DataDisplay
          title="电压"
          :value="batteryData.voltage"
          unit="V"
          icon="mdi:lightning-bolt"
          iconColor="#00FFFE"
        />
        <DataDisplay
          title="充放电"
          :value="batteryData.cycleCount"
          unit="次"
          icon="mdi:counter"
          iconColor="#00FFFE"
        />
      </div>
    </div>
  </ScreenCard>
</template>

<style scoped lang="scss">
.battery-indicators {
  display: flex;
  gap: 0.5rem;

  .battery-item {
    display: flex;
    align-items: center;
    gap: 0.2rem;

    .battery-percent {
      font-size: $font-size-panel-caption;
      color: $text-default;
      font-weight: bold;
    }
  }
}

.battery-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.5rem 0;

  .battery-row {
    display: flex;
    gap: 0.5rem;

    &.data-display-row {
      justify-content: space-between;
    }
  }
}
</style>
