# 生产环境配置示例
# 复制此文件为 .env.production 并根据生产环境需求修改配置

# Django 核心配置
DJANGO_SECRET_KEY='your-production-secret-key-here'
DEBUG=False
ALLOWED_HOSTS=your-domain.com,your-ip-address

# 数据库配置 - PostgreSQL (生产环境)
DB_NAME=your_production_db
DB_USER=your_production_user
DB_PASSWORD=your_production_password
DB_HOST=your_production_db_host
DB_PORT=5432
DB_CONN_MAX_AGE=60

# Redis 配置 (生产环境)
REDIS_HOST=your_production_redis_host
REDIS_PORT=6379
REDIS_DB=0

# Celery 配置 (生产环境)
CELERY_BROKER_URL=redis://your_production_redis_host:6379/0
CELERY_RESULT_BACKEND=redis://your_production_redis_host:6379/0
CELERY_TIMEZONE=Asia/Shanghai

# 生产环境日志配置 (减少日志输出)
LOG_LEVEL_DJANGO=WARNING       # 生产环境只记录警告及以上级别
LOG_LEVEL_CELERY=WARNING       # Celery 任务队列日志级别  
LOG_LEVEL_DB=ERROR             # 数据库查询只记录错误
LOG_LEVEL_REQUEST=WARNING      # HTTP 请求日志级别

# 生产环境 Channel Layers 配置 (使用 Redis)
CHANNEL_LAYERS_BACKEND=redis
CHANNEL_REDIS_HOST=your_production_redis_host
CHANNEL_REDIS_PORT=6379

# 许可证文件路径 (生产环境)
WINDOWS_LICENSE_PATH=C:/production/license/gzwj_license.lic
LINUX_LICENSE_PATH=/opt/app/license/gzwj_license.lic

# 项目服务配置 (生产环境)
PROJECT_WEB_PROTOCOL=https
PROJECT_SERVICE_IP=0.0.0.0
PROJECT_SERVICE_PORT=8000

# SuperMap iServer 配置 (生产环境)
SUPERMAP_ISERVER_URL=http://your_production_iserver:8090/
SUPERMAP_ISERVER_PASSWORD=your_production_password
SUPERMAP_ISERVER_USER=your_production_user

# 业务配置 (生产环境)
AUTH_TOKEN_AGE=3600            # 生产环境 Token 过期时间较短
MAX_THREAD_COUNT=32            # 生产环境可以使用更多线程
PAGE_MAX_SIZE=100              # 生产环境限制分页大小
IS_USE_MOCK_DATA=False
IS_USE_VERIFICATION_CODE=True  # 生产环境启用验证码
IS_ENCRYPTION=True             # 生产环境启用加密
LOGIN_ERROR_ATTEMPTS=3         # 生产环境更严格的登录限制
LOGIN_LOCKED_TIME=1800         # 生产环境更长的锁定时间
TOKEN_USE_CACHE=True           # 生产环境启用 Token 缓存

# 其他配置
LANGUAGE_CODE=zh-Hans
TIME_ZONE=Asia/Shanghai
