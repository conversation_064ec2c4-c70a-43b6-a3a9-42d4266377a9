// 从正确的路径导入 request 工具
import request, { get, post, put, del, upload } from '../utils/request'

/**
 * 示例 API 调用
 * dev 环境会使用 vite.config.ts 中的 server.proxy 配置
 * prod 环境会使用 .env.production 中的 VITE_API_BASE_URL 配置
 */
export const exampleApi = {
  /**
   * 使用 request 实例的 get 方法
   * 当使用 request 实例时，需要在配置对象中使用 params 来传递查询参数
   */
  getWithRequestInstance: () => {
    return request.get('/example', {
      params: { id: 1, status: 'active' },
      timeout: 5000, // 可以传入其他 axios 配置
    })
  },

  /**
   * 使用封装的 get 方法
   * @param id 资源ID
   * 当使用封装的 get 方法时，第二个参数直接是查询参数对象
   */
  getExample: (id: number) => {
    // 正确用法：第二个参数直接是查询参数对象
    return get('/example', { id, status: 'active' })

    // 如果需要额外的 axios 配置，可以使用第三个参数
    // return get('/example', { id }, { timeout: 5000 })
  },

  /**
   * 使用封装的 post 方法
   * @param data 要创建的数据
   * 当使用封装的 post 方法时，第二个参数直接是请求体数据
   */
  postExample: (data: { name: string; age: number }) => {
    // 正确用法：第二个参数直接是请求体数据
    return post('/example', data)

    // 如果需要额外的 axios 配置
    // return post('/example', data, { timeout: 5000 })
  },

  /**
   * 使用封装的 put 方法
   * @param id 要更新的资源ID
   * @param data 更新的数据
   */
  putExample: (id: number, data: { name: string; age: number }) => {
    // 正确用法：直接传递请求体数据
    return put(`/example/${id}`, data)
  },

  /**
   * 使用封装的 del 方法 - 通过路径参数删除资源
   * @param id 要删除的资源ID
   * DELETE 请求通常通过 URL 路径参数来标识要删除的资源
   */
  deleteById: (id: number) => {
    // 正确用法：通过路径参数标识要删除的资源
    return del(`/example/${id}`)
  },

  /**
   * 使用封装的 del 方法 - 带请求体的删除
   * 如果确实需要在 DELETE 请求中发送请求体，可以使用配置对象的 data 属性
   */
  deleteWithRequestBody: (ids: number[]) => {
    // 注意：这种用法较少见，但有时确实需要
    return del('/example/batch', {
      data: { ids }, // 请求体需要包装在 data 属性中
    })
  },

  /**
   * 使用封装的 upload 方法上传文件
   * @param file 要上传的文件
   */
  uploadFile: (file: File) => {
    return upload('/upload', file)
  },

  /**
   * 使用 FormData 上传多个文件和其他数据
   * @param files 要上传的文件数组
   * @param data 其他数据
   */
  uploadMultipleFiles: (files: File[], data: { userId: number }) => {
    const formData = new FormData()

    // 添加文件
    files.forEach((file, index) => {
      formData.append(`file${index}`, file)
    })

    // 添加其他数据
    formData.append('userId', data.userId.toString())

    return upload('/upload/multiple', formData)
  },
}
