<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'

// 视频流背景组件 - 飞控模式下的背景

// 定义组件props
interface Props {
  // 视频流类型：监控视频或无人机视频
  streamType?: 'monitor' | 'drone_video'
}

// 接收props，默认为监控视频
const props = withDefaults(defineProps<Props>(), {
  streamType: 'monitor',
})

// 视频元素引用
const videoRef = ref<HTMLVideoElement>()

// 根据类型显示不同的说明文字
const streamTypeText = computed(() => {
  return props.streamType === 'monitor' ? '监控视频' : '无人机视频'
})

// 根据streamType计算视频地址
const videoSrc = computed(() => {
  // 目前监控和无人机都使用同一个视频文件进行模拟
  // 后续可以根据不同的streamType返回不同的视频地址
  // const baseVideoPath = '/video/drone-1-1.mp4'

  // switch (props.streamType) {
  //   case 'monitor':
  //     // 监控视频 - 可以后续替换为不同的视频源
  //     return '/video/jiankong-1-1.mp4'
  //   case 'drone_video':
  //     // 无人机视频 - 可以后续替换为不同的视频源
  //     return '/video/drone-1-1.mp4'
  //   default:
  //     return baseVideoPath
  // }
  if (props.streamType === 'monitor') {
    return '/video/jiankong-1-1.mp4'
  } else {
    return '/video/drone-1-1.mp4'
  }
})

// 视频加载状态
const isVideoLoaded = ref(false)
const hasVideoError = ref(false)

// 视频事件处理
const handleVideoLoaded = () => {
  isVideoLoaded.value = true
  hasVideoError.value = false
  console.log(`${streamTypeText.value}加载成功`)
}

const handleVideoError = (error: Event) => {
  hasVideoError.value = true
  isVideoLoaded.value = false
  console.error(`${streamTypeText.value}加载失败:`, error)
}

// 组件挂载后初始化视频
onMounted(() => {
  if (videoRef.value) {
    // 设置视频自动播放（某些浏览器可能需要用户交互）
    videoRef.value.play().catch((error) => {
      console.warn(`${streamTypeText.value}自动播放失败:`, error)
    })
  }
})
</script>

<template>
  <div class="video-stream-background">
    <!-- 视频流容器 -->
    <div class="video-container">
      <!-- 视频播放器 -->
      <video
        ref="videoRef"
        :src="videoSrc"
        class="video-player"
        autoplay
        loop
        muted
        playsinline
        @loadeddata="handleVideoLoaded"
        @error="handleVideoError"
      >
        您的浏览器不支持视频播放
      </video>

      <!-- 视频加载状态 -->
      <div v-if="!isVideoLoaded && !hasVideoError" class="video-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载{{ streamTypeText }}...</div>
      </div>

      <!-- 视频错误状态 -->
      <div v-if="hasVideoError" class="video-error">
        <div class="error-icon">⚠</div>
        <div class="error-text">{{ streamTypeText }}加载失败</div>
      </div>

      <!-- 视频覆盖层 - 显示信息和准心 -->
    </div>
  </div>
</template>

<style scoped lang="scss">
.video-stream-background {
  width: 100%;
  height: 100%;
  background: $bg-dark;
  position: relative;
  overflow: hidden;

  .video-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;

    // 视频播放器样式
    .video-player {
      width: 100%;
      height: 100%;
      object-fit: cover; // 保持视频比例，填满容器
      background: $bg-dark;
    }

    // 视频加载状态
    .video-loading {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 2;

      .loading-spinner {
        width: 2rem;
        height: 2rem;
        border: 2px solid rgba($primary-color, 0.3);
        border-top: 2px solid $primary-color;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }

      .loading-text {
        color: $text-active;
        font-size: $font-size-panel-normal;
      }
    }

    // 视频错误状态
    .video-error {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 2;

      .error-icon {
        font-size: 2rem;
        color: #ff6b6b;
        margin-bottom: 1rem;
      }

      .error-text {
        color: $text-secondary;
        font-size: $font-size-panel-normal;
      }
    }

    // 视频覆盖层 - 显示准心和信息
    .video-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none; // 不阻挡视频交互
      z-index: 1;

      // 准心样式
      .crosshair {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 2rem;
        height: 2rem;

        .crosshair-horizontal,
        .crosshair-vertical {
          position: absolute;
          background: $primary-color;
          opacity: 0.8;
          box-shadow: 0 0 4px rgba($primary-color, 0.5);
        }

        .crosshair-horizontal {
          top: 50%;
          left: 0;
          width: 100%;
          height: 1px;
          transform: translateY(-50%);
        }

        .crosshair-vertical {
          left: 50%;
          top: 0;
          width: 1px;
          height: 100%;
          transform: translateX(-50%);
        }
      }

      // 视频流类型说明文字
      .stream-type-text {
        position: absolute;
        top: 1rem;
        left: 1rem;
        color: $primary-color;
        font-size: $font-size-panel-label;
        font-weight: bold;
        text-align: left;
        background: rgba(0, 0, 0, 0.7);
        padding: 0.25rem 0.5rem;
        border-radius: $border-radius-small;
        border: 1px solid rgba($primary-color, 0.3);
        backdrop-filter: blur(4px);
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
