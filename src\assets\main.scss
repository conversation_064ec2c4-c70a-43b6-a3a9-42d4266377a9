// @forward 'element-plus/theme-chalk/src/common/var.scss' with (
//   $colors: (
//     'primary': (
//       'base': #00FFFE,
//     ),
//     'success': (
//       'base': #21ba45,
//     ),
//     'warning': (
//       'base': #f2711c,
//     ),
//     'danger': (
//       'base': #db2828,
//     ),
//     'error': (
//       'base': #db2828,
//     ),
//     'info': (
//       'base': #42b8dd,
//     ),
//   ),
//   $button-padding-horizontal: (
//     'default': 80px,
//   )
// );

// 基础色彩变量 - 直接使用rgba值以便在VSCode中显示颜色
$primary-color: rgba(0, 255, 255, 1);

// 文本颜色
$text-default: rgba(255, 255, 255, 0.7); // 默认文字颜色
$text-secondary: rgba(255, 255, 255, 0.6); // 次要文字颜色
$text-active: rgba(0, 255, 254, 1); // 激活状态文字颜色（亮青色）
$text-inactive: rgba(255, 255, 255, 0.5); // 未激活状态文字颜色
$text-hover: rgba(255, 255, 255, 1); // 悬停状态文字颜色
$text-disabled: rgba(255, 255, 255, 0.3); // 禁用状态文字颜色

// 导航/选项状态
$nav-active: rgba(0, 255, 254, 1); // 激活导航项（亮青色）
$nav-inactive: rgba(255, 255, 255, 0.6); // 未激活导航项
$nav-hover: rgba(0, 255, 254, 0.8); // 悬停导航项
$nav-active-border: rgba(0, 255, 254, 1); // 激活边框颜色

// 背景色
$bg-dark: rgba(4, 18, 50, 1); // 深色背景
$bg-medium: rgba(6, 31, 56, 1); // 中等深度背景
$bg-light: rgba(10, 42, 74, 0.8); // 较浅背景色
$bg-card: rgba(10, 42, 74, 0.2); // 卡片背景色
$bg-hover: rgba(255, 255, 255, 0.05); // 悬停背景色
$bg-panel: rgba(0, 0, 30, 0.85); // panel背景色

// 渐变背景
$primary-bg: linear-gradient(135deg, #0a1f35 0%, #0d3154 50%, #0a1f35 100%);
$muted-bg: linear-gradient(
  135deg,
  rgba(10, 26, 43, 0.8) 0%,
  rgba(11, 38, 64, 0.8) 50%,
  rgba(10, 26, 43, 0.8) 100%
);
$accent-bg: linear-gradient(135deg, rgba(0, 255, 254, 0.8) 0%, rgba(0, 200, 255, 0.8) 100%);

// 状态色
$success-color: rgba(76, 175, 80, 1); // 成功
$warning-color: rgba(255, 193, 7, 1); // 警告
$error-color: rgba(244, 67, 54, 1); // 错误
$info-color: rgba(33, 150, 243, 1); // 信息

// 边框和阴影
$border-color: rgba(255, 255, 255, 0.2); // 一般边框颜色
$border-color-light: rgba(255, 255, 255, 0.1); // 较浅边框颜色
$border-active: rgba(0, 255, 254, 1); // 激活边框颜色
$shadow-color: rgba(0, 0, 0, 0.5); // 阴影颜色
$glow-color: rgba(0, 255, 254, 0.5); // 发光效果颜色

$border-radius-base: 4px;
$border-radius-small: 2px;

// --- Tooltip Colors (新增) ---
@use 'sass:color';
$tooltip-bg-color: color.adjust($bg-panel, $lightness: 5%); // 比面板背景稍亮一些
$tooltip-text-color: $text-default;
$tooltip-border-color: rgba($primary-color, 0.4);

/* 推荐的全局盒模型设置 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

// --- 字体大小 ---
// rem 单位现在直接基于浏览器默认字体大小 (通常 1rem = 16px)
$font-size-root: 1rem; // 根大小，通常是 16px

$font-size-display: 2.5rem; // ≈ 28px, 用于非常大的标题
$font-size-headline: 2rem; // ≈ 22.4px, 用于主要标题
$font-size-title: 1.5rem; // ≈ 16.8px, 用于次级标题或重要文本
$font-size-large: 1.25rem; // ≈ 14px, 用于稍大的正文或标签
$font-size-base: 1rem; // ≈ 11.2px, 标准正文
$font-size-small: 0.875rem; // ≈ 9.8px, 用于辅助文本、小标签
$font-size-tiny: 0.75rem; // ≈ 8.4px, 用于非常小的文本、注释

// 针对组件的特定字体大小 (可以根据需要添加)
$font-size-button: $font-size-base;
$font-size-input: $font-size-base;
$font-size-label: $font-size-small;
$font-size-tooltip: $font-size-small;

// --- 面板特定字体大小 (直接使用期望的rem值) ---
$font-size-panel-title: 0.7rem; // 对应约 11.2px (作为面板最大字体, 基于1rem=16px)
$font-size-panel-subtitle: 0.65rem; // 对应约 10.4px
$font-size-panel-normal: 0.6rem; // 对应约 9.6px (面板常规字体)
$font-size-panel-label: 0.55rem; // 对应约 8.8px (标签等)
$font-size-panel-caption: 0.5rem; // 对应约 8.0px (说明文字)
$font-size-panel-micro: 0.45rem; // 对应约 7.2px (极小文字)

/* 超大型会议室大屏和特大型显示器 */
@media screen and (min-width: 3841px) {
  html {
    font-size: 28px;
  }
}

/* 4K显示器 */
@media screen and (min-width: 2561px) and (max-width: 3840px) {
  html {
    font-size: 24px;
  }
}

/* 2K和超宽显示器 */
@media screen and (min-width: 1921px) and (max-width: 2560px) {
  html {
    font-size: 20px;
  }
}

/* 标准桌面显示器 */
@media screen and (min-width: 1441px) and (max-width: 1920px) {
  html {
    font-size: 18px;
  }
}

/* 笔记本电脑和小型显示器 */
@media screen and (min-width: 1281px) and (max-width: 1440px) {
  html {
    font-size: 16px;
  }
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: transparent; // Hidden by default
  border-radius: 2px;
  transition: background 0.2s ease-in-out; // Smooth transition for appearance
}

// Show thumb when the scrollable element (or any part of it) is hovered
*:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 254, 0.3); // Visible when scrollable area is hovered
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 254, 0.5); // More visible when thumb itself is hovered
}

::-webkit-scrollbar-corner {
  background: transparent;
}

// --- 布局变量 ---
// 仪表盘布局相关变量
$dashboard-header-height: 2rem;
$dashboard-panel-width: 16rem;
$dashboard-panel-margin: 0.7rem;
$dashboard-flight-top-bar-height: 3rem;

// 监控卡片相关变量
$monitor-card-width: 300px;
$monitor-card-height: 200px;
$monitor-card-gap: 1rem;

// 面板间距和尺寸
$panel-padding: 0.5rem;
$panel-border-radius: 0.5rem;
$panel-header-height: 2rem;

// 引入 Element Plus 主题样式
// @import '../styles/element-plus-theme.scss';
