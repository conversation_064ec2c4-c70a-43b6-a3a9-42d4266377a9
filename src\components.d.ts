/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BarChart: typeof import('./components/common/BarChart/BarChart.vue')['default']
    BasePannel: typeof import('./components/common/BasePannel/BasePannel.vue')['default']
    ControButton: typeof import('./components/common/ControlButton/ControButton.vue')['default']
    CoordinateSearch: typeof import('./components/common/CoordinateSearch/CoordinateSearch.vue')['default']
    DataDisplay: typeof import('./components/common/DataDisplay/DataDisplay.vue')['default']
    DateRangePicker: typeof import('./components/common/DateRangePicker/DateRangePicker.vue')['default']
    DroneVideoCard: typeof import('./components/business/DroneVideoCard/DroneVideoCard.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    FlightTimeline: typeof import('./components/common/Timeline/FlightTimeline.vue')['default']
    GaugeChart: typeof import('./components/common/GaugeChart/GaugeChart.vue')['default']
    LineChart: typeof import('./components/common/LineChart/LineChart.vue')['default']
    MarsButton: typeof import('./components/common/MarsButton/MarsButton.vue')['default']
    PanelHeader: typeof import('./components/common/BasePannel/PanelHeader.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScreenCard: typeof import('./components/common/ScreenCard/ScreenCard.vue')['default']
    UIcon: typeof import('./components/common/UIcon/UIcon.vue')['default']
    VideoCard: typeof import('./components/common/VideoCard/VideoCard.vue')['default']
    WebSocketTester: typeof import('./components/debug/WebSocketTester.vue')['default']
  }
}
