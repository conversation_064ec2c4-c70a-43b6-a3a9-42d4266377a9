/**
 * Mars3D平台插件,支持气象 风向图 功能插件  mars3d-wind
 *
 * 版本信息：v3.5.19
 * 编译日期：2024-10-29 13:42
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：维璟（北京）科技有限公司 ，2023-06-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-wind"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';const _0x29607b=_0x5893;(function(_0x4d16c2,_0x253d97){const _0x4b8f5c=_0x5893,_0x5d57cb=_0x4d16c2();while(!![]){try{const _0x42270a=parseInt(_0x4b8f5c(0xf8))/0x1*(-parseInt(_0x4b8f5c(0x7a))/0x2)+-parseInt(_0x4b8f5c(0x142))/0x3*(parseInt(_0x4b8f5c(0xed))/0x4)+-parseInt(_0x4b8f5c(0x8d))/0x5*(parseInt(_0x4b8f5c(0x11a))/0x6)+parseInt(_0x4b8f5c(0xf4))/0x7+-parseInt(_0x4b8f5c(0x82))/0x8*(-parseInt(_0x4b8f5c(0x7c))/0x9)+-parseInt(_0x4b8f5c(0xd9))/0xa*(parseInt(_0x4b8f5c(0x14d))/0xb)+parseInt(_0x4b8f5c(0xc5))/0xc;if(_0x42270a===_0x253d97)break;else _0x5d57cb['push'](_0x5d57cb['shift']());}catch(_0x50b1ce){_0x5d57cb['push'](_0x5d57cb['shift']());}}}(_0x257e,0x86d11));function _interopNamespace(_0x3a47d0){const _0x576386=_0x5893;if(_0x3a47d0&&_0x3a47d0[_0x576386(0x84)])return _0x3a47d0;var _0x5c5331=Object[_0x576386(0xde)](null);return _0x3a47d0&&Object['keys'](_0x3a47d0)[_0x576386(0x77)](function(_0x31eff6){if(_0x31eff6!=='default'){var _0x42a724=Object['getOwnPropertyDescriptor'](_0x3a47d0,_0x31eff6);Object['defineProperty'](_0x5c5331,_0x31eff6,_0x42a724['get']?_0x42a724:{'enumerable':!![],'get':function(){return _0x3a47d0[_0x31eff6];}});}}),_0x5c5331['default']=_0x3a47d0,_0x5c5331;}var mars3d__namespace=_interopNamespace(mars3d);const Cesium$7=mars3d__namespace['Cesium'];function getU(_0x852eb1,_0x4aa28c){const _0x361d54=_0x852eb1*Math['cos'](Cesium$7['Math']['toRadians'](_0x4aa28c));return _0x361d54;}function getV(_0x3b53b7,_0x3eb875){const _0x2499e5=_0x3b53b7*Math['sin'](Cesium$7['Math']['toRadians'](_0x3eb875));return _0x2499e5;}function getSpeed(_0x382bd5,_0x118cbd){const _0x48857d=_0x5893,_0x474b93=Math['sqrt'](Math[_0x48857d(0xc9)](_0x382bd5,0x2)+Math['pow'](_0x118cbd,0x2));return _0x474b93;}function getDirection(_0x183ffb,_0x266fce){const _0x5b1c7f=_0x5893;let _0x432fc9=Cesium$7['Math']['toDegrees'](Math[_0x5b1c7f(0x15c)](_0x266fce,_0x183ffb));return _0x432fc9+=_0x432fc9<0x0?0x168:0x0,_0x432fc9;}var WindUtil={'__proto__':null,'getU':getU,'getV':getV,'getSpeed':getSpeed,'getDirection':getDirection};const Cesium$6=mars3d__namespace['Cesium'];class CustomPrimitive{constructor(_0x561e0b){const _0x1a8aa5=_0x5893;this['commandType']=_0x561e0b['commandType'],this['geometry']=_0x561e0b['geometry'],this['attributeLocations']=_0x561e0b[_0x1a8aa5(0x15d)],this['primitiveType']=_0x561e0b['primitiveType'],this['uniformMap']=_0x561e0b['uniformMap'],this['vertexShaderSource']=_0x561e0b['vertexShaderSource'],this[_0x1a8aa5(0x15b)]=_0x561e0b['fragmentShaderSource'],this['rawRenderState']=_0x561e0b['rawRenderState'],this['framebuffer']=_0x561e0b['framebuffer'],this['outputTexture']=_0x561e0b['outputTexture'],this['autoClear']=_0x561e0b['autoClear']??![],this['preExecute']=_0x561e0b['preExecute'],this['show']=!![],this['commandToExecute']=undefined,this[_0x1a8aa5(0xb6)]=undefined,this['autoClear']&&(this['clearCommand']=new Cesium$6[(_0x1a8aa5(0x10b))]({'color':new Cesium$6[(_0x1a8aa5(0xea))](0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':this['framebuffer'],'pass':Cesium$6[_0x1a8aa5(0x99)][_0x1a8aa5(0x96)]}));}['createCommand'](_0x5b19af){const _0x18b56c=_0x5893;switch(this['commandType']){case'Draw':{const _0x5d3989=Cesium$6['VertexArray']['fromGeometry']({'context':_0x5b19af,'geometry':this[_0x18b56c(0xee)],'attributeLocations':this['attributeLocations'],'bufferUsage':Cesium$6['BufferUsage']['STATIC_DRAW']}),_0x396fca=Cesium$6['ShaderProgram'][_0x18b56c(0x137)]({'context':_0x5b19af,'attributeLocations':this['attributeLocations'],'vertexShaderSource':this[_0x18b56c(0xdb)],'fragmentShaderSource':this[_0x18b56c(0x15b)]}),_0x988236=Cesium$6['RenderState'][_0x18b56c(0x137)](this['rawRenderState']);return new Cesium$6[(_0x18b56c(0xc4))]({'primitiveType':this['primitiveType'],'shaderProgram':_0x396fca,'vertexArray':_0x5d3989,'modelMatrix':Cesium$6['Matrix4'][_0x18b56c(0x140)],'renderState':_0x988236,'uniformMap':this[_0x18b56c(0xe0)],'castShadows':![],'receiveShadows':![],'framebuffer':this['framebuffer'],'pass':Cesium$6['Pass']['OPAQUE'],'pickOnly':!![],'owner':this});}case _0x18b56c(0xc3):{return new Cesium$6['ComputeCommand']({'owner':this,'fragmentShaderSource':this['fragmentShaderSource'],'uniformMap':this['uniformMap'],'outputTexture':this['outputTexture'],'persists':!![]});}}}[_0x29607b(0x167)](_0x4f31cc,_0x42de5c){const _0x19d163=_0x29607b;this['geometry']=_0x42de5c;const _0x438548=Cesium$6[_0x19d163(0x83)]['fromGeometry']({'context':_0x4f31cc,'geometry':this[_0x19d163(0xee)],'attributeLocations':this['attributeLocations'],'bufferUsage':Cesium$6[_0x19d163(0xcd)]['STATIC_DRAW']});this['commandToExecute']['vertexArray']=_0x438548;}['update'](_0x54820b){const _0x41af34=_0x29607b;if(!this[_0x41af34(0x116)])return;if(_0x54820b['mode']!==Cesium$6['SceneMode']['SCENE3D'])return;!Cesium$6['defined'](this[_0x41af34(0x127)])&&(this['commandToExecute']=this[_0x41af34(0x16b)](_0x54820b['context'])),Cesium$6['defined'](this['preExecute'])&&this['preExecute'](),Cesium$6['defined'](this[_0x41af34(0xb6)])&&_0x54820b['commandList']['push'](this['clearCommand']),_0x54820b['commandList']['push'](this['commandToExecute']);}[_0x29607b(0x14a)](){return![];}['destroy'](){const _0xa5add=_0x29607b;if(this[_0xa5add(0xb6)]){var _0x3ed78c,_0x287530;(_0x3ed78c=this[_0xa5add(0xb6)])!==null&&_0x3ed78c!==void 0x0&&_0x3ed78c['vertexArray']&&this['clearCommand'][_0xa5add(0x97)][_0xa5add(0x118)](),(_0x287530=this['clearCommand'])!==null&&_0x287530!==void 0x0&&_0x287530['shaderProgram']&&this['clearCommand']['shaderProgram']['destroy'](),delete this['clearCommand'];}return this['commandToExecute']&&(this['commandToExecute']['vertexArray']&&this['commandToExecute']['vertexArray'][_0xa5add(0x118)](),this['commandToExecute']['shaderProgram']&&this[_0xa5add(0x127)]['shaderProgram'][_0xa5add(0x118)](),delete this[_0xa5add(0x127)]),Cesium$6['destroyObject'](this);}}function _0x5893(_0x3d7c19,_0x4f8f88){const _0x257e20=_0x257e();return _0x5893=function(_0x58933f,_0x334ca0){_0x58933f=_0x58933f-0x77;let _0x3084cd=_0x257e20[_0x58933f];return _0x3084cd;},_0x5893(_0x3d7c19,_0x4f8f88);}const Cesium$5=mars3d__namespace['Cesium'],Util=(function(){const _0x5e04a9=function(){const _0x2af33a=_0x5893,_0x20c99c=new Cesium$5[(_0x2af33a(0xf1))]({'attributes':new Cesium$5['GeometryAttributes']({'position':new Cesium$5[(_0x2af33a(0xdf))]({'componentDatatype':Cesium$5[_0x2af33a(0xbc)]['FLOAT'],'componentsPerAttribute':0x3,'values':new Float32Array([-0x1,-0x1,0x0,0x1,-0x1,0x0,0x1,0x1,0x0,-0x1,0x1,0x0])}),'st':new Cesium$5[(_0x2af33a(0xdf))]({'componentDatatype':Cesium$5['ComponentDatatype']['FLOAT'],'componentsPerAttribute':0x2,'values':new Float32Array([0x0,0x0,0x1,0x0,0x1,0x1,0x0,0x1])})}),'indices':new Uint32Array([0x3,0x2,0x0,0x0,0x2,0x1])});return _0x20c99c;},_0x46b318=function(_0x3ac3fc,_0x31f4b7){const _0x2f1c1d=_0x5893;if(Cesium$5['defined'](_0x31f4b7)){const _0x226855={};_0x226855[_0x2f1c1d(0x9c)]=_0x31f4b7,_0x3ac3fc['source']=_0x226855;}const _0xb77832=new Cesium$5[(_0x2f1c1d(0x7f))](_0x3ac3fc);return _0xb77832;},_0x18cb38=function(_0x31cb25,_0x535c27,_0x329650){const _0x5f01b2=new Cesium$5['Framebuffer']({'context':_0x31cb25,'colorTextures':[_0x535c27],'depthTexture':_0x329650});return _0x5f01b2;},_0x232c9a=function(_0x32b56e){const _0xa1f545=_0x5893,_0x3b71f2=!![],_0x26574f=![],_0x3da3cb={'viewport':_0x32b56e[_0xa1f545(0x79)],'depthTest':_0x32b56e['depthTest'],'depthMask':_0x32b56e['depthMask'],'blending':_0x32b56e['blending']},_0x58f456=Cesium$5['Appearance']['getDefaultRenderState'](_0x3b71f2,_0x26574f,_0x3da3cb);return _0x58f456;},_0x3b6ba6=function(_0x2bebee){const _0x46b946=_0x5893,_0x25ab9b={},_0x3479b9=Cesium$5[_0x46b946(0x131)]['mod'](_0x2bebee['west'],Cesium$5[_0x46b946(0x131)][_0x46b946(0xe8)]),_0x231cf5=Cesium$5[_0x46b946(0x131)]['mod'](_0x2bebee['east'],Cesium$5['Math']['TWO_PI']),_0x57cbbf=_0x2bebee[_0x46b946(0x15a)];let _0x591a23,_0xd3cdda;_0x57cbbf>Cesium$5['Math']['THREE_PI_OVER_TWO']?(_0x591a23=0x0,_0xd3cdda=Cesium$5['Math']['TWO_PI']):_0x231cf5-_0x3479b9<_0x57cbbf?(_0x591a23=_0x3479b9,_0xd3cdda=_0x3479b9+_0x57cbbf):(_0x591a23=_0x3479b9,_0xd3cdda=_0x231cf5);_0x25ab9b['lon']={'min':Cesium$5[_0x46b946(0x131)]['toDegrees'](_0x591a23),'max':Cesium$5['Math'][_0x46b946(0xec)](_0xd3cdda)};const _0x5aec97=_0x2bebee[_0x46b946(0x144)],_0x2bb895=_0x2bebee[_0x46b946(0x90)],_0x3e74c6=_0x2bebee[_0x46b946(0xad)],_0x50b1a5=_0x3e74c6>Cesium$5['Math']['PI']/0xc?_0x3e74c6/0x2:0x0;let _0xeecfe2=Cesium$5[_0x46b946(0x131)][_0x46b946(0x163)](_0x5aec97-_0x50b1a5),_0x39c60a=Cesium$5[_0x46b946(0x131)]['clampToLatitudeRange'](_0x2bb895+_0x50b1a5);return _0xeecfe2<-Cesium$5['Math']['PI_OVER_THREE']&&(_0xeecfe2=-Cesium$5['Math']['PI_OVER_TWO']),_0x39c60a>Cesium$5['Math']['PI_OVER_THREE']&&(_0x39c60a=Cesium$5['Math']['PI_OVER_TWO']),_0x25ab9b['lat']={'min':Cesium$5['Math']['toDegrees'](_0xeecfe2),'max':Cesium$5[_0x46b946(0x131)][_0x46b946(0xec)](_0x39c60a)},_0x25ab9b;};return{'getFullscreenQuad':_0x5e04a9,'createTexture':_0x46b318,'createFramebuffer':_0x18cb38,'createRawRenderState':_0x232c9a,'viewRectangleToLonLatRange':_0x3b6ba6};}());var segmentDraw_vert='in\x20vec2\x20st;\x0a//\x20it\x20is\x20not\x20normal\x20itself,\x20but\x20used\x20to\x20control\x20normal\x0ain\x20vec3\x20normal;\x20//\x20(point\x20to\x20use,\x20offset\x20sign,\x20not\x20used\x20component)\x0a\x0auniform\x20sampler2D\x20currentParticlesPosition;\x0auniform\x20sampler2D\x20postProcessingPosition;\x0auniform\x20sampler2D\x20postProcessingSpeed;\x0a\x0auniform\x20float\x20particleHeight;\x0a\x0auniform\x20float\x20aspect;\x0auniform\x20float\x20pixelSize;\x0auniform\x20float\x20lineWidth;\x0a\x0aout\x20float\x20speedNormalization;\x0a\x0avec3\x20convertCoordinate(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20WGS84\x20(lon,\x20lat,\x20lev)\x20->\x20ECEF\x20(x,\x20y,\x20z)\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Geographic_coordinate_conversion#From_geodetic_to_ECEF_coordinates\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20WGS\x2084\x20geometric\x20constants\x0a\x20\x20\x20\x20float\x20a\x20=\x206378137.0;\x20//\x20Semi-major\x20axis\x0a\x20\x20\x20\x20float\x20b\x20=\x206356752.3142;\x20//\x20Semi-minor\x20axis\x0a\x20\x20\x20\x20float\x20e2\x20=\x206.69437999014e-3;\x20//\x20First\x20eccentricity\x20squared\x0a\x0a\x20\x20\x20\x20float\x20latitude\x20=\x20radians(lonLatLev.y);\x0a\x20\x20\x20\x20float\x20longitude\x20=\x20radians(lonLatLev.x);\x0a\x0a\x20\x20\x20\x20float\x20cosLat\x20=\x20cos(latitude);\x0a\x20\x20\x20\x20float\x20sinLat\x20=\x20sin(latitude);\x0a\x20\x20\x20\x20float\x20cosLon\x20=\x20cos(longitude);\x0a\x20\x20\x20\x20float\x20sinLon\x20=\x20sin(longitude);\x0a\x0a\x20\x20\x20\x20float\x20N_Phi\x20=\x20a\x20/\x20sqrt(1.0\x20-\x20e2\x20*\x20sinLat\x20*\x20sinLat);\x0a\x20\x20\x20\x20float\x20h\x20=\x20particleHeight;\x20//\x20it\x20should\x20be\x20high\x20enough\x20otherwise\x20the\x20particle\x20may\x20not\x20pass\x20the\x20terrain\x20depth\x20test\x0a\x0a\x20\x20\x20\x20vec3\x20cartesian\x20=\x20vec3(0.0);\x0a\x20\x20\x20\x20cartesian.x\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20cosLon;\x0a\x20\x20\x20\x20cartesian.y\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20sinLon;\x0a\x20\x20\x20\x20cartesian.z\x20=\x20((b\x20*\x20b)\x20/\x20(a\x20*\x20a)\x20*\x20N_Phi\x20+\x20h)\x20*\x20sinLat;\x0a\x20\x20\x20\x20return\x20cartesian;\x0a}\x0a\x0avec4\x20calcProjectedCoordinate(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20the\x20range\x20of\x20longitude\x20in\x20Cesium\x20is\x20[-180,\x20180]\x20but\x20the\x20range\x20of\x20longitude\x20in\x20the\x20NetCDF\x20file\x20is\x20[0,\x20360]\x0a\x20\x20\x20\x20//\x20[0,\x20180]\x20is\x20corresponding\x20to\x20[0,\x20180]\x20and\x20[180,\x20360]\x20is\x20corresponding\x20to\x20[-180,\x200]\x0a\x20\x20\x20\x20lonLatLev.x\x20=\x20mod(lonLatLev.x\x20+\x20180.0,\x20360.0)\x20-\x20180.0;\x0a\x20\x20\x20\x20vec3\x20particlePosition\x20=\x20convertCoordinate(lonLatLev);\x0a\x20\x20\x20\x20vec4\x20projectedCoordinate\x20=\x20czm_modelViewProjection\x20*\x20vec4(particlePosition,\x201.0);\x0a\x20\x20\x20\x20return\x20projectedCoordinate;\x0a}\x0a\x0avec4\x20calcOffset(vec4\x20currentProjectedCoordinate,\x20vec4\x20nextProjectedCoordinate,\x20float\x20offsetSign)\x20{\x0a\x20\x20\x20\x20vec2\x20aspectVec2\x20=\x20vec2(aspect,\x201.0);\x0a\x20\x20\x20\x20vec2\x20currentXY\x20=\x20(currentProjectedCoordinate.xy\x20/\x20currentProjectedCoordinate.w)\x20*\x20aspectVec2;\x0a\x20\x20\x20\x20vec2\x20nextXY\x20=\x20(nextProjectedCoordinate.xy\x20/\x20nextProjectedCoordinate.w)\x20*\x20aspectVec2;\x0a\x0a\x20\x20\x20\x20float\x20offsetLength\x20=\x20lineWidth\x20/\x202.0;\x0a\x20\x20\x20\x20vec2\x20direction\x20=\x20normalize(nextXY\x20-\x20currentXY);\x0a\x20\x20\x20\x20vec2\x20normalVector\x20=\x20vec2(-direction.y,\x20direction.x);\x0a\x20\x20\x20\x20normalVector.x\x20=\x20normalVector.x\x20/\x20aspect;\x0a\x20\x20\x20\x20normalVector\x20=\x20offsetLength\x20*\x20normalVector;\x0a\x0a\x20\x20\x20\x20vec4\x20offset\x20=\x20vec4(offsetSign\x20*\x20normalVector,\x200.0,\x200.0);\x0a\x20\x20\x20\x20return\x20offset;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec2\x20particleIndex\x20=\x20st;\x0a\x0a\x20\x20\x20\x20vec3\x20currentPosition\x20=\x20texture(currentParticlesPosition,\x20particleIndex).rgb;\x0a\x20\x20\x20\x20vec4\x20nextPosition\x20=\x20texture(postProcessingPosition,\x20particleIndex);\x0a\x0a\x20\x20\x20\x20vec4\x20currentProjectedCoordinate\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20vec4\x20nextProjectedCoordinate\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20if\x20(nextPosition.w\x20>\x200.0)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20currentProjectedCoordinate\x20=\x20calcProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20\x20\x20\x20\x20nextProjectedCoordinate\x20=\x20calcProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20currentProjectedCoordinate\x20=\x20calcProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20\x20\x20\x20\x20nextProjectedCoordinate\x20=\x20calcProjectedCoordinate(nextPosition.xyz);\x0a\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20float\x20pointToUse\x20=\x20normal.x;\x20//\x20-1\x20is\x20currentProjectedCoordinate\x20and\x20+1\x20is\x20nextProjectedCoordinate\x0a\x20\x20\x20\x20float\x20offsetSign\x20=\x20normal.y;\x0a\x0a\x20\x20\x20\x20vec4\x20offset\x20=\x20pixelSize\x20*\x20calcOffset(currentProjectedCoordinate,\x20nextProjectedCoordinate,\x20offsetSign);\x0a\x20\x20\x20\x20if\x20(pointToUse\x20<\x200.0)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20gl_Position\x20=\x20currentProjectedCoordinate\x20+\x20offset;\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20gl_Position\x20=\x20nextProjectedCoordinate\x20+\x20offset;\x0a\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20speedNormalization\x20=\x20texture(postProcessingSpeed,\x20particleIndex).a;\x0a}\x0a',segmentDraw_frag='uniform\x20sampler2D\x20colorTable;\x0a\x0ain\x20float\x20speedNormalization;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20out_FragColor\x20=\x20texture(colorTable,\x20vec2(speedNormalization,\x200.0));\x0a}\x0a',fullscreen_vert=_0x29607b(0x9f),trailDraw_frag='uniform\x20sampler2D\x20segmentsColorTexture;\x0auniform\x20sampler2D\x20segmentsDepthTexture;\x0a\x0auniform\x20sampler2D\x20currentTrailsColor;\x0auniform\x20sampler2D\x20trailsDepthTexture;\x0a\x0auniform\x20float\x20fadeOpacity;\x0a\x0ain\x20vec2\x20textureCoordinate;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec4\x20pointsColor\x20=\x20texture(segmentsColorTexture,\x20textureCoordinate);\x0a\x20\x20\x20\x20vec4\x20trailsColor\x20=\x20texture(currentTrailsColor,\x20textureCoordinate);\x0a\x0a\x20\x20\x20\x20trailsColor\x20=\x20floor(fadeOpacity\x20*\x20255.0\x20*\x20trailsColor)\x20/\x20255.0;\x20//\x20make\x20sure\x20the\x20trailsColor\x20will\x20be\x20strictly\x20decreased\x0a\x0a\x20\x20\x20\x20float\x20pointsDepth\x20=\x20texture(segmentsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20\x20\x20float\x20trailsDepth\x20=\x20texture(trailsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20\x20\x20float\x20globeDepth\x20=\x20czm_unpackDepth(texture(czm_globeDepthTexture,\x20textureCoordinate));\x0a\x0a\x20\x20\x20\x20out_FragColor\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20if\x20(pointsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20out_FragColor\x20+\x20pointsColor;\x0a\x20\x20\x20\x20}\x0a\x20\x20\x20\x20if\x20(trailsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20out_FragColor\x20+\x20trailsColor;\x0a\x20\x20\x20\x20}\x0a\x20\x20\x20\x20gl_FragDepth\x20=\x20min(pointsDepth,\x20trailsDepth);\x0a}\x0a',screenDraw_frag=_0x29607b(0xef);function _0x257e(){const _0x12ebdd=['ymin','arrayBufferView','random','mouseUp','in\x20vec3\x20position;\x0ain\x20vec2\x20st;\x0a\x0aout\x20vec2\x20textureCoordinate;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20textureCoordinate\x20=\x20st;\x0a\x20\x20\x20\x20gl_Position\x20=\x20vec4(position,\x201.0);\x0a}\x0a','unbindEvent','addEventListener','tlng','context','mouse_down','maxAge','particlesNumber','Rectangle','fill','globeBoundingSphere','globalCompositeOperation','_onMouseDownEvent','red','height','WindLayer','camera','wheel','postProcessingPosition','auto','createRenderingPrimitives','uniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0auniform\x20sampler2D\x20currentParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20lengthOfLonLat(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20unit\x20conversion:\x20meters\x20->\x20longitude\x20latitude\x20degrees\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Geographic_coordinate_system#Length_of_a_degree\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20the\x20length\x20of\x20a\x20degree\x20of\x20latitude\x20and\x20longitude\x20in\x20meters\x0a\x20\x20\x20\x20float\x20latitude\x20=\x20radians(lonLatLev.y);\x0a\x0a\x20\x20\x20\x20float\x20term1\x20=\x20111132.92;\x0a\x20\x20\x20\x20float\x20term2\x20=\x20559.82\x20*\x20cos(2.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term3\x20=\x201.175\x20*\x20cos(4.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term4\x20=\x200.0023\x20*\x20cos(6.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20latLength\x20=\x20term1\x20-\x20term2\x20+\x20term3\x20-\x20term4;\x0a\x0a\x20\x20\x20\x20float\x20term5\x20=\x20111412.84\x20*\x20cos(latitude);\x0a\x20\x20\x20\x20float\x20term6\x20=\x2093.5\x20*\x20cos(3.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term7\x20=\x200.118\x20*\x20cos(5.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20longLength\x20=\x20term5\x20-\x20term6\x20+\x20term7;\x0a\x0a\x20\x20\x20\x20return\x20vec2(longLength,\x20latLength);\x0a}\x0a\x0avoid\x20updatePosition(vec3\x20lonLatLev,\x20vec3\x20speed)\x20{\x0a\x20\x20\x20\x20vec2\x20lonLatLength\x20=\x20lengthOfLonLat(lonLatLev);\x0a\x20\x20\x20\x20float\x20u\x20=\x20speed.x\x20/\x20lonLatLength.x;\x0a\x20\x20\x20\x20float\x20v\x20=\x20speed.y\x20/\x20lonLatLength.y;\x0a\x20\x20\x20\x20float\x20w\x20=\x200.0;\x0a\x20\x20\x20\x20vec3\x20windVectorInLonLatLev\x20=\x20vec3(u,\x20v,\x20w);\x0a\x0a\x20\x20\x20\x20vec3\x20nextParticle\x20=\x20lonLatLev\x20+\x20windVectorInLonLatLev;\x0a\x0a\x20\x20\x20\x20out_FragColor\x20=\x20vec4(nextParticle,\x200.0);\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20vec3\x20lonLatLev\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20speed\x20=\x20texture(currentParticlesSpeed,\x20v_textureCoordinates).rgb;\x0a\x0a\x20\x20\x20\x20updatePosition(lonLatLev,\x20speed);\x0a}\x0a','FLOAT','clearCommand','init','_onMapWhellEvent','cols','refreshTimer','EventType','ComponentDatatype','windField','wind','left','levmin','onmessage','lat','Compute','DrawCommand','7463832NIgtKO','options','bind','globe','pow','PixelFormat','clearFramebuffers','updateSpeed','BufferUsage','nextParticlesPosition','PrimitiveCollection','LayerUtil','worker','fillRect','_updateIng','windData','canvas','setData','LINEAR','_maxAge','30rXMbvc','lon','vertexShaderSource','drawingBufferHeight','pointer-events','create','GeometryAttribute','uniformMap','ShaderSource','textures','redraw','lighter','ymax','dimensions','trails','TWO_PI','clientWidth','Color','fadeOpacity','toDegrees','2741212JCjAuo','geometry','uniform\x20sampler2D\x20trailsColorTexture;\x0auniform\x20sampler2D\x20trailsDepthTexture;\x0a\x0ain\x20vec2\x20textureCoordinate;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec4\x20trailsColor\x20=\x20texture(trailsColorTexture,\x20textureCoordinate);\x0a\x20\x20\x20\x20float\x20trailsDepth\x20=\x20texture(trailsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20\x20\x20float\x20globeDepth\x20=\x20czm_unpackDepth(texture(czm_globeDepthTexture,\x20textureCoordinate));\x0a\x0a\x20\x20\x20\x20if\x20(trailsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20trailsColor;\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20}\x0a}\x0a','_calcUV','Geometry','vdata','_map','6138587KbmgGa','Cesium','NEAREST','setOptions','1OngTBJ','windTextures','globalAlpha','update','_randomParticle','style','hidden','mars3d-canvasWind','postMessage','colors','particleHeight','getContext','add','canvasWind','mouseDown','mode','color','getOptions','LUMINANCE','ClearCommand','canvasResize','resize','createRawRenderState','particlesComputing','age','DomUtil','dropRateBump','_drawLines','viewRectangleToLonLatRange','particlesRendering','show','data','destroy','position','1229886FogtYs','log','pixelSize','mouseMove','TextureMinificationFilter','screen','lng','min','fromDegrees','removeAll','frameTime','xmax','lev','commandToExecute','_data','getColorTexture','//\x20the\x20size\x20of\x20UV\x20textures:\x20width\x20=\x20lon,\x20height\x20=\x20lat*lev\x0auniform\x20sampler2D\x20U;\x20//\x20eastward\x20wind\x0auniform\x20sampler2D\x20V;\x20//\x20northward\x20wind\x0a\x0auniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0a\x0auniform\x20vec3\x20dimension;\x20//\x20(lon,\x20lat,\x20lev)\x0auniform\x20vec3\x20minimum;\x20//\x20minimum\x20of\x20each\x20dimension\x0auniform\x20vec3\x20maximum;\x20//\x20maximum\x20of\x20each\x20dimension\x0auniform\x20vec3\x20interval;\x20//\x20interval\x20of\x20each\x20dimension\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20mapPositionToNormalizedIndex2D(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20ensure\x20the\x20range\x20of\x20longitude\x20and\x20latitude\x0a\x20\x20\x20\x20lonLatLev.x\x20=\x20mod(lonLatLev.x,\x20360.0);\x0a\x20\x20\x20\x20lonLatLev.y\x20=\x20clamp(lonLatLev.y,\x20-90.0,\x2090.0);\x0a\x0a\x20\x20\x20\x20vec3\x20index3D\x20=\x20vec3(0.0);\x0a\x20\x20\x20\x20index3D.x\x20=\x20(lonLatLev.x\x20-\x20minimum.x)\x20/\x20interval.x;\x0a\x20\x20\x20\x20index3D.y\x20=\x20(lonLatLev.y\x20-\x20minimum.y)\x20/\x20interval.y;\x0a\x20\x20\x20\x20index3D.z\x20=\x20(lonLatLev.z\x20-\x20minimum.z)\x20/\x20interval.z;\x0a\x0a\x20\x20\x20\x20//\x20the\x20st\x20texture\x20coordinate\x20corresponding\x20to\x20(col,\x20row)\x20index\x0a\x20\x20\x20\x20//\x20example\x0a\x20\x20\x20\x20//\x20data\x20array\x20is\x20[0,\x201,\x202,\x203,\x204,\x205],\x20width\x20=\x203,\x20height\x20=\x202\x0a\x20\x20\x20\x20//\x20the\x20content\x20of\x20texture\x20will\x20be\x0a\x20\x20\x20\x20//\x20t\x201.0\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x20\x203\x204\x205\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x20\x200\x201\x202\x0a\x20\x20\x20\x20//\x20\x20\x200.0------1.0\x20s\x0a\x0a\x20\x20\x20\x20vec2\x20index2D\x20=\x20vec2(index3D.x,\x20index3D.z\x20*\x20dimension.y\x20+\x20index3D.y);\x0a\x20\x20\x20\x20vec2\x20normalizedIndex2D\x20=\x20vec2(index2D.x\x20/\x20dimension.x,\x20index2D.y\x20/\x20(dimension.y\x20*\x20dimension.z));\x0a\x20\x20\x20\x20return\x20normalizedIndex2D;\x0a}\x0a\x0afloat\x20getWind(sampler2D\x20windTexture,\x20vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20vec2\x20normalizedIndex2D\x20=\x20mapPositionToNormalizedIndex2D(lonLatLev);\x0a\x20\x20\x20\x20float\x20result\x20=\x20texture(windTexture,\x20normalizedIndex2D).r;\x0a\x20\x20\x20\x20return\x20result;\x0a}\x0a\x0aconst\x20mat4\x20kernelMatrix\x20=\x20mat4(\x0a\x20\x20\x20\x200.0,\x20-1.0,\x202.0,\x20-1.0,\x20//\x20first\x20column\x0a\x20\x20\x20\x202.0,\x200.0,\x20-5.0,\x203.0,\x20//\x20second\x20column\x0a\x20\x20\x20\x200.0,\x201.0,\x204.0,\x20-3.0,\x20//\x20third\x20column\x0a\x20\x20\x20\x200.0,\x200.0,\x20-1.0,\x201.0\x20//\x20fourth\x20column\x0a);\x0afloat\x20oneDimensionInterpolation(float\x20t,\x20float\x20p0,\x20float\x20p1,\x20float\x20p2,\x20float\x20p3)\x20{\x0a\x20\x20\x20\x20vec4\x20tVec4\x20=\x20vec4(1.0,\x20t,\x20t\x20*\x20t,\x20t\x20*\x20t\x20*\x20t);\x0a\x20\x20\x20\x20tVec4\x20=\x20tVec4\x20/\x202.0;\x0a\x20\x20\x20\x20vec4\x20pVec4\x20=\x20vec4(p0,\x20p1,\x20p2,\x20p3);\x0a\x20\x20\x20\x20return\x20dot((tVec4\x20*\x20kernelMatrix),\x20pVec4);\x0a}\x0a\x0afloat\x20calculateB(sampler2D\x20windTexture,\x20float\x20t,\x20float\x20lon,\x20float\x20lat,\x20float\x20lev)\x20{\x0a\x20\x20\x20\x20float\x20lon0\x20=\x20floor(lon)\x20-\x201.0\x20*\x20interval.x;\x0a\x20\x20\x20\x20float\x20lon1\x20=\x20floor(lon);\x0a\x20\x20\x20\x20float\x20lon2\x20=\x20floor(lon)\x20+\x201.0\x20*\x20interval.x;\x0a\x20\x20\x20\x20float\x20lon3\x20=\x20floor(lon)\x20+\x202.0\x20*\x20interval.x;\x0a\x0a\x20\x20\x20\x20float\x20p0\x20=\x20getWind(windTexture,\x20vec3(lon0,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p1\x20=\x20getWind(windTexture,\x20vec3(lon1,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p2\x20=\x20getWind(windTexture,\x20vec3(lon2,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p3\x20=\x20getWind(windTexture,\x20vec3(lon3,\x20lat,\x20lev));\x0a\x0a\x20\x20\x20\x20return\x20oneDimensionInterpolation(t,\x20p0,\x20p1,\x20p2,\x20p3);\x0a}\x0a\x0afloat\x20interpolateOneTexture(sampler2D\x20windTexture,\x20vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20float\x20lon\x20=\x20lonLatLev.x;\x0a\x20\x20\x20\x20float\x20lat\x20=\x20lonLatLev.y;\x0a\x20\x20\x20\x20float\x20lev\x20=\x20lonLatLev.z;\x0a\x0a\x20\x20\x20\x20float\x20lat0\x20=\x20floor(lat)\x20-\x201.0\x20*\x20interval.y;\x0a\x20\x20\x20\x20float\x20lat1\x20=\x20floor(lat);\x0a\x20\x20\x20\x20float\x20lat2\x20=\x20floor(lat)\x20+\x201.0\x20*\x20interval.y;\x0a\x20\x20\x20\x20float\x20lat3\x20=\x20floor(lat)\x20+\x202.0\x20*\x20interval.y;\x0a\x0a\x20\x20\x20\x20vec2\x20coefficient\x20=\x20lonLatLev.xy\x20-\x20floor(lonLatLev.xy);\x0a\x20\x20\x20\x20float\x20b0\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat0,\x20lev);\x0a\x20\x20\x20\x20float\x20b1\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat1,\x20lev);\x0a\x20\x20\x20\x20float\x20b2\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat2,\x20lev);\x0a\x20\x20\x20\x20float\x20b3\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat3,\x20lev);\x0a\x0a\x20\x20\x20\x20return\x20oneDimensionInterpolation(coefficient.y,\x20b0,\x20b1,\x20b2,\x20b3);\x0a}\x0a\x0avec3\x20bicubic(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20https://en.wikipedia.org/wiki/Bicubic_interpolation#Bicubic_convolution_algorithm\x0a\x20\x20\x20\x20float\x20u\x20=\x20interpolateOneTexture(U,\x20lonLatLev);\x0a\x20\x20\x20\x20float\x20v\x20=\x20interpolateOneTexture(V,\x20lonLatLev);\x0a\x20\x20\x20\x20float\x20w\x20=\x200.0;\x0a\x20\x20\x20\x20return\x20vec3(u,\x20v,\x20w);\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20vec3\x20lonLatLev\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20windVector\x20=\x20bicubic(lonLatLev);\x0a\x20\x20\x20\x20out_FragColor\x20=\x20vec4(windVector,\x200.0);\x0a}\x0a','fixedHeight','postProcessingSpeed','currentParticlesSpeed','PrimitiveType','getUVByXY','TextureMagnificationFilter','Math','particlesTextureSize','canvasContext','nextTrails','randomBetween','applyViewerParameters','fromCache','particles','_pointerEvents','getRandomLatLng','viewerParameters','createTexture','createSegmentsGeometry','clientHeight','framebuffers','IDENTITY','round','3ZAzqkL','Cartesian3','south','Cartesian2','UNSIGNED_INT','grid','lineWidth','keys','isDestroyed','length','createParticlesTextures','2117434tVVOtq','segments','udata','levmax','_onMouseMoveEvent','rgb(206,255,255)','particleSystem','refreshParticles','getWind','currentParticlesPosition','0px','uniform\x20sampler2D\x20currentParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0auniform\x20sampler2D\x20particlesWind;\x0a\x0a//\x20used\x20to\x20calculate\x20the\x20wind\x20norm\x0auniform\x20vec2\x20uSpeedRange;\x20//\x20(min,\x20max);\x0auniform\x20vec2\x20vSpeedRange;\x0auniform\x20float\x20pixelSize;\x0auniform\x20float\x20speedFactor;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0afloat\x20calculateWindNorm(vec3\x20speed)\x20{\x0a\x20\x20\x20\x20vec3\x20percent\x20=\x20vec3(0.0);\x0a\x20\x20\x20\x20percent.x\x20=\x20(speed.x\x20-\x20uSpeedRange.x)\x20/\x20(uSpeedRange.y\x20-\x20uSpeedRange.x);\x0a\x20\x20\x20\x20percent.y\x20=\x20(speed.y\x20-\x20vSpeedRange.x)\x20/\x20(vSpeedRange.y\x20-\x20vSpeedRange.x);\x0a\x20\x20\x20\x20float\x20normalization\x20=\x20length(percent);\x0a\x0a\x20\x20\x20\x20return\x20normalization;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20//\x20vec3\x20currentSpeed\x20=\x20texture(currentParticlesSpeed,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20windVector\x20=\x20texture(particlesWind,\x20v_textureCoordinates).rgb;\x0a\x0a\x20\x20\x20\x20vec4\x20nextSpeed\x20=\x20vec4(speedFactor\x20*\x20pixelSize\x20*\x20windVector,\x20calculateWindNorm(windVector));\x0a\x20\x20\x20\x20out_FragColor\x20=\x20nextSpeed;\x0a}\x0a','particlesTextures','width','fragmentShaderSource','atan2','attributeLocations','Draw','canvasWidth','computeViewRectangle','tlat','particlesWind','clampToLatitudeRange','xmin','currentTrails','PixelDatatype','setGeometry','push','off','_calc_speedRate','createCommand','primitives','forEach','getParticles','viewport','516506QHsEvX','speedRate','7013709ZXcdrD','remove','Sampler','Texture','UNSIGNED_BYTE','createRenderingFramebuffers','8zoqPqK','VertexArray','__esModule','_updateIng2','_onMouseUpEvent','destroyParticlesTextures','none','setDate','lonRange','max','clear','5cuItmb','reverse','mouse_move','north','scene','rows','speedFactor','toGridXY','visibility','OPAQUE','vertexArray','lineTo','Pass','RGBA'];_0x257e=function(){return _0x12ebdd;};return _0x257e();}const Cesium$4=mars3d__namespace['Cesium'];class ParticlesRendering{constructor(_0x215fd2,_0x2029d8,_0x3a7e4e,_0x3f26aa,_0x57976a){const _0x518fd4=_0x29607b;this['createRenderingTextures'](_0x215fd2,_0x2029d8,_0x3a7e4e['colors']),this[_0x518fd4(0x81)](_0x215fd2),this[_0x518fd4(0xb3)](_0x215fd2,_0x3a7e4e,_0x3f26aa,_0x57976a);}['createRenderingTextures'](_0x422637,_0x545129,_0x2c05ec){const _0xef5fea=_0x29607b,_0x2b8586={'context':_0x422637,'width':_0x422637['drawingBufferWidth'],'height':_0x422637['drawingBufferHeight'],'pixelFormat':Cesium$4['PixelFormat']['RGBA'],'pixelDatatype':Cesium$4['PixelDatatype'][_0xef5fea(0x80)]},_0x32ff7b={'context':_0x422637,'width':_0x422637['drawingBufferWidth'],'height':_0x422637[_0xef5fea(0xdc)],'pixelFormat':Cesium$4[_0xef5fea(0xca)]['DEPTH_COMPONENT'],'pixelDatatype':Cesium$4['PixelDatatype'][_0xef5fea(0x146)]},_0x59fc8e=_0x2c05ec[_0xef5fea(0x14b)],_0x3f37e6=new Float32Array(_0x59fc8e*0x3);for(let _0x2a9fab=0x0;_0x2a9fab<_0x59fc8e;_0x2a9fab++){const _0x56f1c6=Cesium$4['Color']['fromCssColorString'](_0x2c05ec[_0x2a9fab]);_0x3f37e6[0x3*_0x2a9fab]=_0x56f1c6[_0xef5fea(0xac)],_0x3f37e6[0x3*_0x2a9fab+0x1]=_0x56f1c6['green'],_0x3f37e6[0x3*_0x2a9fab+0x2]=_0x56f1c6['blue'];}const _0x1c41ea={'context':_0x422637,'width':_0x59fc8e,'height':0x1,'pixelFormat':Cesium$4[_0xef5fea(0xca)]['RGB'],'pixelDatatype':Cesium$4['PixelDatatype'][_0xef5fea(0xb5)],'sampler':new Cesium$4['Sampler']({'minificationFilter':Cesium$4[_0xef5fea(0x11e)]['LINEAR'],'magnificationFilter':Cesium$4[_0xef5fea(0x130)][_0xef5fea(0xd7)]})};this['textures']={'segmentsColor':Util['createTexture'](_0x2b8586),'segmentsDepth':Util[_0xef5fea(0x13c)](_0x32ff7b),'currentTrailsColor':Util['createTexture'](_0x2b8586),'currentTrailsDepth':Util['createTexture'](_0x32ff7b),'nextTrailsColor':Util['createTexture'](_0x2b8586),'nextTrailsDepth':Util[_0xef5fea(0x13c)](_0x32ff7b),'colorTable':Util['createTexture'](_0x1c41ea,_0x3f37e6)};}['createRenderingFramebuffers'](_0x55bf56){const _0x21655a=_0x29607b;this[_0x21655a(0x13f)]={'segments':Util['createFramebuffer'](_0x55bf56,this[_0x21655a(0xe2)]['segmentsColor'],this['textures']['segmentsDepth']),'currentTrails':Util['createFramebuffer'](_0x55bf56,this['textures']['currentTrailsColor'],this['textures']['currentTrailsDepth']),'nextTrails':Util['createFramebuffer'](_0x55bf56,this['textures']['nextTrailsColor'],this['textures']['nextTrailsDepth'])};}['createSegmentsGeometry'](_0x4732ae){const _0x2d3cea=_0x29607b,_0x32a9cd=0x4;let _0x8dfd37=[];for(let _0x3f725f=0x0;_0x3f725f<_0x4732ae['particlesTextureSize'];_0x3f725f++){for(let _0x3b5689=0x0;_0x3b5689<_0x4732ae['particlesTextureSize'];_0x3b5689++){for(let _0x20063a=0x0;_0x20063a<_0x32a9cd;_0x20063a++){_0x8dfd37['push'](_0x3f725f/_0x4732ae[_0x2d3cea(0x132)]),_0x8dfd37['push'](_0x3b5689/_0x4732ae[_0x2d3cea(0x132)]);}}}_0x8dfd37=new Float32Array(_0x8dfd37);let _0x15afe5=[];const _0x236356=[-0x1,0x1],_0x34e463=[-0x1,0x1];for(let _0x5d1704=0x0;_0x5d1704<_0x4732ae['maxParticles'];_0x5d1704++){for(let _0x11c6be=0x0;_0x11c6be<_0x32a9cd/0x2;_0x11c6be++){for(let _0x52b7aa=0x0;_0x52b7aa<_0x32a9cd/0x2;_0x52b7aa++){_0x15afe5['push'](_0x236356[_0x11c6be]),_0x15afe5['push'](_0x34e463[_0x52b7aa]),_0x15afe5['push'](0x0);}}}_0x15afe5=new Float32Array(_0x15afe5);const _0x2f0175=0x6*_0x4732ae['maxParticles'],_0x480040=new Uint32Array(_0x2f0175);for(let _0x4c5657=0x0,_0x1eadea=0x0,_0x5792a6=0x0;_0x4c5657<_0x4732ae['maxParticles'];_0x4c5657++){_0x480040[_0x1eadea++]=_0x5792a6+0x0,_0x480040[_0x1eadea++]=_0x5792a6+0x1,_0x480040[_0x1eadea++]=_0x5792a6+0x2,_0x480040[_0x1eadea++]=_0x5792a6+0x2,_0x480040[_0x1eadea++]=_0x5792a6+0x1,_0x480040[_0x1eadea++]=_0x5792a6+0x3,_0x5792a6+=0x4;}const _0x4f03ec=new Cesium$4['Geometry']({'attributes':new Cesium$4['GeometryAttributes']({'st':new Cesium$4['GeometryAttribute']({'componentDatatype':Cesium$4[_0x2d3cea(0xbc)]['FLOAT'],'componentsPerAttribute':0x2,'values':_0x8dfd37}),'normal':new Cesium$4[(_0x2d3cea(0xdf))]({'componentDatatype':Cesium$4['ComponentDatatype'][_0x2d3cea(0xb5)],'componentsPerAttribute':0x3,'values':_0x15afe5})}),'indices':_0x480040});return _0x4f03ec;}['createRenderingPrimitives'](_0x199961,_0x624f66,_0x5c1ba9,_0x41ac28){const _0x5eeb81=_0x29607b,_0x1f6a82=this;this['primitives']={'segments':new CustomPrimitive({'commandType':'Draw','attributeLocations':{'st':0x0,'normal':0x1},'geometry':this[_0x5eeb81(0x13d)](_0x624f66),'primitiveType':Cesium$4['PrimitiveType']['TRIANGLES'],'uniformMap':{'currentParticlesPosition':function(){const _0x15e1c8=_0x5eeb81;return _0x41ac28['particlesTextures'][_0x15e1c8(0x156)];},'postProcessingPosition':function(){const _0x2a905c=_0x5eeb81;return _0x41ac28[_0x2a905c(0x159)][_0x2a905c(0xb1)];},'postProcessingSpeed':function(){return _0x41ac28['particlesTextures']['postProcessingSpeed'];},'colorTable':function(){const _0x1e3119=_0x5eeb81;return _0x1f6a82[_0x1e3119(0xe2)]['colorTable'];},'aspect':function(){const _0x55477c=_0x5eeb81;return _0x199961['drawingBufferWidth']/_0x199961[_0x55477c(0xdc)];},'pixelSize':function(){return _0x5c1ba9['pixelSize'];},'lineWidth':function(){return _0x624f66['lineWidth'];},'particleHeight':function(){const _0x17bb3d=_0x5eeb81;return _0x624f66[_0x17bb3d(0x102)];}},'vertexShaderSource':new Cesium$4[(_0x5eeb81(0xe1))]({'sources':[segmentDraw_vert]}),'fragmentShaderSource':new Cesium$4[(_0x5eeb81(0xe1))]({'sources':[segmentDraw_frag]}),'rawRenderState':Util['createRawRenderState']({'viewport':undefined,'depthTest':{'enabled':!![]},'depthMask':!![]}),'framebuffer':this[_0x5eeb81(0x13f)][_0x5eeb81(0x14e)],'autoClear':!![]}),'trails':new CustomPrimitive({'commandType':'Draw','attributeLocations':{'position':0x0,'st':0x1},'geometry':Util['getFullscreenQuad'](),'primitiveType':Cesium$4['PrimitiveType']['TRIANGLES'],'uniformMap':{'segmentsColorTexture':function(){return _0x1f6a82['textures']['segmentsColor'];},'segmentsDepthTexture':function(){const _0x3055e2=_0x5eeb81;return _0x1f6a82[_0x3055e2(0xe2)]['segmentsDepth'];},'currentTrailsColor':function(){const _0x21ce7b=_0x5eeb81;return _0x1f6a82['framebuffers'][_0x21ce7b(0x165)][_0x21ce7b(0x129)](0x0);},'trailsDepthTexture':function(){return _0x1f6a82['framebuffers']['currentTrails']['depthTexture'];},'fadeOpacity':function(){const _0x2a5b04=_0x5eeb81;return _0x624f66[_0x2a5b04(0xeb)];}},'vertexShaderSource':new Cesium$4[(_0x5eeb81(0xe1))]({'defines':['DISABLE_GL_POSITION_LOG_DEPTH'],'sources':[fullscreen_vert]}),'fragmentShaderSource':new Cesium$4['ShaderSource']({'defines':['DISABLE_LOG_DEPTH_FRAGMENT_WRITE'],'sources':[trailDraw_frag]}),'rawRenderState':Util[_0x5eeb81(0x10e)]({'viewport':undefined,'depthTest':{'enabled':!![],'func':Cesium$4['DepthFunction']['ALWAYS']},'depthMask':!![]}),'framebuffer':this['framebuffers'][_0x5eeb81(0x134)],'autoClear':!![],'preExecute':function(){const _0x6062bf=_0x5eeb81,_0x53f0b5=_0x1f6a82['framebuffers']['currentTrails'];_0x1f6a82['framebuffers'][_0x6062bf(0x165)]=_0x1f6a82[_0x6062bf(0x13f)][_0x6062bf(0x134)],_0x1f6a82[_0x6062bf(0x13f)]['nextTrails']=_0x53f0b5,_0x1f6a82['primitives'][_0x6062bf(0xe7)][_0x6062bf(0x127)]['framebuffer']=_0x1f6a82['framebuffers']['nextTrails'],_0x1f6a82[_0x6062bf(0x16c)]['trails']['clearCommand']['framebuffer']=_0x1f6a82['framebuffers']['nextTrails'];}}),'screen':new CustomPrimitive({'commandType':_0x5eeb81(0x15e),'attributeLocations':{'position':0x0,'st':0x1},'geometry':Util['getFullscreenQuad'](),'primitiveType':Cesium$4[_0x5eeb81(0x12e)]['TRIANGLES'],'uniformMap':{'trailsColorTexture':function(){const _0xad5901=_0x5eeb81;return _0x1f6a82[_0xad5901(0x13f)]['nextTrails']['getColorTexture'](0x0);},'trailsDepthTexture':function(){return _0x1f6a82['framebuffers']['nextTrails']['depthTexture'];}},'vertexShaderSource':new Cesium$4['ShaderSource']({'defines':['DISABLE_GL_POSITION_LOG_DEPTH'],'sources':[fullscreen_vert]}),'fragmentShaderSource':new Cesium$4['ShaderSource']({'defines':['DISABLE_LOG_DEPTH_FRAGMENT_WRITE'],'sources':[screenDraw_frag]}),'rawRenderState':Util['createRawRenderState']({'viewport':undefined,'depthTest':{'enabled':![]},'depthMask':!![],'blending':{'enabled':!![]}}),'framebuffer':undefined})};}}var getWind_frag=_0x29607b(0x12a),updateSpeed_frag=_0x29607b(0x158),updatePosition_frag=_0x29607b(0xb4),postProcessingPosition_frag='uniform\x20sampler2D\x20nextParticlesPosition;\x0auniform\x20sampler2D\x20nextParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0a\x0a//\x20range\x20(min,\x20max)\x0auniform\x20vec2\x20lonRange;\x0auniform\x20vec2\x20latRange;\x0a\x0auniform\x20float\x20randomCoefficient;\x20//\x20use\x20to\x20improve\x20the\x20pseudo-random\x20generator\x0auniform\x20float\x20dropRate;\x20//\x20drop\x20rate\x20is\x20a\x20chance\x20a\x20particle\x20will\x20restart\x20at\x20random\x20position\x20to\x20avoid\x20degeneration\x0auniform\x20float\x20dropRateBump;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0a//\x20pseudo-random\x20generator\x0aconst\x20vec3\x20randomConstants\x20=\x20vec3(12.9898,\x2078.233,\x204375.85453);\x0aconst\x20vec2\x20normalRange\x20=\x20vec2(0.0,\x201.0);\x0afloat\x20rand(vec2\x20seed,\x20vec2\x20range)\x20{\x0a\x20\x20\x20\x20vec2\x20randomSeed\x20=\x20randomCoefficient\x20*\x20seed;\x0a\x20\x20\x20\x20float\x20temp\x20=\x20dot(randomConstants.xy,\x20randomSeed);\x0a\x20\x20\x20\x20temp\x20=\x20fract(sin(temp)\x20*\x20(randomConstants.z\x20+\x20temp));\x0a\x20\x20\x20\x20return\x20temp\x20*\x20(range.y\x20-\x20range.x)\x20+\x20range.x;\x0a}\x0a\x0avec3\x20generateRandomParticle(vec2\x20seed,\x20float\x20lev)\x20{\x0a\x20\x20\x20\x20//\x20ensure\x20the\x20longitude\x20is\x20in\x20[0,\x20360]\x0a\x20\x20\x20\x20float\x20randomLon\x20=\x20mod(rand(seed,\x20lonRange),\x20360.0);\x0a\x20\x20\x20\x20float\x20randomLat\x20=\x20rand(-seed,\x20latRange);\x0a\x0a\x20\x20\x20\x20return\x20vec3(randomLon,\x20randomLat,\x20lev);\x0a}\x0a\x0abool\x20particleOutbound(vec3\x20particle)\x20{\x0a\x20\x20\x20\x20return\x20particle.y\x20<\x20-90.0\x20||\x20particle.y\x20>\x2090.0;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec3\x20nextParticle\x20=\x20texture(nextParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec4\x20nextSpeed\x20=\x20texture(nextParticlesSpeed,\x20v_textureCoordinates);\x0a\x20\x20\x20\x20float\x20particleDropRate\x20=\x20dropRate\x20+\x20dropRateBump\x20*\x20nextSpeed.a;\x0a\x0a\x20\x20\x20\x20vec2\x20seed1\x20=\x20nextParticle.xy\x20+\x20v_textureCoordinates;\x0a\x20\x20\x20\x20vec2\x20seed2\x20=\x20nextSpeed.xy\x20+\x20v_textureCoordinates;\x0a\x20\x20\x20\x20vec3\x20randomParticle\x20=\x20generateRandomParticle(seed1,\x20nextParticle.z);\x0a\x20\x20\x20\x20float\x20randomNumber\x20=\x20rand(seed2,\x20normalRange);\x0a\x0a\x20\x20\x20\x20if\x20(randomNumber\x20<\x20particleDropRate\x20||\x20particleOutbound(nextParticle))\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(randomParticle,\x201.0);\x20//\x201.0\x20means\x20this\x20is\x20a\x20random\x20particle\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(nextParticle,\x200.0);\x0a\x20\x20\x20\x20}\x0a}\x0a',postProcessingSpeed_frag='uniform\x20sampler2D\x20postProcessingPosition;\x0auniform\x20sampler2D\x20nextParticlesSpeed;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec4\x20randomParticle\x20=\x20texture(postProcessingPosition,\x20v_textureCoordinates);\x0a\x20\x20\x20\x20vec4\x20particleSpeed\x20=\x20texture(nextParticlesSpeed,\x20v_textureCoordinates);\x0a\x0a\x20\x20\x20\x20if\x20(randomParticle.a\x20>\x200.0)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20particleSpeed;\x0a\x20\x20\x20\x20}\x0a}\x0a';const Cesium$3=mars3d__namespace['Cesium'];class ParticlesComputing{constructor(_0x4d428a,_0x3c2856,_0x4828c0,_0x453f56){this['data']=_0x3c2856,this['createWindTextures'](_0x4d428a,_0x3c2856),this['createParticlesTextures'](_0x4d428a,_0x4828c0,_0x453f56),this['createComputingPrimitives'](_0x3c2856,_0x4828c0,_0x453f56);}['createWindTextures'](_0xa08972,_0x2d4a87){const _0x25eea9=_0x29607b,_0x532895={'context':_0xa08972,'width':_0x2d4a87[_0x25eea9(0xe6)]['lon'],'height':_0x2d4a87['dimensions'][_0x25eea9(0xc2)]*(_0x2d4a87['dimensions']['lev']||0x1),'pixelFormat':Cesium$3['PixelFormat'][_0x25eea9(0x10a)],'pixelDatatype':Cesium$3[_0x25eea9(0x166)]['FLOAT'],'flipY':![],'sampler':new Cesium$3[(_0x25eea9(0x7e))]({'minificationFilter':Cesium$3['TextureMinificationFilter'][_0x25eea9(0xf6)],'magnificationFilter':Cesium$3[_0x25eea9(0x130)]['NEAREST']})};this['windTextures']={'U':Util['createTexture'](_0x532895,_0x2d4a87['U']['array']),'V':Util['createTexture'](_0x532895,_0x2d4a87['V']['array'])};}[_0x29607b(0x14c)](_0x2d79a5,_0x55e93c,_0x585007){const _0x2ec67c=_0x29607b,_0x2fff31={'context':_0x2d79a5,'width':_0x55e93c['particlesTextureSize'],'height':_0x55e93c['particlesTextureSize'],'pixelFormat':Cesium$3['PixelFormat'][_0x2ec67c(0x9a)],'pixelDatatype':Cesium$3['PixelDatatype']['FLOAT'],'flipY':![],'sampler':new Cesium$3[(_0x2ec67c(0x7e))]({'minificationFilter':Cesium$3['TextureMinificationFilter']['NEAREST'],'magnificationFilter':Cesium$3['TextureMagnificationFilter']['NEAREST']})},_0x322b57=this['randomizeParticles'](_0x55e93c['maxParticles'],_0x585007),_0x1581b9=new Float32Array(0x4*_0x55e93c['maxParticles'])[_0x2ec67c(0xa8)](0x0);this[_0x2ec67c(0x159)]={'particlesWind':Util['createTexture'](_0x2fff31),'currentParticlesPosition':Util['createTexture'](_0x2fff31,_0x322b57),'nextParticlesPosition':Util['createTexture'](_0x2fff31,_0x322b57),'currentParticlesSpeed':Util[_0x2ec67c(0x13c)](_0x2fff31,_0x1581b9),'nextParticlesSpeed':Util['createTexture'](_0x2fff31,_0x1581b9),'postProcessingPosition':Util[_0x2ec67c(0x13c)](_0x2fff31,_0x322b57),'postProcessingSpeed':Util['createTexture'](_0x2fff31,_0x1581b9)};}['randomizeParticles'](_0x7a2ced,_0x5e10e1){const _0x21480b=_0x29607b,_0x6ee47d=new Float32Array(0x4*_0x7a2ced);for(let _0x5591dc=0x0;_0x5591dc<_0x7a2ced;_0x5591dc++){_0x6ee47d[0x4*_0x5591dc]=Cesium$3[_0x21480b(0x131)][_0x21480b(0x135)](_0x5e10e1['lonRange']['x'],_0x5e10e1[_0x21480b(0x8a)]['y']),_0x6ee47d[0x4*_0x5591dc+0x1]=Cesium$3['Math']['randomBetween'](_0x5e10e1['latRange']['x'],_0x5e10e1['latRange']['y']),_0x6ee47d[0x4*_0x5591dc+0x2]=Cesium$3[_0x21480b(0x131)]['randomBetween'](this['data'][_0x21480b(0x126)][_0x21480b(0x121)],this['data'][_0x21480b(0x126)]['max']),_0x6ee47d[0x4*_0x5591dc+0x3]=0x0;}return _0x6ee47d;}[_0x29607b(0x87)](){const _0x238f9c=_0x29607b;Object['keys'](this[_0x238f9c(0x159)])['forEach'](_0x18e095=>{this['particlesTextures'][_0x18e095]['destroy']();});}['createComputingPrimitives'](_0x2a3f10,_0x2cff2d,_0x40ff5b){const _0x1c4247=_0x29607b,_0x4b16d8=new Cesium$3['Cartesian3'](_0x2a3f10[_0x1c4247(0xe6)]['lon'],_0x2a3f10['dimensions']['lat'],_0x2a3f10['dimensions'][_0x1c4247(0x126)]),_0x5c54ac=new Cesium$3['Cartesian3'](_0x2a3f10[_0x1c4247(0xda)][_0x1c4247(0x121)],_0x2a3f10['lat'][_0x1c4247(0x121)],_0x2a3f10[_0x1c4247(0x126)][_0x1c4247(0x121)]),_0x41b369=new Cesium$3[(_0x1c4247(0x143))](_0x2a3f10[_0x1c4247(0xda)][_0x1c4247(0x8b)],_0x2a3f10[_0x1c4247(0xc2)][_0x1c4247(0x8b)],_0x2a3f10['lev'][_0x1c4247(0x8b)]),_0x58f6c2=new Cesium$3[(_0x1c4247(0x143))]((_0x41b369['x']-_0x5c54ac['x'])/(_0x4b16d8['x']-0x1),(_0x41b369['y']-_0x5c54ac['y'])/(_0x4b16d8['y']-0x1),_0x4b16d8['z']>0x1?(_0x41b369['z']-_0x5c54ac['z'])/(_0x4b16d8['z']-0x1):0x1),_0x53625c=new Cesium$3['Cartesian2'](_0x2a3f10['U']['min'],_0x2a3f10['U']['max']),_0x324e5f=new Cesium$3['Cartesian2'](_0x2a3f10['V'][_0x1c4247(0x121)],_0x2a3f10['V']['max']),_0x5115f2=this;this['primitives']={'getWind':new CustomPrimitive({'commandType':'Compute','uniformMap':{'U':function(){return _0x5115f2['windTextures']['U'];},'V':function(){return _0x5115f2['windTextures']['V'];},'currentParticlesPosition':function(){const _0x298a73=_0x1c4247;return _0x5115f2['particlesTextures'][_0x298a73(0x156)];},'dimension':function(){return _0x4b16d8;},'minimum':function(){return _0x5c54ac;},'maximum':function(){return _0x41b369;},'interval':function(){return _0x58f6c2;}},'fragmentShaderSource':new Cesium$3['ShaderSource']({'sources':[getWind_frag]}),'outputTexture':this['particlesTextures'][_0x1c4247(0x162)],'preExecute':function(){const _0x4c6db2=_0x1c4247;_0x5115f2['primitives'][_0x4c6db2(0x155)]['commandToExecute']['outputTexture']=_0x5115f2['particlesTextures'][_0x4c6db2(0x162)];}}),'updateSpeed':new CustomPrimitive({'commandType':'Compute','uniformMap':{'currentParticlesSpeed':function(){return _0x5115f2['particlesTextures']['currentParticlesSpeed'];},'particlesWind':function(){const _0x381c70=_0x1c4247;return _0x5115f2[_0x381c70(0x159)]['particlesWind'];},'uSpeedRange':function(){return _0x53625c;},'vSpeedRange':function(){return _0x324e5f;},'pixelSize':function(){return _0x40ff5b['pixelSize'];},'speedFactor':function(){const _0x4c19b4=_0x1c4247;return _0x2cff2d[_0x4c19b4(0x93)];}},'fragmentShaderSource':new Cesium$3['ShaderSource']({'sources':[updateSpeed_frag]}),'outputTexture':this['particlesTextures']['nextParticlesSpeed'],'preExecute':function(){const _0x4e44a2=_0x1c4247,_0x341f86=_0x5115f2['particlesTextures']['currentParticlesSpeed'];_0x5115f2['particlesTextures']['currentParticlesSpeed']=_0x5115f2['particlesTextures']['postProcessingSpeed'],_0x5115f2['particlesTextures'][_0x4e44a2(0x12c)]=_0x341f86,_0x5115f2['primitives'][_0x4e44a2(0xcc)]['commandToExecute']['outputTexture']=_0x5115f2['particlesTextures']['nextParticlesSpeed'];}}),'updatePosition':new CustomPrimitive({'commandType':'Compute','uniformMap':{'currentParticlesPosition':function(){const _0x3872f9=_0x1c4247;return _0x5115f2[_0x3872f9(0x159)][_0x3872f9(0x156)];},'currentParticlesSpeed':function(){const _0x1b2f7a=_0x1c4247;return _0x5115f2[_0x1b2f7a(0x159)][_0x1b2f7a(0x12d)];}},'fragmentShaderSource':new Cesium$3[(_0x1c4247(0xe1))]({'sources':[updatePosition_frag]}),'outputTexture':this[_0x1c4247(0x159)][_0x1c4247(0xce)],'preExecute':function(){const _0x251710=_0x5115f2['particlesTextures']['currentParticlesPosition'];_0x5115f2['particlesTextures']['currentParticlesPosition']=_0x5115f2['particlesTextures']['postProcessingPosition'],_0x5115f2['particlesTextures']['postProcessingPosition']=_0x251710,_0x5115f2['primitives']['updatePosition']['commandToExecute']['outputTexture']=_0x5115f2['particlesTextures']['nextParticlesPosition'];}}),'postProcessingPosition':new CustomPrimitive({'commandType':_0x1c4247(0xc3),'uniformMap':{'nextParticlesPosition':function(){const _0xdba72a=_0x1c4247;return _0x5115f2[_0xdba72a(0x159)]['nextParticlesPosition'];},'nextParticlesSpeed':function(){const _0x104243=_0x1c4247;return _0x5115f2[_0x104243(0x159)]['nextParticlesSpeed'];},'lonRange':function(){return _0x40ff5b['lonRange'];},'latRange':function(){return _0x40ff5b['latRange'];},'randomCoefficient':function(){const _0x3adc8e=Math['random']();return _0x3adc8e;},'dropRate':function(){return _0x2cff2d['dropRate'];},'dropRateBump':function(){const _0x3bc265=_0x1c4247;return _0x2cff2d[_0x3bc265(0x112)];}},'fragmentShaderSource':new Cesium$3['ShaderSource']({'sources':[postProcessingPosition_frag]}),'outputTexture':this['particlesTextures']['postProcessingPosition'],'preExecute':function(){const _0x31a45f=_0x1c4247;_0x5115f2['primitives'][_0x31a45f(0xb1)]['commandToExecute']['outputTexture']=_0x5115f2[_0x31a45f(0x159)]['postProcessingPosition'];}}),'postProcessingSpeed':new CustomPrimitive({'commandType':'Compute','uniformMap':{'postProcessingPosition':function(){const _0x2501d7=_0x1c4247;return _0x5115f2['particlesTextures'][_0x2501d7(0xb1)];},'nextParticlesSpeed':function(){const _0x398593=_0x1c4247;return _0x5115f2[_0x398593(0x159)]['nextParticlesSpeed'];}},'fragmentShaderSource':new Cesium$3['ShaderSource']({'sources':[postProcessingSpeed_frag]}),'outputTexture':this['particlesTextures'][_0x1c4247(0x12c)],'preExecute':function(){_0x5115f2['primitives']['postProcessingSpeed']['commandToExecute']['outputTexture']=_0x5115f2['particlesTextures']['postProcessingSpeed'];}})};}}const Cesium$2=mars3d__namespace['Cesium'];class ParticleSystem{constructor(_0x26413a,_0x5a2f7d,_0x5c7677,_0x4c3a5f){const _0x2772fc=_0x29607b;this[_0x2772fc(0xa3)]=_0x26413a,_0x5a2f7d={..._0x5a2f7d},_0x5a2f7d['udata']&&_0x5a2f7d['vdata']&&(_0x5a2f7d[_0x2772fc(0xe6)]={},_0x5a2f7d[_0x2772fc(0xe6)]['lon']=_0x5a2f7d['cols'],_0x5a2f7d['dimensions'][_0x2772fc(0xc2)]=_0x5a2f7d[_0x2772fc(0x92)],_0x5a2f7d[_0x2772fc(0xe6)]['lev']=_0x5a2f7d['lev']||0x1,_0x5a2f7d[_0x2772fc(0xda)]={},_0x5a2f7d['lon'][_0x2772fc(0x121)]=_0x5a2f7d[_0x2772fc(0x164)],_0x5a2f7d['lon']['max']=_0x5a2f7d[_0x2772fc(0x125)],_0x5a2f7d['lat']={},_0x5a2f7d[_0x2772fc(0xc2)]['min']=_0x5a2f7d['ymin'],_0x5a2f7d['lat'][_0x2772fc(0x8b)]=_0x5a2f7d['ymax'],_0x5a2f7d['lev']={},_0x5a2f7d['lev']['min']=_0x5a2f7d[_0x2772fc(0xc0)]??0x1,_0x5a2f7d['lev'][_0x2772fc(0x8b)]=_0x5a2f7d[_0x2772fc(0x150)]??0x1,_0x5a2f7d['U']={},_0x5a2f7d['U']['array']=new Float32Array(_0x5a2f7d[_0x2772fc(0x14f)]),_0x5a2f7d['U']['min']=_0x5a2f7d['umin']??Math['min'](..._0x5a2f7d['udata']),_0x5a2f7d['U'][_0x2772fc(0x8b)]=_0x5a2f7d['umax']??Math['max'](..._0x5a2f7d[_0x2772fc(0x14f)]),_0x5a2f7d['V']={},_0x5a2f7d['V']['array']=new Float32Array(_0x5a2f7d[_0x2772fc(0xf2)]),_0x5a2f7d['V'][_0x2772fc(0x121)]=_0x5a2f7d['vmin']??Math[_0x2772fc(0x121)](..._0x5a2f7d['vdata']),_0x5a2f7d['V'][_0x2772fc(0x8b)]=_0x5a2f7d['vmax']??Math['max'](..._0x5a2f7d['vdata'])),this[_0x2772fc(0x117)]=_0x5a2f7d,this['options']=_0x5c7677,this['viewerParameters']=_0x4c3a5f,this['particlesComputing']=new ParticlesComputing(this['context'],this[_0x2772fc(0x117)],this['options'],this['viewerParameters']),this['particlesRendering']=new ParticlesRendering(this[_0x2772fc(0xa3)],this['data'],this[_0x2772fc(0xc6)],this['viewerParameters'],this['particlesComputing']);}[_0x29607b(0x10c)](_0x339a65){const _0x55da3a=_0x29607b;this[_0x55da3a(0x10f)]['destroyParticlesTextures'](),Object['keys'](this['particlesComputing'][_0x55da3a(0xf9)])['forEach'](_0x323388=>{const _0xfcf1b7=_0x55da3a;this['particlesComputing'][_0xfcf1b7(0xf9)][_0x323388]['destroy']();}),this[_0x55da3a(0x115)]['textures']['colorTable']['destroy'](),Object['keys'](this['particlesRendering'][_0x55da3a(0x13f)])['forEach'](_0x14ecc3=>{const _0x9dd3cc=_0x55da3a;this['particlesRendering'][_0x9dd3cc(0x13f)][_0x14ecc3]['destroy']();}),this['context']=_0x339a65,this['particlesComputing']=new ParticlesComputing(this['context'],this['data'],this['options'],this['viewerParameters']),this['particlesRendering']=new ParticlesRendering(this['context'],this['data'],this[_0x55da3a(0xc6)],this['viewerParameters'],this['particlesComputing']);}['clearFramebuffers'](){const _0x1ecba8=_0x29607b,_0x24adfa=new Cesium$2['ClearCommand']({'color':new Cesium$2[(_0x1ecba8(0xea))](0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':undefined,'pass':Cesium$2[_0x1ecba8(0x99)]['OPAQUE']});Object[_0x1ecba8(0x149)](this['particlesRendering']['framebuffers'])['forEach'](_0x3956a7=>{const _0x1db6fd=_0x1ecba8;_0x24adfa['framebuffer']=this[_0x1db6fd(0x115)]['framebuffers'][_0x3956a7],_0x24adfa['execute'](this[_0x1db6fd(0xa3)]);});}[_0x29607b(0x154)](_0x550b02){const _0x4b0edb=_0x29607b;this[_0x4b0edb(0xcb)](),this['particlesComputing']['destroyParticlesTextures'](),this[_0x4b0edb(0x10f)]['createParticlesTextures'](this['context'],this['options'],this[_0x4b0edb(0x13b)]);if(_0x550b02){var _0x565b41;const _0x30004d=this['particlesRendering']['createSegmentsGeometry'](this['options']);this['particlesRendering']['primitives']['segments']['geometry']=_0x30004d;const _0x6613a4=Cesium$2['VertexArray']['fromGeometry']({'context':this['context'],'geometry':_0x30004d,'attributeLocations':this['particlesRendering'][_0x4b0edb(0x16c)]['segments']['attributeLocations'],'bufferUsage':Cesium$2['BufferUsage']['STATIC_DRAW']});(_0x565b41=this['particlesRendering'][_0x4b0edb(0x16c)])!==null&&_0x565b41!==void 0x0&&(_0x565b41=_0x565b41['segments'])!==null&&_0x565b41!==void 0x0&&_0x565b41['commandToExecute']&&(this['particlesRendering'][_0x4b0edb(0x16c)]['segments']['commandToExecute'][_0x4b0edb(0x97)]=_0x6613a4);}}['setOptions'](_0xd272da){let _0x5679c4=![];this['options']['maxParticles']!==_0xd272da['maxParticles']&&(_0x5679c4=!![]),Object['keys'](_0xd272da)['forEach'](_0x1a35f5=>{this['options'][_0x1a35f5]=_0xd272da[_0x1a35f5];}),this['refreshParticles'](_0x5679c4);}['applyViewerParameters'](_0x1c47db){Object['keys'](_0x1c47db)['forEach'](_0x9127b4=>{const _0x177c28=_0x5893;this[_0x177c28(0x13b)][_0x9127b4]=_0x1c47db[_0x9127b4];}),this['refreshParticles'](![]);}[_0x29607b(0x118)](){const _0xf0b368=_0x29607b;clearTimeout(this['canrefresh']),this['particlesComputing'][_0xf0b368(0x87)](),Object['keys'](this['particlesComputing']['windTextures'])[_0xf0b368(0x77)](_0x213de4=>{this['particlesComputing']['windTextures'][_0x213de4]['destroy']();}),this['particlesRendering']['textures']['colorTable']['destroy'](),Object['keys'](this['particlesRendering'][_0xf0b368(0x13f)])['forEach'](_0x4cb6a1=>{this['particlesRendering']['framebuffers'][_0x4cb6a1]['destroy']();});for(const _0x4b1041 in this){delete this[_0x4b1041];}}}const Cesium$1=mars3d__namespace[_0x29607b(0xf5)],BaseLayer$1=mars3d__namespace['layer']['BaseLayer'],DEF_OPTIONS={'particlesNumber':0x1000,'fixedHeight':0x0,'fadeOpacity':0.996,'dropRate':0.003,'dropRateBump':0.01,'speedFactor':0.5,'lineWidth':0x2,'colors':[_0x29607b(0x152)]};class WindLayer extends BaseLayer$1{constructor(_0x55aa05={}){_0x55aa05={...DEF_OPTIONS,..._0x55aa05},super(_0x55aa05),this['_setOptionsHook'](_0x55aa05);}get['layer'](){const _0x1893e0=_0x29607b;return this[_0x1893e0(0x16c)];}get['data'](){return this['_data'];}set['data'](_0x1853ab){const _0x4d0cc0=_0x29607b;this[_0x4d0cc0(0xd6)](_0x1853ab);}get['colors'](){const _0x5564f7=_0x29607b;return this[_0x5564f7(0xc6)]['colors'];}set['colors'](_0x2685cb){const _0x449289=_0x29607b;this['options'][_0x449289(0x101)]=_0x2685cb,this[_0x449289(0x153)]&&this[_0x449289(0x153)]['setOptions']({'colors':_0x2685cb}),this[_0x449289(0x10d)]();}['_mountedHook'](){}['_addedHook'](){const _0x3f4a55=_0x29607b;this['scene']=this[_0x3f4a55(0xf3)][_0x3f4a55(0x91)],this[_0x3f4a55(0xaf)]=this['_map']['camera'],this['primitives']=new Cesium$1[(_0x3f4a55(0xcf))](),this['_map']['scene']['primitives']['add'](this[_0x3f4a55(0x16c)]),this[_0x3f4a55(0x13b)]={'lonRange':new Cesium$1['Cartesian2'](),'latRange':new Cesium$1[(_0x3f4a55(0x145))](),'pixelSize':0x0},this[_0x3f4a55(0xa9)]=new Cesium$1['BoundingSphere'](Cesium$1[_0x3f4a55(0x143)]['ZERO'],0.99*0x615299),this['updateViewerParameters'](),window['addEventListener'](_0x3f4a55(0x10d),this['resize']['bind'](this),![]),this['mouse_down']=![],this['mouse_move']=![],this['_map']['on'](mars3d__namespace['EventType']['wheel'],this['_onMapWhellEvent'],this),this['_map']['on'](mars3d__namespace['EventType'][_0x3f4a55(0x106)],this['_onMouseDownEvent'],this),this['_map']['on'](mars3d__namespace['EventType']['mouseUp'],this[_0x3f4a55(0x86)],this),this['_map']['on'](mars3d__namespace['EventType']['mouseMove'],this['_onMouseMoveEvent'],this),this['_data']&&this['setData'](this[_0x3f4a55(0x128)]);}['_removedHook'](){const _0x1d27a2=_0x29607b;window['removeEventListener']('resize',this['resize']),this['_map']['off'](mars3d__namespace['EventType']['preRender'],this['_onMap_preRenderEvent'],this),this['_map'][_0x1d27a2(0x169)](mars3d__namespace['EventType']['wheel'],this[_0x1d27a2(0xb8)],this),this['_map'][_0x1d27a2(0x169)](mars3d__namespace['EventType'][_0x1d27a2(0x106)],this[_0x1d27a2(0xab)],this),this['_map']['off'](mars3d__namespace['EventType']['mouseUp'],this['_onMouseUpEvent'],this),this[_0x1d27a2(0xf3)][_0x1d27a2(0x169)](mars3d__namespace[_0x1d27a2(0xbb)][_0x1d27a2(0x11d)],this[_0x1d27a2(0x151)],this),this['primitives'][_0x1d27a2(0x123)](),this['_map']['scene']['primitives'][_0x1d27a2(0x7d)](this['primitives']);}['resize'](){const _0x43b0c4=_0x29607b;if(!this[_0x43b0c4(0x116)]||!this['particleSystem'])return;this['primitives']['show']=![],this['primitives'][_0x43b0c4(0x123)](),this[_0x43b0c4(0xf3)]['once'](mars3d__namespace['EventType']['preRender'],this['_onMap_preRenderEvent'],this);}['_onMap_preRenderEvent'](_0x5b553c){const _0x3b39b4=_0x29607b;this['particleSystem'][_0x3b39b4(0x10c)](this['scene']['context']),this['addPrimitives'](),this[_0x3b39b4(0x16c)]['show']=!![];}[_0x29607b(0xb8)](_0x254f80){const _0xd12faa=_0x29607b;clearTimeout(this[_0xd12faa(0xba)]);if(!this['show']||!this['particleSystem'])return;this['primitives']['show']=![],this['refreshTimer']=setTimeout(()=>{if(!this['show'])return;this['redraw']();},0xc8);}['_onMouseDownEvent'](_0x3c5387){this['mouse_down']=!![];}['_onMouseMoveEvent'](_0x21778f){const _0x555aa0=_0x29607b;if(!this['show']||!this['particleSystem'])return;this['mouse_down']&&(this['primitives']['show']=![],this[_0x555aa0(0x8f)]=!![]);}['_onMouseUpEvent'](_0xfb38fe){const _0x41f018=_0x29607b;if(!this['show']||!this['particleSystem'])return;this['mouse_down']&&this['mouse_move']&&this['redraw'](),this[_0x41f018(0x16c)]['show']=!![],this['mouse_down']=![],this['mouse_move']=![];}['redraw'](){const _0x27d881=_0x29607b;if(!this['_map']||!this[_0x27d881(0x116)])return;this['updateViewerParameters'](),this[_0x27d881(0x153)][_0x27d881(0x136)](this[_0x27d881(0x13b)]),this[_0x27d881(0x16c)]['show']=!![];}[_0x29607b(0xd6)](_0x25c1d2){const _0x307767=_0x29607b;this['_data']=_0x25c1d2,this['particleSystem']&&this[_0x307767(0x153)]['destroy'](),this['particleSystem']=new ParticleSystem(this['scene']['context'],_0x25c1d2,this['getOptions'](),this['viewerParameters']),this['addPrimitives']();}['_setOptionsHook'](_0x14feb4,_0x13bbed){const _0xbdc636=_0x29607b;if(_0x14feb4)for(const _0x4acfed in _0x14feb4){this[_0x4acfed]=_0x14feb4[_0x4acfed];}this['particleSystem']&&this[_0xbdc636(0x153)]['setOptions'](this[_0xbdc636(0x109)]());}['getOptions'](){const _0x1cb77c=_0x29607b,_0x507a2e=Math['ceil'](Math['sqrt'](this['particlesNumber']));return this['particlesNumber']=_0x507a2e*_0x507a2e,{'particlesTextureSize':_0x507a2e,'maxParticles':this['particlesNumber'],'particleHeight':this['fixedHeight'],'fadeOpacity':this['fadeOpacity'],'dropRate':this['dropRate'],'dropRateBump':this['dropRateBump'],'speedFactor':this['speedFactor'],'lineWidth':this[_0x1cb77c(0x148)],'colors':this['colors']};}['addPrimitives'](){const _0x279b36=_0x29607b;this['primitives']['add'](this['particleSystem']['particlesComputing'][_0x279b36(0x16c)][_0x279b36(0x155)]),this['primitives']['add'](this['particleSystem'][_0x279b36(0x10f)][_0x279b36(0x16c)][_0x279b36(0xcc)]),this['primitives'][_0x279b36(0x104)](this[_0x279b36(0x153)][_0x279b36(0x10f)][_0x279b36(0x16c)]['updatePosition']),this['primitives']['add'](this[_0x279b36(0x153)]['particlesComputing']['primitives']['postProcessingPosition']),this[_0x279b36(0x16c)]['add'](this['particleSystem']['particlesComputing']['primitives']['postProcessingSpeed']),this['primitives'][_0x279b36(0x104)](this['particleSystem']['particlesRendering']['primitives'][_0x279b36(0x14e)]),this['primitives'][_0x279b36(0x104)](this['particleSystem']['particlesRendering'][_0x279b36(0x16c)]['trails']),this[_0x279b36(0x16c)]['add'](this[_0x279b36(0x153)]['particlesRendering'][_0x279b36(0x16c)][_0x279b36(0x11f)]);}['updateViewerParameters'](){const _0x2791a6=_0x29607b;let _0x545273=this['camera'][_0x2791a6(0x160)](this[_0x2791a6(0x91)]['globe']['ellipsoid']);if(!_0x545273){const _0x27a0c8=this['_map']['getExtent']();_0x545273=Cesium$1[_0x2791a6(0xa7)]['fromDegrees'](_0x27a0c8['xmin'],_0x27a0c8['ymin'],_0x27a0c8['xmax'],_0x27a0c8['ymax']);}const _0x3f843d=Util[_0x2791a6(0x114)](_0x545273);this[_0x2791a6(0x13b)]['lonRange']['x']=_0x3f843d['lon'][_0x2791a6(0x121)],this[_0x2791a6(0x13b)]['lonRange']['y']=_0x3f843d[_0x2791a6(0xda)][_0x2791a6(0x8b)],this['viewerParameters']['latRange']['x']=_0x3f843d[_0x2791a6(0xc2)]['min'],this['viewerParameters']['latRange']['y']=_0x3f843d['lat']['max'];const _0x5c207e=this[_0x2791a6(0xaf)]['getPixelSize'](this[_0x2791a6(0xa9)],this['scene']['drawingBufferWidth'],this['scene']['drawingBufferHeight']);_0x5c207e>0x0&&(this['viewerParameters'][_0x2791a6(0x11c)]=_0x5c207e);}}mars3d__namespace['LayerUtil']['register'](_0x29607b(0xbe),WindLayer),mars3d__namespace['layer'][_0x29607b(0xae)]=WindLayer;class CanvasParticle{constructor(){const _0xbb28ab=_0x29607b;this[_0xbb28ab(0x120)]=null,this['lat']=null,this['tlng']=null,this[_0xbb28ab(0x161)]=null,this['age']=null,this['speed']=null;}['destroy'](){for(const _0x28124a in this){delete this[_0x28124a];}}}class CanvasWindField{constructor(_0x12d244){const _0x2df636=_0x29607b;this[_0x2df636(0xf7)](_0x12d244);}get[_0x29607b(0x7b)](){return this['_speedRate'];}set[_0x29607b(0x7b)](_0x495ab4){const _0xf66b58=_0x29607b;this['_speedRate']=(0x64-(_0x495ab4>0x63?0x63:_0x495ab4))*0x64,this[_0xf66b58(0x16a)]=[(this[_0xf66b58(0x125)]-this['xmin'])/this['_speedRate'],(this[_0xf66b58(0xe5)]-this[_0xf66b58(0x9b)])/this['_speedRate']];}get[_0x29607b(0xa5)](){const _0x4037c4=_0x29607b;return this[_0x4037c4(0xd8)];}set['maxAge'](_0x189cdc){this['_maxAge']=_0x189cdc;}['setOptions'](_0x5ed0ea){const _0x5b3784=_0x29607b;this['options']=_0x5ed0ea,this['maxAge']=_0x5ed0ea['maxAge']||0x78,this['speedRate']=_0x5ed0ea['speedRate']||0x32,this['particles']=[];const _0x223f27=_0x5ed0ea['particlesNumber']||0x1000;for(let _0x204228=0x0;_0x204228<_0x223f27;_0x204228++){const _0x27592=this[_0x5b3784(0xfc)](new CanvasParticle());this['particles'][_0x5b3784(0x168)](_0x27592);}}[_0x29607b(0x89)](_0x55e4ab){const _0x307773=_0x29607b;this[_0x307773(0x92)]=_0x55e4ab['rows'],this['cols']=_0x55e4ab[_0x307773(0xb9)],this['xmin']=_0x55e4ab['xmin'],this['xmax']=_0x55e4ab['xmax'],this[_0x307773(0x9b)]=_0x55e4ab['ymin'],this['ymax']=_0x55e4ab[_0x307773(0xe5)],this[_0x307773(0x147)]=[];const _0x51b91f=_0x55e4ab['udata'],_0x292de0=_0x55e4ab['vdata'];let _0xbcf69b=![];_0x51b91f['length']===this['rows']&&_0x51b91f[0x0][_0x307773(0x14b)]===this['cols']&&(_0xbcf69b=!![]);let _0x390419=0x0,_0x14e3cd=null,_0x586dbe=null;for(let _0x4f488c=0x0;_0x4f488c<this['rows'];_0x4f488c++){_0x14e3cd=[];for(let _0x3308cb=0x0;_0x3308cb<this['cols'];_0x3308cb++,_0x390419++){_0xbcf69b?_0x586dbe=this['_calcUV'](_0x51b91f[_0x4f488c][_0x3308cb],_0x292de0[_0x4f488c][_0x3308cb]):_0x586dbe=this[_0x307773(0xf0)](_0x51b91f[_0x390419],_0x292de0[_0x390419]),_0x14e3cd['push'](_0x586dbe);}this['grid'][_0x307773(0x168)](_0x14e3cd);}this[_0x307773(0xc6)]['reverseY']&&this['grid'][_0x307773(0x8e)]();}['clear'](){const _0x11fd43=_0x29607b;delete this['rows'],delete this[_0x11fd43(0xb9)],delete this['xmin'],delete this['xmax'],delete this['ymin'],delete this['ymax'],delete this['grid'],delete this['particles'];}[_0x29607b(0x94)](_0x1c3027,_0x1e9734){const _0x4d8b3d=_0x29607b,_0x5f59a2=(_0x1c3027-this['xmin'])/(this[_0x4d8b3d(0x125)]-this[_0x4d8b3d(0x164)])*(this['cols']-0x1),_0x35d5e8=(this['ymax']-_0x1e9734)/(this['ymax']-this['ymin'])*(this['rows']-0x1);return[_0x5f59a2,_0x35d5e8];}['getUVByXY'](_0x58887a,_0x35fd2e){const _0x1b72e7=_0x29607b;if(_0x58887a<0x0||_0x58887a>=this['cols']||_0x35fd2e>=this['rows'])return[0x0,0x0,0x0];const _0x3b4f22=Math['floor'](_0x58887a),_0x4c8bbd=Math['floor'](_0x35fd2e);if(_0x3b4f22===_0x58887a&&_0x4c8bbd===_0x35fd2e)return this['grid'][_0x35fd2e][_0x58887a];const _0x1420b5=_0x3b4f22+0x1,_0x3c3e66=_0x4c8bbd+0x1,_0x2e0e37=this[_0x1b72e7(0x12f)](_0x3b4f22,_0x4c8bbd),_0x5a99a3=this['getUVByXY'](_0x1420b5,_0x4c8bbd),_0x444b5f=this[_0x1b72e7(0x12f)](_0x3b4f22,_0x3c3e66),_0x2ca126=this[_0x1b72e7(0x12f)](_0x1420b5,_0x3c3e66);let _0x2f3b45=null;try{_0x2f3b45=this['_bilinearInterpolation'](_0x58887a-_0x3b4f22,_0x35fd2e-_0x4c8bbd,_0x2e0e37,_0x5a99a3,_0x444b5f,_0x2ca126);}catch(_0x519d67){console[_0x1b72e7(0x11b)](_0x58887a,_0x35fd2e);}return _0x2f3b45;}['_bilinearInterpolation'](_0x192c90,_0x17c0d1,_0x42206c,_0x388bf9,_0x5b9b4f,_0x4460c6){const _0x180111=0x1-_0x192c90,_0x59be2a=0x1-_0x17c0d1,_0x262fa8=_0x180111*_0x59be2a,_0x140db7=_0x192c90*_0x59be2a,_0x46148d=_0x180111*_0x17c0d1,_0x3c1ba6=_0x192c90*_0x17c0d1,_0x3a8052=_0x42206c[0x0]*_0x262fa8+_0x388bf9[0x0]*_0x140db7+_0x5b9b4f[0x0]*_0x46148d+_0x4460c6[0x0]*_0x3c1ba6,_0x362fba=_0x42206c[0x1]*_0x262fa8+_0x388bf9[0x1]*_0x140db7+_0x5b9b4f[0x1]*_0x46148d+_0x4460c6[0x1]*_0x3c1ba6;return this['_calcUV'](_0x3a8052,_0x362fba);}[_0x29607b(0xf0)](_0x20e87a,_0x37a252){return[+_0x20e87a,+_0x37a252,Math['sqrt'](_0x20e87a*_0x20e87a+_0x37a252*_0x37a252)];}['getUVByPoint'](_0x4bea7e,_0x115d3c){const _0x44d7cd=_0x29607b;if(!this['isInExtent'](_0x4bea7e,_0x115d3c))return null;const _0x141656=this[_0x44d7cd(0x94)](_0x4bea7e,_0x115d3c),_0x1bf475=this['getUVByXY'](_0x141656[0x0],_0x141656[0x1]);return _0x1bf475;}['isInExtent'](_0x4481b3,_0x1ab66e){return _0x4481b3>=this['xmin']&&_0x4481b3<=this['xmax']&&_0x1ab66e>=this['ymin']&&_0x1ab66e<=this['ymax']?!![]:![];}[_0x29607b(0x13a)](){const _0x464073=_0x29607b,_0x3cde86=fRandomByfloat(this['xmin'],this[_0x464073(0x125)]),_0x26f018=fRandomByfloat(this['ymin'],this['ymax']);return{'lat':_0x26f018,'lng':_0x3cde86};}['getParticles'](){const _0x362b34=_0x29607b;let _0x3e33c9,_0x40855d,_0x1ff326;for(let _0x4fac57=0x0,_0x2bedaf=this[_0x362b34(0x138)]['length'];_0x4fac57<_0x2bedaf;_0x4fac57++){let _0x365448=this['particles'][_0x4fac57];_0x365448['age']<=0x0&&(_0x365448=this['_randomParticle'](_0x365448));if(_0x365448['age']>0x0){const _0x491b7f=_0x365448['tlng'],_0x381c8f=_0x365448[_0x362b34(0x161)];_0x1ff326=this['getUVByPoint'](_0x491b7f,_0x381c8f),_0x1ff326?(_0x3e33c9=_0x491b7f+this[_0x362b34(0x16a)][0x0]*_0x1ff326[0x0],_0x40855d=_0x381c8f+this['_calc_speedRate'][0x1]*_0x1ff326[0x1],_0x365448['lng']=_0x491b7f,_0x365448['lat']=_0x381c8f,_0x365448['tlng']=_0x3e33c9,_0x365448[_0x362b34(0x161)]=_0x40855d,_0x365448['speed']=_0x1ff326[0x2],_0x365448['age']--):_0x365448[_0x362b34(0x110)]=0x0;}}return this['particles'];}['_randomParticle'](_0x46daec){const _0x3526c6=_0x29607b;let _0x1cfcee,_0x1f2435;for(let _0x4c85a3=0x0;_0x4c85a3<0x1e;_0x4c85a3++){_0x1cfcee=this['getRandomLatLng'](),_0x1f2435=this['getUVByPoint'](_0x1cfcee[_0x3526c6(0x120)],_0x1cfcee['lat']);if(_0x1f2435&&_0x1f2435[0x2]>0x0)break;}if(!_0x1f2435)return _0x46daec;const _0x3eecd6=_0x1cfcee['lng']+this[_0x3526c6(0x16a)][0x0]*_0x1f2435[0x0],_0x278a42=_0x1cfcee['lat']+this[_0x3526c6(0x16a)][0x1]*_0x1f2435[0x1];return _0x46daec['lng']=_0x1cfcee[_0x3526c6(0x120)],_0x46daec['lat']=_0x1cfcee['lat'],_0x46daec['tlng']=_0x3eecd6,_0x46daec['tlat']=_0x278a42,_0x46daec['age']=Math[_0x3526c6(0x141)](Math['random']()*this['maxAge']),_0x46daec['speed']=_0x1f2435[0x2],_0x46daec;}['destroy'](){for(const _0x4a0385 in this){delete this[_0x4a0385];}}}function fRandomByfloat(_0x4f9163,_0x35ca4a){const _0x6c56ec=_0x29607b;return _0x4f9163+Math[_0x6c56ec(0x9d)]()*(_0x35ca4a-_0x4f9163);}const Cesium=mars3d__namespace[_0x29607b(0xf5)],BaseLayer=mars3d__namespace['layer']['BaseLayer'];class CanvasWindLayer extends BaseLayer{constructor(_0xd1d762={}){super(_0xd1d762),this['_setOptionsHook'](_0xd1d762),this['canvas']=null;}['_setOptionsHook'](_0x33aca6,_0x2965d9){const _0x152c9d=_0x29607b;this['frameTime']=0x3e8/(_0x33aca6['frameRate']||0xa),this['_pointerEvents']=this[_0x152c9d(0xc6)]['pointerEvents']??![],this['color']=_0x33aca6[_0x152c9d(0x108)]||'#ffffff',this[_0x152c9d(0x148)]=_0x33aca6['lineWidth']||0x1,this['fixedHeight']=_0x33aca6[_0x152c9d(0x12b)]??0x0,this['reverseY']=_0x33aca6['reverseY']??![],this['windField']&&this['windField']['setOptions'](_0x33aca6);}get['layer'](){const _0x5df9c6=_0x29607b;return this[_0x5df9c6(0xd5)];}get['canvasWidth'](){const _0x55de1a=_0x29607b;return this[_0x55de1a(0xf3)][_0x55de1a(0x91)][_0x55de1a(0xd5)]['clientWidth'];}get['canvasHeight'](){const _0x5ed759=_0x29607b;return this[_0x5ed759(0xf3)]['scene'][_0x5ed759(0xd5)][_0x5ed759(0x13e)];}get['pointerEvents'](){return this['_pointerEvents'];}set['pointerEvents'](_0x8562a8){const _0x33df4e=_0x29607b;this[_0x33df4e(0x139)]=_0x8562a8;if(!this['canvas'])return;_0x8562a8?this[_0x33df4e(0xd5)]['style'][_0x33df4e(0xdd)]='all':this['canvas'][_0x33df4e(0xfd)]['pointer-events']='none';}get['particlesNumber'](){const _0x3e8e45=_0x29607b;return this[_0x3e8e45(0xc6)][_0x3e8e45(0xa6)];}set['particlesNumber'](_0x1d8b51){this['options']['particlesNumber']=_0x1d8b51,clearTimeout(this['_canrefresh']),this['_canrefresh']=setTimeout(()=>{this['redraw']();},0x1f4);}get['speedRate'](){return this['options']['speedRate'];}set[_0x29607b(0x7b)](_0x11e32a){const _0x3b65a0=_0x29607b;this['options']['speedRate']=_0x11e32a,this['windField']&&(this['windField'][_0x3b65a0(0x7b)]=_0x11e32a);}get[_0x29607b(0xa5)](){return this['options']['maxAge'];}set['maxAge'](_0x60e66b){const _0x1eed4a=_0x29607b;this[_0x1eed4a(0xc6)][_0x1eed4a(0xa5)]=_0x60e66b,this['windField']&&(this['windField'][_0x1eed4a(0xa5)]=_0x60e66b);}get['data'](){return this['windData'];}set[_0x29607b(0x117)](_0x21e3b4){const _0x35adf9=_0x29607b;this[_0x35adf9(0xd6)](_0x21e3b4);}['_showHook'](_0x259126){const _0x26c6f5=_0x29607b;_0x259126?this['_addedHook']():(this[_0x26c6f5(0xd4)]&&(this['options'][_0x26c6f5(0x117)]=this[_0x26c6f5(0xd4)]),this['_removedHook']());}['_mountedHook'](){const _0x29243d=_0x29607b;this[_0x29243d(0xc6)]['worker']?this['initWorker']():this['windField']=new CanvasWindField(this['options']);}['_addedHook'](){const _0x222f9c=_0x29607b;this['canvas']=this['_createCanvas'](),this['canvasContext']=this['canvas'][_0x222f9c(0x103)]('2d',{'willReadFrequently':!![]}),this['bindEvent'](),this['options']['data']&&this['setData'](this['options'][_0x222f9c(0x117)]);}['_removedHook'](){const _0x3889cc=_0x29607b;this[_0x3889cc(0x8c)](),this['unbindEvent'](),this[_0x3889cc(0xd5)]&&(this[_0x3889cc(0xf3)]['container']['removeChild'](this['canvas']),delete this['canvas']);}['_createCanvas'](){const _0x5225ad=_0x29607b,_0x5ef0c7=mars3d__namespace[_0x5225ad(0x111)]['create']('canvas',_0x5225ad(0xff),this['_map']['container']);return _0x5ef0c7['style'][_0x5225ad(0x119)]='absolute',_0x5ef0c7['style']['top']=_0x5225ad(0x157),_0x5ef0c7['style'][_0x5225ad(0xbf)]=_0x5225ad(0x157),_0x5ef0c7[_0x5225ad(0xfd)]['width']=this['_map']['scene']['canvas']['clientWidth']+'px',_0x5ef0c7['style']['height']=this['_map']['scene']['canvas']['clientHeight']+'px',_0x5ef0c7['style']['pointerEvents']=this['_pointerEvents']?_0x5225ad(0xb2):_0x5225ad(0x88),_0x5ef0c7['style']['zIndex']=this[_0x5225ad(0xc6)]['zIndex']??0x9,_0x5ef0c7[_0x5225ad(0x15a)]=this['_map']['scene']['canvas']['clientWidth'],_0x5ef0c7['height']=this['_map']['scene']['canvas']['clientHeight'],_0x5ef0c7;}[_0x29607b(0x10d)](){const _0x39a259=_0x29607b;this['canvas']&&(this['canvas']['style']['width']=this[_0x39a259(0xf3)]['scene'][_0x39a259(0xd5)]['clientWidth']+'px',this[_0x39a259(0xd5)]['style']['height']=this['_map'][_0x39a259(0x91)]['canvas']['clientHeight']+'px',this['canvas'][_0x39a259(0x15a)]=this['_map'][_0x39a259(0x91)]['canvas'][_0x39a259(0xe9)],this[_0x39a259(0xd5)]['height']=this['_map']['scene']['canvas']['clientHeight']);}['bindEvent'](){const _0x3bff3b=_0x29607b,_0x44a9d6=this;let _0x390ad5=Date['now']();(function _0xe4ccce(){const _0x20c360=_0x5893;_0x44a9d6['animateFrame']=window['requestAnimationFrame'](_0xe4ccce);if(_0x44a9d6[_0x20c360(0x116)]&&_0x44a9d6['windField']){const _0x347495=Date['now'](),_0x181d20=_0x347495-_0x390ad5;_0x181d20>_0x44a9d6[_0x20c360(0x124)]&&(_0x390ad5=_0x347495-_0x181d20%_0x44a9d6['frameTime'],_0x44a9d6['update']());}}(),window[_0x3bff3b(0xa1)](_0x3bff3b(0x10d),this['resize'][_0x3bff3b(0xc7)](this),![]),this['mouse_down']=![],this['mouse_move']=![],this[_0x3bff3b(0xc6)]['mouseHidden']&&(this['_map']['on'](mars3d__namespace['EventType']['wheel'],this[_0x3bff3b(0xb8)],this),this[_0x3bff3b(0xf3)]['on'](mars3d__namespace['EventType']['mouseDown'],this[_0x3bff3b(0xab)],this),this[_0x3bff3b(0xf3)]['on'](mars3d__namespace[_0x3bff3b(0xbb)]['mouseUp'],this[_0x3bff3b(0x86)],this)));}[_0x29607b(0xa0)](){const _0x17ca73=_0x29607b;window['cancelAnimationFrame'](this['animateFrame']),delete this['animateFrame'],window['removeEventListener']('resize',this['resize']),this['options']['mouseHidden']&&(this[_0x17ca73(0xf3)][_0x17ca73(0x169)](mars3d__namespace['EventType'][_0x17ca73(0xb0)],this[_0x17ca73(0xb8)],this),this['_map']['off'](mars3d__namespace[_0x17ca73(0xbb)][_0x17ca73(0x106)],this[_0x17ca73(0xab)],this),this[_0x17ca73(0xf3)]['off'](mars3d__namespace[_0x17ca73(0xbb)][_0x17ca73(0x9e)],this['_onMouseUpEvent'],this),this['_map']['off'](mars3d__namespace['EventType']['mouseMove'],this['_onMouseMoveEvent'],this));}['_onMapWhellEvent'](_0x355697){const _0x2ed712=_0x29607b;clearTimeout(this[_0x2ed712(0xba)]);if(!this['show']||!this[_0x2ed712(0xd5)])return;this['canvas'][_0x2ed712(0xfd)][_0x2ed712(0x95)]='hidden',this['refreshTimer']=setTimeout(()=>{const _0x21508c=_0x2ed712;if(!this[_0x21508c(0x116)])return;this[_0x21508c(0xe3)](),this['canvas']['style'][_0x21508c(0x95)]='visible';},0xc8);}['_onMouseDownEvent'](_0x455879){const _0x157196=_0x29607b;this['mouse_down']=!![],this['_map']['off'](mars3d__namespace[_0x157196(0xbb)]['mouseMove'],this[_0x157196(0x151)],this),this['_map']['on'](mars3d__namespace['EventType'][_0x157196(0x11d)],this[_0x157196(0x151)],this);}['_onMouseMoveEvent'](_0x57a2a0){const _0x376f76=_0x29607b;if(!this['show']||!this['canvas'])return;this['mouse_down']&&(this['canvas']['style'][_0x376f76(0x95)]=_0x376f76(0xfe),this['mouse_move']=!![]);}[_0x29607b(0x86)](_0x29c61d){const _0x4c41d1=_0x29607b;if(!this['show']||!this['canvas'])return;this['_map'][_0x4c41d1(0x169)](mars3d__namespace['EventType']['mouseMove'],this[_0x4c41d1(0x151)],this),this['mouse_down']&&this['mouse_move']&&this['redraw'](),this['canvas']['style']['visibility']='visible',this[_0x4c41d1(0xa4)]=![],this['mouse_move']=![];}[_0x29607b(0xd6)](_0x3ba5a1){const _0x46e9d3=_0x29607b;this[_0x46e9d3(0x8c)](),this[_0x46e9d3(0xd4)]=_0x3ba5a1,this['windField']['setDate'](_0x3ba5a1),this['redraw']();}['redraw'](){if(!this['show'])return;this['windField']['setOptions'](this['options']),this['update']();}['update'](){const _0x23a092=_0x29607b;if(this['_updateIng'])return;this[_0x23a092(0xd3)]=!![];if(this['worker'])this['windField']['update']();else{const _0x491a98=this[_0x23a092(0xbd)][_0x23a092(0x78)]();this['_drawLines'](_0x491a98);}this['_updateIng']=![];}['_drawLines'](_0x3f0aa2){const _0x3bb487=_0x29607b;this['canvasContext'][_0x3bb487(0xaa)]='destination-in',this['canvasContext'][_0x3bb487(0xd2)](0x0,0x0,this['canvasWidth'],this['canvasHeight']),this['canvasContext'][_0x3bb487(0xaa)]=_0x3bb487(0xe4),this[_0x3bb487(0x133)][_0x3bb487(0xfa)]=0.9,this['canvasContext']['beginPath'](),this[_0x3bb487(0x133)][_0x3bb487(0x148)]=this['lineWidth'],this[_0x3bb487(0x133)]['strokeStyle']=this[_0x3bb487(0x108)];const _0x2d2cb2=this['_map']['scene']['mode']!==Cesium['SceneMode']['SCENE3D'],_0x140ac6=this[_0x3bb487(0x15f)]*0.25;for(let _0x4eac3d=0x0,_0x3bb955=_0x3f0aa2['length'];_0x4eac3d<_0x3bb955;_0x4eac3d++){const _0x45cac1=_0x3f0aa2[_0x4eac3d],_0x47d622=this['_tomap'](_0x45cac1['lng'],_0x45cac1['lat'],_0x45cac1),_0x5e0ffc=this['_tomap'](_0x45cac1[_0x3bb487(0xa2)],_0x45cac1['tlat'],_0x45cac1);if(!_0x47d622||!_0x5e0ffc)continue;if(_0x2d2cb2&&Math['abs'](_0x47d622[0x0]-_0x5e0ffc[0x0])>=_0x140ac6)continue;this['canvasContext']['moveTo'](_0x47d622[0x0],_0x47d622[0x1]),this['canvasContext'][_0x3bb487(0x98)](_0x5e0ffc[0x0],_0x5e0ffc[0x1]);}this[_0x3bb487(0x133)]['stroke']();}['_tomap'](_0x20cda4,_0x4a0301,_0x4abfd3){const _0x22282f=_0x29607b,_0x406d61=Cesium['Cartesian3'][_0x22282f(0x122)](_0x20cda4,_0x4a0301,this['fixedHeight']),_0x267278=this['_map']['scene'];if(_0x267278[_0x22282f(0x107)]===Cesium['SceneMode']['SCENE3D']){const _0x3a8c52=new Cesium['EllipsoidalOccluder'](_0x267278[_0x22282f(0xc8)]['ellipsoid'],_0x267278['camera']['positionWC']),_0x2d4007=_0x3a8c52['isPointVisible'](_0x406d61);if(!_0x2d4007)return _0x4abfd3['age']=0x0,null;}const _0x3ce200=mars3d__namespace['PointTrans']['toWindowCoordinates'](this[_0x22282f(0xf3)]['scene'],_0x406d61);return _0x3ce200?[_0x3ce200['x'],_0x3ce200['y']]:null;}['clear'](){const _0x5417fe=_0x29607b;this[_0x5417fe(0xbd)]['clear'](),delete this['windData'];}['initWorker'](){const _0x35d59c=_0x29607b;this['worker']=new Worker(this['options']['worker']),this['worker'][_0x35d59c(0xc1)]=_0x4f236c=>{const _0x27269b=_0x35d59c;this[_0x27269b(0x113)](_0x4f236c['data']['particles']),this[_0x27269b(0x85)]=![];},this['windField']={'init':_0x215dcd=>{const _0x523d66=_0x35d59c;this['worker']['postMessage']({'type':_0x523d66(0xb7),'options':_0x215dcd});},'setOptions':_0x4edd92=>{const _0x86995e=_0x35d59c;this[_0x86995e(0xd1)][_0x86995e(0x100)]({'type':_0x86995e(0xf7),'options':_0x4edd92});},'setDate':_0x522142=>{const _0x12fd95=_0x35d59c;this['worker'][_0x12fd95(0x100)]({'type':'setDate','data':_0x522142});},'update':()=>{const _0x446755=_0x35d59c;if(this['_updateIng2'])return;this['_updateIng2']=!![],this[_0x446755(0xd1)]['postMessage']({'type':_0x446755(0xfb)});},'clear':()=>{this['worker']['postMessage']({'type':'clear'});}},this['windField']['init'](this['options']);}}mars3d__namespace[_0x29607b(0xd0)]['register'](_0x29607b(0x105),CanvasWindLayer),mars3d__namespace['layer']['CanvasWindLayer']=CanvasWindLayer,mars3d__namespace['CanvasWindField']=CanvasWindField,mars3d__namespace['WindUtil']=WindUtil,exports['CanvasWindField']=CanvasWindField,exports['CanvasWindLayer']=CanvasWindLayer,exports[_0x29607b(0xae)]=WindLayer,exports['WindUtil']=WindUtil,Object['defineProperty'](exports,'__esModule',{'value':!![]});
}));
