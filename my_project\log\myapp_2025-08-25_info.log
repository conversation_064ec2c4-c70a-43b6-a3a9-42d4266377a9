2025-08-25 09:53:06.657 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 09:55:02.704 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 09:55:04.110 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 09:55:09.085 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 09:55:10.792 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 09:55:12.821 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 09:55:14.072 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 09:55:15.913 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 09:55:17.180 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 09:55:23.264 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 09:55:24.692 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 09:56:23.589 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\views\ywuView.py changed, reloading.
2025-08-25 09:56:25.061 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 09:56:39.619 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\views\bgglView.py changed, reloading.
2025-08-25 09:56:41.239 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 09:56:55.380 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\views\dataBaseBak.py changed, reloading.
2025-08-25 09:56:56.806 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 09:58:39.049 | INFO     | channels.management.commands.runserver:log_action:168 - WebSocket HANDSHAKING /ws/msg [127.0.0.1:62500]
2025-08-25 09:58:39.054 | INFO     | channels.management.commands.runserver:log_action:164 - WebSocket CONNECT /ws/msg [127.0.0.1:62500]
2025-08-25 09:58:44.866 | INFO     | channels.management.commands.runserver:log_action:166 - WebSocket DISCONNECT /ws/msg [127.0.0.1:62500]
2025-08-25 09:59:40.246 | INFO     | my_app.middleware:__call__:217 - 没有加解密
2025-08-25 09:59:40.257 | INFO     | my_app.middleware:process_response:45 - Request from: 127.0.0.1 - Apifox/1.0.0 (https://apifox.com)
2025-08-25 09:59:47.326 | INFO     | my_app.middleware:__call__:217 - 没有加解密
2025-08-25 09:59:47.333 | INFO     | my_app.middleware:process_response:45 - Request from: 127.0.0.1 - Apifox/1.0.0 (https://apifox.com)
2025-08-25 09:59:54.516 | INFO     | my_app.middleware:__call__:217 - 没有加解密
2025-08-25 09:59:54.522 | INFO     | my_app.middleware:process_response:45 - Request from: 127.0.0.1 - Apifox/1.0.0 (https://apifox.com)
2025-08-25 10:00:44.746 | INFO     | my_app.middleware:__call__:217 - 没有加解密
2025-08-25 10:00:44.900 | INFO     | vgis_log.logTools:set_start_log_info:77 - 开始时间：2025-08-25 10:00:44
2025-08-25 10:00:45.572 | INFO     | my_app.middleware:process_response:45 - Request from: 127.0.0.1 - Apifox/1.0.0 (https://apifox.com)
2025-08-25 10:00:45.579 | INFO     | channels.management.commands.runserver:log_action:147 - [mHTTP POST /wrj_api/user/loginWithForce/ 200 [1.01, 127.0.0.1:62564][0m
2025-08-25 10:01:27.465 | INFO     | my_app.middleware:__call__:217 - 没有加解密
2025-08-25 10:01:27.478 | INFO     | my_app.middleware:process_response:45 - Request from: 127.0.0.1 - Apifox/1.0.0 (https://apifox.com)
2025-08-25 10:01:27.480 | INFO     | channels.management.commands.runserver:log_action:147 - [mHTTP GET /wrj_api/user/?ordering&page=1&size=100 200 [0.10, 127.0.0.1:62564][0m
2025-08-25 10:03:35.517 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 10:03:37.901 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:03:54.112 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 10:03:55.684 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:04:22.353 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 10:04:23.900 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:04:29.604 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 10:04:31.095 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:04:36.584 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 10:04:38.412 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:04:43.739 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 10:04:45.703 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:04:52.153 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 10:04:53.758 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:04:59.490 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 10:05:01.122 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:05:15.318 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 10:05:16.900 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:05:26.337 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\models\__init__.py changed, reloading.
2025-08-25 10:05:27.853 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:11:13.552 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_project\routings.py changed, reloading.
2025-08-25 10:11:15.133 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:11:33.810 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_app\websocket\drone_comsumers.py changed, reloading.
2025-08-25 10:11:35.420 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:12:18.504 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_project\routings.py changed, reloading.
2025-08-25 10:12:19.958 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:12:26.949 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_project\routings.py changed, reloading.
2025-08-25 10:12:28.443 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:12:35.462 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_project\routings.py changed, reloading.
2025-08-25 10:12:37.039 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:13:17.752 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_project\routings.py changed, reloading.
2025-08-25 10:13:19.283 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:15:04.290 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:15:17.520 | INFO     | channels.management.commands.runserver:log_action:168 - WebSocket HANDSHAKING /ws/msg [127.0.0.1:63978]
2025-08-25 10:15:17.524 | INFO     | channels.management.commands.runserver:log_action:164 - WebSocket CONNECT /ws/msg [127.0.0.1:63978]
2025-08-25 10:15:26.604 | INFO     | channels.management.commands.runserver:log_action:166 - WebSocket DISCONNECT /ws/msg [127.0.0.1:63978]
2025-08-25 10:34:06.390 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:34:15.800 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_project\settings.py changed, reloading.
2025-08-25 10:34:17.316 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:57:05.247 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 10:57:50.654 | INFO     | my_app.middleware:__call__:217 - 没有加解密
2025-08-25 10:57:50.666 | INFO     | my_app.middleware:process_response:45 - Request from: 127.0.0.1 - Apifox/1.0.0 (https://apifox.com)
2025-08-25 10:57:50.671 | INFO     | channels.management.commands.runserver:log_action:147 - [mHTTP GET /wrj_api/user/?ordering&page=1&size=100 200 [0.10, 127.0.0.1:50258][0m
2025-08-25 10:58:38.824 | INFO     | channels.management.commands.runserver:log_action:168 - WebSocket HANDSHAKING /ws/msg [127.0.0.1:50279]
2025-08-25 10:58:38.830 | INFO     | channels.management.commands.runserver:log_action:164 - WebSocket CONNECT /ws/msg [127.0.0.1:50279]
2025-08-25 10:58:49.160 | INFO     | channels.management.commands.runserver:log_action:166 - WebSocket DISCONNECT /ws/msg [127.0.0.1:50279]
2025-08-25 11:34:14.284 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 11:37:20.113 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_project\settings.py changed, reloading.
2025-08-25 11:37:21.495 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
2025-08-25 11:37:26.786 | INFO     | django.utils.autoreload:trigger_reload:266 - E:\NewDev\zhouzhou\drone-service\my_project\my_project\settings.py changed, reloading.
2025-08-25 11:37:28.562 | INFO     | django.utils.autoreload:run_with_reloader:668 - Watching for file changes with StatReloader
