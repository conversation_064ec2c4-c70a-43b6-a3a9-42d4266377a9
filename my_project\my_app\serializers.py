# -*- coding: utf-8 -*-
"""
===================================
#!/usr/bin/python3.9
@Author: chenxw
@Email : <EMAIL>
@File: serializers.py.py
@Date: Create in 2021/2/5 19:49
@Description: 序列化器
@ Software: PyCharm
===================================
"""

from rest_framework import serializers

from my_app.models import AuthUser, TtUploadFileData, SysConfig, SysDepartment, SysLog, SysMenu, SysOss, SysRole, \
    SysRoleMenu, SysUser, \
    SysUserRole, SysUserToken, TmDdistrict, SysParam, TtAnnotationSysmbolData, TtAnnotationType, \
    TtFeatureAnnotationData, TtFriendFoeInfo, TtIntelligenceSource, TtPoiData, TtViewBookmarkData, TtPointType, \
    TmBingtuan, TtFieldType, TtFeatureAnnotationeNewFields, TtServiceData, TtServiceInterfaceType, TtKeyPointAreaData


# 用户登录请求序列化器
class LoginRequestSerializer(serializers.Serializer):
    """
    用户登录请求的序列化器，只包含登录所需的字段
    """
    username = serializers.CharField(
        max_length=150,
        help_text="用户名",
        required=True
    )
    password = serializers.CharField(
        max_length=128,
        help_text="密码",
        required=True,
        style={'input_type': 'password'}  # 在Swagger UI中显示为密码输入框
    )
    verifcation = serializers.CharField(
        max_length=10,
        help_text="验证码",
        required=False,
        allow_blank=True
    )
    client_time = serializers.IntegerField(
        help_text="客户端时间戳",
        required=True
    )

    class Meta:
        # 不绑定到具体模型，这是一个纯粹的请求序列化器
        fields = ['username', 'password', 'verifcation', 'client_time']
        # API文档示例值（仅用于文档展示）
        swagger_schema_fields = {
            "example": {
                "username": "zhous",
                "password": "admin",
                "verifcation": "1234",
                "client_time": 1684489627117
            }
        }


# 强制登录请求序列化器
class ForceLoginRequestSerializer(LoginRequestSerializer):
    """
    强制登录请求的序列化器，继承自登录请求序列化器
    """
    # insuranceName = serializers.CharField(
    #     max_length=100,
    #     help_text="保险名称",
    #     required=False,
    #     allow_blank=True
    # )

    class Meta:
        fields = ['username', 'password', 'verifcation', 'client_time']
        # API文档示例值（仅用于文档展示）
        swagger_schema_fields = {
            "example": {
                "username": "zhous",
                "password": "admin",
                "verifcation": "1234",
                "client_time": 1684489627117
            }
        }


# 单点登录请求序列化器
class SSOLoginRequestSerializer(serializers.Serializer):
    """
    单点登录请求的序列化器
    """
    ssoToken = serializers.CharField(
        max_length=500,
        help_text="单点登录令牌",
        required=True
    )
    
    class Meta:
        fields = ['ssoToken']
        # API文档示例值（仅用于文档展示）
        swagger_schema_fields = {
            "example": {
                "ssoToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VybmFtZSI6ImFkbWluIn0.example_token"
            }
        }


# 退出登录请求序列化器
class LogoutRequestSerializer(serializers.Serializer):
    """
    退出登录请求的序列化器
    """
    username = serializers.CharField(
        max_length=150,
        help_text="用户名",
        required=True
    )
    userid = serializers.IntegerField(
        help_text="用户ID",
        required=True
    )
    
    class Meta:
        fields = ['username', 'userid']
        # API文档示例值（仅用于文档展示）
        swagger_schema_fields = {
            "example": {
                "username": "admin",
                "userid": 123
            }
        }


# 登录响应序列化器
class LoginResponseSerializer(serializers.Serializer):
    """
    登录响应的序列化器，用于文档展示
    """
    code = serializers.IntegerField(help_text="响应代码")
    success = serializers.BooleanField(help_text="是否成功",required=False)
    message = serializers.CharField(help_text="响应消息", required=False)
    token = serializers.CharField(help_text="令牌", required=False)
    data = serializers.DictField(help_text="响应数据", required=False)
    
    class Meta:
        fields = ['success', 'code', 'message', 'token', 'data']
        # API文档示例值（仅用于文档展示）
        swagger_schema_fields = {
            "example": {
                "code": 0,
                "success": True,
                "info": "用户登录成功！",
                "userid": 1817609406574606,
                "username": "zhous",
                "token": "5c9e724860e96e2eb55112213cb9d8ffa8bc0243"
            }
        }


# 用户信息表序列化器
class AuthUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = AuthUser
        fields = "__all__"


# 上传文件表序列器
class TtUploadFileDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtUploadFileData
        fields = "__all__"


# 行政区划表序列器
class TmDdistrictSerializer(serializers.ModelSerializer):
    class Meta:
        model = TmDdistrict
        fields = "__all__"


# 兵团区划表序列器
class TmBingtuanSerializer(serializers.ModelSerializer):
    class Meta:
        model = TmBingtuan
        fields = "__all__"


# 系统配置表序列器
class SysConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysConfig
        fields = "__all__"


# 系统部门表序列器
class SysDepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysDepartment
        fields = "__all__"


# 系统日志表序列器
class SysLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysLog
        fields = "__all__"


# 系统菜单表序列器
class SysMenuSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysMenu
        fields = "__all__"


# 系统定制表序列器
class SysOssSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysOss
        fields = "__all__"


# 系统角色表序列器
class SysRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysRole
        fields = "__all__"


# 系统角色菜单表序列器
class SysRoleMenuSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysRoleMenu
        fields = "__all__"


# 系统用户表（已废弃）序列器
class SysUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysUser
        fields = "__all__"


# 系统用户角色表序列器
class SysUserRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysUserRole
        fields = "__all__"


# 系统用户token表序列器
class SysUserTokenSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysUserToken
        fields = "__all__"


class AuthUserpulldownSerializer(serializers.ModelSerializer):
    class Meta:
        model = AuthUser
        fields = ["id", "fullname"]


# 系统参数
class SysParamSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysParam
        fields = "__all__"


# 标注符号数据表序列器
class TtAnnotationSysmbolDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtAnnotationSysmbolData
        fields = "__all__"


# 标注类型字典表序列器
class TtAnnotationTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtAnnotationType
        fields = "__all__"


# 要素标注数据表序列器
class TtFeatureAnnotationDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtFeatureAnnotationData
        fields = "__all__"

# 标注数据新增字段表序列器
class TtFeatureAnnotationNewFieldsSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtFeatureAnnotationeNewFields
        fields = "__all__"

# 敌我信息字典表序列器
class TtFriendFoeInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtFriendFoeInfo
        fields = "__all__"


# 情报来源字典表序列器
class TtIntelligenceSourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtIntelligenceSource
        fields = "__all__"

# 标注点类型数据表序列器
class TtPointTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtPointType
        fields = "__all__"


# 字段类型数据表序列器
class TtFieldTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtFieldType
        fields = "__all__"


# POI数据表序列器
class TtPoiDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtPoiData
        fields = "__all__"

# 服务数据表序列器
class TtServiceDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtServiceData
        fields = "__all__"

# 服务接口类型序列器
class TtServiceInterfaceTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtServiceInterfaceType
        fields = "__all__"


# 视口书签数据表序列器
class TtViewBookmarkDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtViewBookmarkData
        fields = "__all__"


# 重点点位区域数据表序列器
class TtKeyPointAreaDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtKeyPointAreaData
        fields = "__all__"