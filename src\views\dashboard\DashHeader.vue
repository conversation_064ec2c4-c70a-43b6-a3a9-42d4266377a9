<script setup lang="ts">
import { computed } from 'vue'
import { useChineseDateTime } from '@/composables/useDateTime'

// 使用中文时间组合函数
const { chineseTime } = useChineseDateTime()

// 从环境变量获取平台信息
const platformName = import.meta.env.VITE_PLATFORM_NAME || '低空遥感云平台'
const companyName = import.meta.env.VITE_COMPANY_NAME || '维璟（北京）科技有限公司'

// 完整的时间显示文本
const timeDisplayText = computed(() => {
  return `${chineseTime.value} ${companyName}`
})
</script>

<template>
  <header>
    <div class="header-container">
      <img src="@/assets/images/mask_header.png" alt="Dashboard Header" />
      <!-- 时间显示 -->
      <div class="time-display">
        <span>{{ timeDisplayText }}</span>
      </div>

      <div class="title-container">
        <h1>{{ platformName }}</h1>
      </div>
    </div>
  </header>
</template>

<style lang="scss" scoped>
header {
  width: 100%;
  height: 100%;

  .header-container {
    position: relative;
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      object-fit: cover;
    }

    .time-display {
      position: absolute;
      top: 0.5rem;
      right: $dashboard-panel-margin;

      span {
        color: $text-active;
        font-size: 0.7rem;
        font-weight: bold;
      }
    }

    .title-container {
      position: absolute;
      top: 45%;
      left: 50%;
      transform: translate(-50%, -30%);
      display: flex;
      justify-content: center;
      align-items: center;

      h1 {
        color: $text-default;
        font-size: 1.8rem;
        font-weight: bold;
        letter-spacing: 0.5em;
        margin: 0;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }
    }
  }
}
</style>
