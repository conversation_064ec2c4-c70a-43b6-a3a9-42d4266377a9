import os
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated

from my_app.serializers import SysLogSerializer
from my_project.token import ExpiringTokenAuthentication
from rest_framework.response import Response
# 日志相关模块导入
from vgis_log.logTools import LoggerHelper
import logging
from my_app.models import SysLog
# 获取 Django 日志记录器
logger = logging.getLogger('django')

# 获取系统编码，使用 platform 模块获取当前操作系统类型并转换为小写
import platform
platformType = platform.system().lower()

# 时间模块导入，用于处理日期和时间
import datetime

# 给前端返回的语法相关模块导入，用于构建统一的响应格式
from my_app.views.response.baseRespone import Result

# 人工点位标注默认相关模型导入
from my_app.models import WjyyGzBggl

# 数据 id 生成类导入，用于生成唯一的 ID
from my_app.utils.snowflake_id_util import SnowflakeIDUtil

# JSON 转换模块导入，用于处理 JSON 数据
import json

# DRF 自定义请求装饰器导入，用于定义自定义的视图动作
from rest_framework.decorators import action

class DataBaseBakViewSet(viewsets.ModelViewSet):
    # 设置权限类，要求用户必须经过身份验证才能访问该视图集的接口
    permission_classes = (IsAuthenticated,)
    # 设置认证类，使用自定义的过期令牌认证方式
    authentication_classes = (ExpiringTokenAuthentication,)
    @action(methods=['GET'], detail=False)
    def getDataBaseBak(self, request):
        # 定义 API 路径，用于日志记录
        api_path = "/dataBaseBak/getDataBaseBak/"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        # 定义功能标题，用于日志记录和返回信息
        function_title = "获取数据库备份文件，请求时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        try:
            function_title += "返回时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            # 获取当前工作目录
            current_working_directory = os.getcwd()
            # 获取当前工作目录的上级目录
            parent_directory = os.path.abspath(os.path.join(current_working_directory, os.pardir))
            # 列出上级目录下的所有文件和文件夹
            files_and_dirs = os.listdir(parent_directory)
            # 筛选出文件列表中的文件
            files = [os.path.join(parent_directory, f) for f in files_and_dirs if
                     os.path.isfile(os.path.join(parent_directory, f))]

            # 打印文件列表
            print(files)
            # 记录正常结束日志信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)
            # 返回成功结果
            return Result.sucess("{}成功".format(function_title))

        except Exception as exp:
            # 构建错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            function_title += "返回时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request,
                                                       function_title, error_info, exp)
            # 返回失败结果
            return Result.fail(error_info, json_data)

    @action(methods=['POST'], detail=False)
    def delFile(self, request):
        # 定义 API 路径，用于日志记录
        api_path = "/dataBaseBak/delFile/"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        # 定义功能标题，用于日志记录和返回信息
        function_title = "删除备份文件，请求时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        try:
            file_name = "example.txt"
            file_to_delete = f'file\\{file_name}'  # 要删除的文件名称
            # 获取当前工作目录
            current_working_directory = os.getcwd()
            # 构造文件的完整路径
            file_path = os.path.join(current_working_directory, file_to_delete)
            # 检查文件是否存在
            if os.path.exists(file_path) and os.path.isfile(file_path):
                # 删除文件
                os.remove(file_path)
                print(f"文件 {file_name} 已删除。")
            else:
                print(f"文件 {file_name} 不存在或不是一个文件。")
            # 记录正常结束日志信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)
            # 返回成功结果
            return Result.sucess("{}成功".format(function_title))

        except Exception as exp:
            # 构建错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            function_title += "返回时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request,
                                                       function_title, error_info, exp)
            # 返回失败结果
            return Result.fail(error_info, json_data)