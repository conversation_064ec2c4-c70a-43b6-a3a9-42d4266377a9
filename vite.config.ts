import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { mars3dPlugin } from 'vite-plugin-mars3d'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 根据当前 mode 加载 .env 文件
  // process.cwd() 是项目根目录
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [
      vue(),
      vueDevTools(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
        imports: ['vue', 'vue-router', 'pinia'],
        dts: 'src/auto-imports.d.ts',
      }),
      Components({
        resolvers: [ElementPlusResolver()],
        dts: 'src/components.d.ts',
        directoryAsNamespace: false, // 关闭目录命名空间，使用文件名作为组件名
        // 只扫描 components 目录，保持组件职责清晰
        dirs: ['src/components'],
        deep: true,
      }),
      mars3dPlugin(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/assets/main.scss" as *;`,
        },
      },
    },
    server: {
      host: '0.0.0.0',
      allowedHosts: true,
      proxy: {
        // 使用环境变量来动态设置代理规则
        [env.VITE_API_BASE_URL]: {
          target: env.VITE_PROXY_TARGET, // 从环境变量读取 target
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^${env.VITE_API_BASE_URL}`), ''),
          proxyTimeout: 30000,
        },
      },
    },
  }
})

