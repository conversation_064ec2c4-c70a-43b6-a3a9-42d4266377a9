import asyncio
import json
import datetime
from channels.generic.websocket import AsyncJsonWebsocketConsumer
from channels.middleware import BaseMiddleware
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
# from rest_framework.authtoken.models import Token
from my_app.models import SysUserToken, AuthUser
from django.db import connection
from my_app.enum.base_env_enum import BaseEnvEnum
from my_app.enum.SOCKET_STATUS_ENUM import SOCKET_STATUS_ENUM
from my_app.enum.SOCKET_CONNECT_TYPE_ENUM import SOCKET_CONNECT_TYPE_ENUM

# 统一使用项目的日志系统
from loguru import logger

# 导入无人机业务逻辑服务
from .drone_service import drone_service

class TokenAuthMiddleware(BaseMiddleware):
    async def __call__(self, scope, receive, send):
        try:
            # 从查询字符串中解析token
            query_string = scope.get("query_string", b"").decode('utf-8')
            token = None

            # 解析查询参数
            if query_string:
                from urllib.parse import parse_qs
                params = parse_qs(query_string)
                token = params.get('authorization', [None])[0]

            # 如果没有token，设置为匿名用户
            if not token:
                from django.contrib.auth.models import AnonymousUser
                scope["user"] = AnonymousUser()
            else:
                # 将 token 添加到 scope 中
                scope["user"] = await self.get_user_from_token(token)

        except Exception as e:
            logger.error(f"WebSocket认证错误: {e}")
            from django.contrib.auth.models import AnonymousUser
            scope["user"] = AnonymousUser()

        return await super().__call__(scope, receive, send)

    @database_sync_to_async
    def get_user_from_token(self, token):
        # 根据 token 获取用户
        try:
            from django.contrib.auth.models import AnonymousUser
            # 导入已在文件顶部完成

            # 查找token记录
            user_token = SysUserToken.objects.get(token=token)

            # 根据user_id获取用户（注意：这里user_id是主键，不是外键）
            # 需要通过user_id查找对应的AuthUser
            try:
                user = AuthUser.objects.get(id=user_token.user_id)
                logger.info(f"Token认证成功: 用户 {user.username} (ID: {user.id})")
                return user
            except AuthUser.DoesNotExist:
                logger.warning(f"Token {token} 对应的用户ID {user_token.user_id} 不存在")
                return AnonymousUser()

        except SysUserToken.DoesNotExist:
            logger.warning(f"Token {token} 不存在")
            return AnonymousUser()
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            from django.contrib.auth.models import AnonymousUser
            return AnonymousUser()


class AlertConsumer(AsyncJsonWebsocketConsumer):
    """
    WebSocket消费者类，用于处理无人机实时数据通信

    主要功能：
    1. 处理WebSocket连接和断开
    2. 接收客户端消息并路由到相应的处理方法
    3. 推送无人机实时数据到前端（符合FlightRealTimeData接口）
    4. 提供心跳检测和错误处理

    支持的消息类型：
    - start_drone_data: 开始推送指定无人机的实时数据
    - stop_drone_data: 停止推送无人机数据
    - get_drone_status: 获取特定无人机的状态信息
    - ping: 心跳检测

    TODO: 未来需要对接第三方无人机平台接口
    - 大疆DJI Mobile SDK / Windows SDK集成
    - 大疆司空2平台API对接
    - 其他无人机厂商（如道通、极飞等）API集成
    - 实时视频流推送
    - 飞行控制指令下发
    - 设备状态监控和告警
    - 航线规划和任务管理
    """
    async def connect(self):
        """WebSocket连接建立时调用"""
        # 直接接受连接，不做认证检查（恢复原来的行为）
        await self.accept()

        # 发送连接成功消息
        await self.send_json({
            'type': 'connection_success',
            'message': 'WebSocket连接成功',
            'timestamp': datetime.datetime.now().isoformat(),
            'instructions': {
                'start_drone_data': '发送 {"action": "start_drone_data", "drone_id": "drone_001"} 开始接收指定无人机数据',
                'stop_drone_data': '发送 {"action": "stop_drone_data"} 停止接收无人机数据',
                'get_drone_status': '发送 {"action": "get_drone_status", "drone_id": "drone_001"} 获取特定无人机状态',
                'available_drones': ['drone_001', 'drone_002', 'drone_003']
            }
        })

        logger.info('WebSocket连接成功: 无认证模式')

    async def receive_json(self, content):
        """处理接收到的JSON消息"""
        try:
            logger.info(f'=== 开始处理WebSocket消息 ===')
            logger.info(f'收到的原始内容: {content}')
            logger.info(f'内容类型: {type(content)}')

            action = content.get('action', '')
            logger.info(f'解析出的action: {action}')

            if action == 'start_drone_data':
                logger.info('处理start_drone_data请求')
                # 获取指定的无人机ID，默认为drone_001
                drone_id = content.get('drone_id', 'drone_001')
                # 开始推送指定无人机数据
                await self.start_drone_data_push(drone_id)

            elif action == 'stop_drone_data':
                logger.info('处理stop_drone_data请求')
                # 停止推送无人机数据
                await self.stop_drone_data_push()

            elif action == 'get_drone_status':
                logger.info('处理get_drone_status请求')
                # 获取特定无人机状态
                drone_id = content.get('drone_id', 'drone_001')
                await self.send_drone_status(drone_id)

            elif action == 'ping':
                logger.info('处理ping请求')
                # 心跳检测
                await self.send_json({
                    'type': 'pong',
                    'timestamp': datetime.datetime.now().isoformat()
                })

            else:
                logger.warning(f'未知操作: {action}')
                # 未知操作
                await self.send_json({
                    'type': 'error',
                    'message': f'未知操作: {action}',
                    'available_actions': ['start_drone_data', 'stop_drone_data', 'get_drone_status', 'ping'],
                    'available_drones': ['drone_001', 'drone_002', 'drone_003']
                })

            logger.info('=== WebSocket消息处理完成 ===')

        except Exception as e:
            error_msg = f'处理WebSocket消息错误: {e}'
            logger.error(error_msg)
            await self.send_json({
                'type': 'error',
                'message': f'服务器错误: {str(e)}'
            })

    async def start_drone_data_push(self, drone_id: str = 'drone_001'):
        """
        开始推送指定无人机数据

        Args:
            drone_id: 无人机ID，默认为drone_001
        """
        self.current_drone_id = drone_id  # 保存当前监控的无人机ID

        await self.send_json({
            'type': 'drone_data_start',
            'message': f'开始推送无人机 {drone_id} 数据',
            'drone_id': drone_id,
            'timestamp': datetime.datetime.now().isoformat()
        })

        # 创建一个后台任务持续推送数据
        import asyncio
        self.drone_data_task = asyncio.create_task(self.drone_data_loop(drone_id))

    async def stop_drone_data_push(self):
        """停止推送无人机数据"""
        if hasattr(self, 'drone_data_task'):
            self.drone_data_task.cancel()

        await self.send_json({
            'type': 'drone_data_stop',
            'message': '停止推送无人机数据',
            'timestamp': datetime.datetime.now().isoformat()
        })

    async def drone_data_loop(self, drone_id: str):
        """
        无人机数据推送循环

        Args:
            drone_id: 要推送数据的无人机ID
        """
        try:
            import asyncio
            push_count = 0

            logger.info(f'开始推送无人机 {drone_id} 的实时数据')

            while True:
                # 使用业务逻辑服务生成符合前端接口的数据
                flight_data = drone_service.generate_flight_real_time_data(drone_id)

                # 发送符合FlightRealTimeData接口的数据
                await self.send_json({
                    'type': 'flight_real_time_data',
                    'data': flight_data,
                    'push_count': push_count,
                    'timestamp': datetime.datetime.now().isoformat()
                })

                push_count += 1
                logger.debug(f'推送无人机 {drone_id} 数据，第 {push_count} 次')

                # 每2秒推送一次数据
                await asyncio.sleep(2)

        except asyncio.CancelledError:
            logger.info(f'无人机 {drone_id} 数据推送任务已取消')
        except Exception as e:
            logger.error(f'无人机 {drone_id} 数据推送错误: {e}')

    async def send_drone_status(self, drone_id: str):
        """
        发送特定无人机状态

        Args:
            drone_id: 无人机ID
        """
        try:
            # 使用业务逻辑服务获取无人机状态
            drone_status = drone_service.get_drone_status(drone_id)

            await self.send_json({
                'type': 'drone_status',
                'data': drone_status,
                'timestamp': datetime.datetime.now().isoformat()
            })

            logger.info(f'发送无人机 {drone_id} 状态数据')

        except Exception as e:
            logger.error(f'获取无人机 {drone_id} 状态失败: {e}')
            await self.send_json({
                'type': 'error',
                'message': f'获取无人机 {drone_id} 状态失败: {str(e)}',
                'timestamp': datetime.datetime.now().isoformat()
            })

    async def send_msg(self, group_id, data_info, socket_connect_type):
        if data_info:
            logger.info('group_id = {} 发送消息,发送时间: {},msg_type = {}'.format(group_id, datetime.datetime.now().strftime(
                "%Y-%m-%d %H:%M:%S"), "登陆系统"))
            await self.send(text_data=json.dumps({
                'data': data_info,
                'socket_connect_type': socket_connect_type
            }))
            # 用户只要一登陆就初始化发送

    async def connect_success(self, username, user_id, channel_name):

        success_msg = {
            "message":
                {
                    "username": username,
                    "successes": True,
                    "time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "msg": "socket 连接成功",
                    "status": SOCKET_STATUS_ENUM.SUCCESS.value,
                    "channel_name": channel_name
                },
            "socket_connect_type":
                SOCKET_CONNECT_TYPE_ENUM.LOGIN.value
        }
        return success_msg

    '''
        前端发来消息 ->
        1.发来心跳信息 5分钟发一次心跳信息 
        heartbeat_check
        2.心跳检查 
          1.5分钟一次心跳检测
            {
                "message" {
                    "socket_connect_type":"heartbeat_check"
                }
            }
          2.相应
          {
            "socket_connect_type":"heartbeat_check",
            "successes":True,
            "time":datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "msg":"socket heartbeat_check ok",
            "status": SOCKET_STATUS_ENUM.SUCCESS.value,
            "channel_name": channel_name
         }


    '''

    # 旧的websocket_receive方法已删除，使用receive_json替代

    # 相应用户的信息
    async def receive_reback_msg(self, socket_connect_type):
        user = self.user
        user_id = user.id
        username = user.username
        channel_name = self.channel_name

        return {
            "user_id": user_id,
            "username": username,
            "socket_connect_type": socket_connect_type,
            "successes": True,
            "time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "msg": "socket {} ok".format(socket_connect_type),
            "status": SOCKET_STATUS_ENUM.SUCCESS.value,
            "channel_name": channel_name
        }

    async def disconnect(self, close_code):
        """WebSocket连接断开时调用"""
        try:
            # 停止无人机数据推送任务
            if hasattr(self, 'drone_data_task'):
                self.drone_data_task.cancel()
                logger.info('已取消无人机数据推送任务')

            # 清理组连接（如果有的话）
            if hasattr(self, 'group_name') and self.group_name is not None:
                group_name = self.group_name
                channel_name = self.channel_name
                logger.info(f"用户关闭连接 group_name = {group_name}, channel_name = {channel_name}")

                if hasattr(self, 'channel_layer') and self.channel_layer:
                    await self.channel_layer.group_discard(self.group_name, self.channel_name)

            logger.info(f'WebSocket连接已断开，关闭代码: {close_code}')
        except Exception as ex:
            logger.error(f'断开连接清理错误: {ex}')

        # Receive message from room group

    async def send_message(self, event):
        msg = event['message']
        socket_connect_type = event['socket_connect_type']
        await self.send(text_data=json.dumps({
            'data': msg,
            'socket_connect_type': socket_connect_type
        }))
