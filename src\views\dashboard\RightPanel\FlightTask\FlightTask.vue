<template>
  <div class="flight-task">
    <!-- 任务列表视图 -->
    <div v-if="taskStore.currentViewMode === 'list'" class="task-list-view">
      <!-- 顶部日期选择器区域 -->
      <div class="date-picker-section">
        <DateRangePicker v-model:dateRange="dateRange" @dateRangeChange="handleDateRangeChange" />
      </div>

      <!-- 中间任务列表区域 -->
      <div class="task-list-section">
        <!-- 列表头部筛选 -->
        <div class="task-list-header">
          <div class="header-title">任务列表</div>
          <div class="filter-dropdowns">
            <!-- 类型筛选 -->
            <el-dropdown @command="handleTypeFilter" trigger="click">
              <span class="dropdown-trigger">
                {{ currentTypeLabel }}
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="type in typeOptions"
                    :key="type.value"
                    :command="type.value"
                  >
                    {{ type.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 状态筛选 -->
            <el-dropdown @command="handleStatusFilter" trigger="click">
              <span class="dropdown-trigger">
                {{ currentStatusLabel }}
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="status in statusOptions"
                    :key="status.value"
                    :command="status.value"
                  >
                    {{ status.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 任务卡片列表 -->
        <div class="task-list-content">
          <el-scrollbar class="task-scrollbar">
            <div class="task-cards">
              <TaskCard
                v-for="task in filteredTasks"
                :key="task.id"
                :task="task"
                @actionClick="handleTaskAction"
              />
            </div>
          </el-scrollbar>
        </div>
      </div>

      <!-- 底部分页区域 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="taskStore.pagination.currentPage"
          v-model:page-size="taskStore.pagination.pageSize"
          :total="taskStore.pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, prev, pager, next"
          :pager-count="5"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 新建任务视图 -->
    <div v-else-if="taskStore.currentViewMode === 'create'" class="create-task-view">
      <CreateTaskForm
        :formData="taskStore.createTaskForm"
        :loading="taskStore.isLoadingTasks"
        :taskDroneEstimate="taskStore.taskDroneEstimate"
        @submit="handleCreateTaskSubmit"
        @cancel="handleCreateTaskCancel"
        @back="handleCreateTaskBack"
        @quickReview="handleCreateTaskQuickReview"
      />
    </div>

    <!-- 任务详情视图 -->
    <div v-else-if="taskStore.currentViewMode === 'detail'" class="task-detail-view">
      <TaskDetailView
        :task="taskStore.selectedTask"
        :loading="taskStore.isLoadingTasks"
        @back="handleTaskDetailBack"
        @confirm="handleTaskDetailConfirm"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ArrowDown } from '@element-plus/icons-vue'
import DateRangePicker from '@/components/common/DateRangePicker/DateRangePicker.vue'
import TaskCard from '@/views/dashboard/RightPanel/FlightTask/components/TaskCard.vue'
import CreateTaskForm from '@/views/dashboard/RightPanel/FlightTask/components/CreateTaskForm.vue'
import TaskDetailView from '@/views/dashboard/RightPanel/FlightTask/components/TaskDetail.vue'
import { useTaskStore } from '@/stores/taskStore'
import type { CreateTaskRequest } from '@/types/ui'

// 使用任务Store
const taskStore = useTaskStore()

// 类型筛选选项（基于TaskPurpose）
const typeOptions = [
  { label: '全部类型', value: 'all' },
  { label: '拍摄', value: '拍摄' },
  { label: '全景', value: '全景' },
  { label: '三维', value: '三维' },
  { label: '正射', value: '正射' },
  { label: '视频', value: '视频' },
]

// 任务状态筛选选项（基于TaskStatus）
const statusOptions = [
  { label: '全部状态', value: 'all' },
  { label: '待执行', value: '待执行' },
  { label: '执行中', value: '执行中' },
  { label: '已完成', value: '已完成' },
  { label: '已取消', value: '已取消' },
  { label: '异常', value: '异常' },
  { label: '暂停中', value: '暂停中' },
]

// 响应式数据
const dateRange = ref<[Date, Date]>([
  new Date(2025, 2, 3), // 2025-03-03
  new Date(2025, 3, 2), // 2025-04-02
])
const activeType = ref('all')
const activeStatus = ref('all')

// 计算属性
const currentTypeLabel = computed(() => {
  return typeOptions.find((option) => option.value === activeType.value)?.label || '全部类型'
})

const currentStatusLabel = computed(() => {
  return statusOptions.find((option) => option.value === activeStatus.value)?.label || '全部状态'
})

const filteredTasks = computed(() => {
  // 从Store获取基础任务列表（只经过日期和分页筛选）
  let tasks = taskStore.tasks

  // 前端类型筛选
  if (activeType.value !== 'all') {
    tasks = tasks.filter((task) => task.purpose === activeType.value)
  }

  // 前端状态筛选
  if (activeStatus.value !== 'all') {
    tasks = tasks.filter((task) => task.status === activeStatus.value)
  }

  return tasks
})

// 事件处理函数
const handleDateRangeChange = async ({
  startDate,
  endDate,
}: {
  startDate: Date
  endDate: Date
}) => {
  // 更新筛选条件并重新获取数据
  taskStore.updateFilters({
    dateRange: [startDate, endDate],
  })
  taskStore.updatePagination({ currentPage: 1 })
  await taskStore.fetchTasks()
}

const handleTypeFilter = (type: string) => {
  activeType.value = type
  // 类型筛选在前端进行，不调用API
  console.log('类型筛选:', type)
}

const handleStatusFilter = (status: string) => {
  activeStatus.value = status
  // 状态筛选在前端进行，不调用API
  console.log('状态筛选:', status)
}

const handleTaskAction = (taskId: string, action: string) => {
  // 处理任务操作（如查看详情）
  if (action === 'view') {
    taskStore.selectTask(taskId)
  }
  console.log('任务操作:', taskId, action)
}

const handlePageChange = async (page: number) => {
  taskStore.updatePagination({ currentPage: page })
  await taskStore.fetchTasks()
}

const handleSizeChange = async (size: number) => {
  taskStore.updatePagination({
    pageSize: size,
    currentPage: 1,
  })
  await taskStore.fetchTasks()
}

// 创建任务相关事件处理
const handleCreateTaskSubmit = async (data: CreateTaskRequest) => {
  // 调用API创建任务
  console.log('创建任务:', data)
  // 创建成功后返回列表
  taskStore.setViewMode('list')
  // 重新获取任务列表
  await taskStore.fetchTasks()
}

const handleCreateTaskCancel = () => {
  taskStore.cancelCreateTask()
}

const handleCreateTaskBack = () => {
  taskStore.cancelCreateTask()
}

const handleCreateTaskQuickReview = async (data: CreateTaskRequest) => {
  // 调用API一键审核
  console.log('一键审核:', data)
  // 审核成功后返回列表
  taskStore.setViewMode('list')
  // 重新获取任务列表
  await taskStore.fetchTasks()
}

// 任务详情相关事件处理
const handleTaskDetailBack = () => {
  taskStore.setViewMode('list')
}

const handleTaskDetailConfirm = () => {
  taskStore.setViewMode('list')
}

// 组件挂载时初始化数据
onMounted(async () => {
  // 初始化前端筛选状态
  activeType.value = 'all'
  activeStatus.value = 'all'

  // 获取任务列表数据（只传递日期和分页参数）
  await taskStore.fetchTasks()
})
</script>

<style scoped lang="scss">
.flight-task {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  row-gap: 0.5rem;
  flex: 1;
  align-items: center;

  // 视图容器
  .task-list-view,
  .create-task-view,
  .task-detail-view {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  // 顶部日期选择器区域
  .date-picker-section {
    flex-shrink: 0;
    width: 100%;
    // DateRangePicker 组件内部已统一设置样式
  }

  // 中间任务列表区域
  .task-list-section {
    // padding: 0.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; // 重要：确保flex子项可以收缩
    width: 100%;

    .task-list-header {
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem;
      border-bottom: 1px solid $border-color-light;
      // margin-bottom: 0.5rem;

      .header-title {
        font-size: $font-size-panel-title;
        color: $text-default;
        font-weight: 500;
      }

      .filter-dropdowns {
        display: flex;
        gap: 0.5rem;

        .dropdown-trigger {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          padding: 0.25rem 0.5rem;
          font-size: $font-size-panel-normal;
          color: $text-default;
          cursor: pointer;
          border: 1px solid $border-color-light;
          border-radius: $border-radius-small;
          transition: all 0.3s ease;
          min-width: 5rem;
          justify-content: space-between;

          &:hover {
            color: $text-active;
          }

          .dropdown-icon {
            font-size: 0.75rem;
            transition: transform 0.3s ease;
          }
        }
      }
    }

    .task-list-content {
      flex: 1;
      min-height: 0;

      .task-scrollbar {
        height: 100%;

        .task-cards {
          display: flex;
          flex-direction: column;
          gap: 0.4rem;
        }
      }
    }
  }

  // 底部分页区域
  .pagination-section {
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.3rem;
    border-top: 1px solid $border-color-light;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden; // 防止分页组件超出容器
  }
}
</style>
