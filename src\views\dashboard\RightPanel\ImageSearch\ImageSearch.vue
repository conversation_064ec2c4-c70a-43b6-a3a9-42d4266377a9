<template>
  <div class="image-search">
    <!-- 时间范围选择器 -->
    <div class="time-selector-section">
      <DateRangePicker
        v-model:dateRange="dateRange"
        :showQuickSelect="true"
        @dateRangeChange="handleDateRangeChange"
      />
    </div>

    <!-- 坐标搜索器 -->
    <div class="coordinate-section">
      <CoordinateSearch
        ref="coordinateSearchRef"
        placeholder="请拾取坐标"
        @search="handleCoordinateSearch"
        @coordinateChange="handleCoordinateChange"
      />
    </div>

    <!-- 搜索结果展示区域 -->
    <div class="search-results">
      <div v-if="searchParams.dateRange || searchParams.coordinates" class="search-info">
        <div v-if="searchParams.dateRange" class="search-item">
          <span class="label">时间范围：</span>
          <span class="value">{{ formatDateRange(searchParams.dateRange) }}</span>
        </div>
        <div v-if="searchParams.coordinates" class="search-item">
          <span class="label">搜索坐标：</span>
          <span class="value">{{ searchParams.coordinates.raw }}</span>
        </div>
      </div>

      <!-- 这里可以添加搜索结果列表 -->
      <div class="results-placeholder">
        <el-empty description="请设置搜索条件" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import DateRangePicker from '@/components/common/DateRangePicker/DateRangePicker.vue'
import CoordinateSearch from '@/components/common/CoordinateSearch/CoordinateSearch.vue'

// 时空搜图组件
interface SearchParams {
  dateRange?: { startDate: Date; endDate: Date }
  coordinates?: { longitude: number; latitude: number; raw: string }
}

// 响应式数据
const dateRange = ref<[Date, Date]>([
  new Date(new Date().getFullYear(), new Date().getMonth(), 1), // 当月第一天
  new Date(), // 今天
])

const searchParams = ref<SearchParams>({})
const coordinateSearchRef = ref()

// 处理日期范围变化
const handleDateRangeChange = ({ startDate, endDate }: { startDate: Date; endDate: Date }) => {
  searchParams.value.dateRange = { startDate, endDate }
  console.log('时间范围变化:', startDate.toLocaleDateString(), endDate.toLocaleDateString())

  // 这里可以触发搜索逻辑
  performSearch()
}

// 处理坐标搜索
const handleCoordinateSearch = (coordinates: {
  longitude: number
  latitude: number
  raw: string
}) => {
  searchParams.value.coordinates = coordinates
  console.log('坐标搜索:', coordinates)

  // 这里可以触发搜索逻辑
  performSearch()
}

// 处理坐标输入变化
const handleCoordinateChange = (value: string) => {
  console.log('坐标输入变化:', value)
}

// 执行搜索
const performSearch = () => {
  console.log('执行时空搜图搜索:', searchParams.value)
  // 这里可以调用 API 进行搜索
}

// 格式化日期范围显示
const formatDateRange = (range: { startDate: Date; endDate: Date }) => {
  const start = range.startDate.toLocaleDateString()
  const end = range.endDate.toLocaleDateString()
  return `${start} 至 ${end}`
}

// 清空搜索条件
const clearSearch = () => {
  searchParams.value = {}
  coordinateSearchRef.value?.clearCoordinates()
}

// 暴露方法给父组件
defineExpose({
  clearSearch,
  performSearch,
})
</script>

<style scoped lang="scss">

.image-search {
  width: 100%;
  height: 100%;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  .time-selector-section {
    // DateRangePicker 组件内部已有样式，这里只需要确保容器正确
  }

  .coordinate-section {
    // CoordinateSearch 组件样式
  }

  .search-results {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    overflow: hidden;

    .search-info {
      background: rgba(0, 255, 254, 0.05);
      border: 1px solid rgba(0, 255, 254, 0.2);
      border-radius: 0.25rem;
      padding: 0.5rem;
      font-size: 0.875rem;

      .search-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.25rem;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: $text-secondary;
          margin-right: 0.5rem;
          min-width: 5rem;
        }

        .value {
          color: $text-default;
          font-weight: 500;
        }
      }
    }

    .results-placeholder {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      :deep(.el-empty) {
        .el-empty__description {
          color: $text-secondary;
        }
      }
    }
  }
}
</style>
