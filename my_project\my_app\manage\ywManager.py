from collections import defaultdict
import json
class YwManager:
    def __init__(self, connection):
        self.connection = connection

    def get_zhbzfx(self, request):
        """
        查询综合保障分析数据的函数。
        根据请求参数动态构建 SQL 查询语句，支持条件查询、时间范围查询、排序和分页功能。

        :param request: 请求对象，包含查询所需的参数
        :return: 包含查询结果数据列表和数据总数的字典
        """
        # 获取请求数据中的参数
        params = request.data
        # 构建基础 SQL 查询语句，查询所有综合保障分析数据
        sql = ''' 
                 SELECT  * FROM	"WJYY_GZ_ZHBZFX"     where 1=1  '''
        # 用于存储动态查询条件的列表
        conditions = []
        # 定义时间相关参数的列表，这些参数用于特殊处理
        timearr = ["cbsj_end", "cjsj_start", "cjsj_end", "create_time_start", "create_time_end", "page_size", "page",
                   "page_close"]
        # 定义数字类型参数的列表，这些参数在构建查询条件时使用精确匹配
        bumberarr = ["id", "jd", "wd", "jfmj", "model_type", "create_user_id"]
        # 遍历请求参数，根据参数类型构建查询条件
        for key, value in params.items():
            # 如果参数不在时间和数字参数列表中，且值不为空字符串，则使用模糊匹配
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"{key} LIKE '%{value}%'")
            # 如果参数不在时间参数列表中，且值不为空字符串，则使用精确匹配
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"{key} = {value}")
        # 如果有动态查询条件，将其添加到基础 SQL 语句中
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)
        # 如果请求参数中包含创建时间范围的起始时间，且起始时间不为空，则添加时间范围查询条件
        if "create_time_start" in params and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        # 构建用于计算总数的 SQL 语句，将基础 SQL 语句中的查询字段替换为 COUNT(*)
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")
        # 获取数据库连接的游标
        cursor = self.connection.cursor()
        # 执行总数查询 SQL 语句
        cursor.execute(total_sql)
        # 获取总数查询结果
        total_result = cursor.fetchone()
        # 提取总数，如果查询结果为空则总数为 0
        total = total_result[0] if total_result else 0
        # 判断是否关闭分页功能，默认为不关闭
        page_close = bool(params.get("page_close", False))
        if not page_close:
            # 如果不关闭分页，添加排序和分页信息到 SQL 语句中
            sql += " order by id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            # 如果关闭分页，只添加排序信息到 SQL 语句中
            sql += " order by id desc"
        # 执行最终的 SQL 查询语句
        cursor.execute(sql)
        # 获取查询结果
        records = cursor.fetchall()
        # 获取查询结果的列名
        titile = [title[0] for title in cursor.description]
        # 将查询结果转换为字典列表
        data_list = [dict(list(zip(titile, item))) for item in records]
        # 返回包含查询结果数据列表和数据总数的字典
        return {"data": data_list, "total": total}

    def get_renche(self, request):
        """
        查询人车数据的函数。
        根据请求参数动态构建 SQL 查询语句，支持条件查询、时间范围查询、排序和分页功能。

        :param request: 请求对象，包含查询所需的参数
        :return: 包含查询结果数据列表和数据总数的字典
        """
        # 获取请求数据中的参数
        params = request.data
        # 构建基础 SQL 查询语句，查询所有人车数据
        sql = ''' 
                 SELECT  * FROM	 "WJYY_GZ_RENCHE_DICT"     where 1=1  '''
        # 用于存储动态查询条件的列表
        conditions = []
        # 定义时间相关参数的列表，这些参数用于特殊处理
        timearr = ["cbsj_end", "cjsj_start", "cjsj_end", "create_time_start", "create_time_end", "page_size", "page",
                   "page_close"]
        # 定义数字类型参数的列表，这些参数在构建查询条件时使用精确匹配
        bumberarr = ["id", "jd", "wd", "jfmj", "type", "area", "model_type", "create_user_id"]
        # 遍历请求参数，根据参数类型构建查询条件
        for key, value in params.items():
            # 如果参数不在时间和数字参数列表中，且值不为空字符串，则使用模糊匹配
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"{key} LIKE '%{value}%'")
            # 如果参数不在时间参数列表中，且值不为空字符串，则使用精确匹配
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"{key} = {value}")
        # 如果有动态查询条件，将其添加到基础 SQL 语句中
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)
        # 如果请求参数中包含创建时间范围的起始时间，且起始时间不为空，则添加时间范围查询条件
        if "create_time_start" in params and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        # 构建用于计算总数的 SQL 语句，将基础 SQL 语句中的查询字段替换为 COUNT(*)
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")
        # 获取数据库连接的游标
        cursor = self.connection.cursor()
        # 执行总数查询 SQL 语句
        cursor.execute(total_sql)
        # 获取总数查询结果
        total_result = cursor.fetchone()
        # 提取总数，如果查询结果为空则总数为 0
        total = total_result[0] if total_result else 0
        # 判断是否关闭分页功能，默认为不关闭
        page_close = bool(params.get("page_close", False))
        if not page_close:
            # 如果不关闭分页，添加排序和分页信息到 SQL 语句中
            sql += " order by id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            # 如果关闭分页，只添加排序信息到 SQL 语句中
            sql += " order by id desc"
        # 执行最终的 SQL 查询语句
        cursor.execute(sql)
        # 获取查询结果
        records = cursor.fetchall()
        # 获取查询结果的列名
        titile = [title[0] for title in cursor.description]
        # 将查询结果转换为字典列表
        data_list = [dict(list(zip(titile, item))) for item in records]
        # 返回包含查询结果数据列表和数据总数的字典
        return {"data": data_list, "total": total}

    def get_all_renche(self, request):
        """
        查询人车数据的函数。
        根据请求参数动态构建 SQL 查询语句，支持条件查询、时间范围查询、排序和分页功能。

        :param request: 请求对象，包含查询所需的参数
        :return: 包含查询结果数据列表和数据总数的字典
        """
        # 构建基础 SQL 查询语句，查询所有人车数据
        sql = ''' 
                 SELECT  id,mc as name,high as "maxHeight",width as "maxWidth",load as "maxWeight" FROM	 "WJYY_GZ_RENCHE_DICT"     where 1=1 and type = 2'''
        # 获取数据库连接的游标
        cursor = self.connection.cursor()
        # 执行最终的 SQL 查询语句
        cursor.execute(sql)
        # 获取查询结果
        records = cursor.fetchall()
        # 获取查询结果的列名
        titile = [title[0] for title in cursor.description]
        # 将查询结果转换为字典列表
        data_list = [dict(list(zip(titile, item))) for item in records]
        # 返回包含查询结果数据列表和数据总数的字典
        return {"data": data_list}

    def get_jjdy(self, request):
        """
        查询集结地域数据的函数。
        根据请求参数动态构建 SQL 查询语句，支持条件查询、时间范围查询、排序和分页功能。

        :param request: 请求对象，包含查询所需的参数
        :return: 包含查询结果数据列表和数据总数的字典
        """
        # 获取请求数据中的参数
        params = request.data
        # 构建基础 SQL 查询语句，查询所有集结地域数据
        sql = ''' 
                 SELECT  * FROM	"WJYY_GZ_JJDY"     where 1=1  '''
        # 用于存储动态查询条件的列表
        conditions = []
        # 定义时间相关参数的列表，这些参数用于特殊处理
        timearr = ["cbsj_end", "cjsj_start", "cjsj_end", "create_time_start", "create_time_end", "page_size", "page",
                   "page_close"]
        # 定义数字类型参数的列表，这些参数在构建查询条件时使用精确匹配
        bumberarr = ["id", "jd", "wd", "jfmj", "model_type", "create_user_id"]
        # 遍历请求参数，根据参数类型构建查询条件
        for key, value in params.items():
            # 如果参数不在时间和数字参数列表中，且值不为空字符串，则使用模糊匹配
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"{key} LIKE '%{value}%'")
            # 如果参数不在时间参数列表中，且值不为空字符串，则使用精确匹配
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"{key} = {value}")
        # 如果有动态查询条件，将其添加到基础 SQL 语句中
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)
        # 如果请求参数中包含创建时间范围的起始时间，且起始时间不为空，则添加时间范围查询条件
        if "create_time_start" in params and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        # 构建用于计算总数的 SQL 语句，将基础 SQL 语句中的查询字段替换为 COUNT(*)
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")
        # 获取数据库连接的游标
        cursor = self.connection.cursor()
        # 执行总数查询 SQL 语句
        cursor.execute(total_sql)
        # 获取总数查询结果
        total_result = cursor.fetchone()
        # 提取总数，如果查询结果为空则总数为 0
        total = total_result[0] if total_result else 0
        # 判断是否关闭分页功能，默认为不关闭
        page_close = bool(params.get("page_close", False))
        if not page_close:
            # 如果不关闭分页，添加排序和分页信息到 SQL 语句中
            sql += " order by id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            # 如果关闭分页，只添加排序信息到 SQL 语句中
            sql += " order by id desc"
        # 执行最终的 SQL 查询语句
        cursor.execute(sql)
        # 获取查询结果
        records = cursor.fetchall()
        # 获取查询结果的列名
        titile = [title[0] for title in cursor.description]
        # 将查询结果转换为字典列表
        data_list = [dict(list(zip(titile, item))) for item in records]
        # 返回包含查询结果数据列表和数据总数的字典
        return {"data": data_list, "total": total}

    def get_qsfx(self, request):
        """
        查询趋势分析数据的函数。
        根据请求参数动态构建 SQL 查询语句，支持条件查询、时间范围查询、排序和分页功能。

        :param request: 请求对象，包含查询所需的参数
        :return: 包含查询结果数据列表和数据总数的字典
        """
        # 获取请求数据中的参数
        params = request.data
        # 构建基础 SQL 查询语句，查询所有趋势分析数据
        sql = ''' 
                 SELECT  * FROM	"WJYY_GZ_QSFX"     where 1=1  '''
        # 用于存储动态查询条件的列表
        conditions = []
        # 定义时间相关参数的列表，这些参数用于特殊处理
        timearr = ["cbsj_end", "cjsj_start", "cjsj_end", "create_time_start", "create_time_end", "page_size", "page",
                   "page_close"]
        # 定义数字类型参数的列表，这些参数在构建查询条件时使用精确匹配
        bumberarr = ["id", "jd", "wd", "jfmj", "model_type", "create_user_id"]
        # 遍历请求参数，根据参数类型构建查询条件
        for key, value in params.items():
            # 如果参数不在时间和数字参数列表中，且值不为空字符串，则使用模糊匹配
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"{key} LIKE '%{value}%'")
            # 如果参数不在时间参数列表中，且值不为空字符串，则使用精确匹配
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"{key} = {value}")
        # 如果有动态查询条件，将其添加到基础 SQL 语句中
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)
        # 如果请求参数中包含创建时间范围的起始时间，且起始时间不为空，则添加时间范围查询条件
        if "create_time_start" in params and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        # 构建用于计算总数的 SQL 语句，将基础 SQL 语句中的查询字段替换为 COUNT(*)
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")
        # 获取数据库连接的游标
        cursor = self.connection.cursor()
        # 执行总数查询 SQL 语句
        cursor.execute(total_sql)
        # 获取总数查询结果
        total_result = cursor.fetchone()
        # 提取总数，如果查询结果为空则总数为 0
        total = total_result[0] if total_result else 0
        # 判断是否关闭分页功能，默认为不关闭
        page_close = bool(params.get("page_close", False))
        if not page_close:
            # 如果不关闭分页，添加排序和分页信息到 SQL 语句中
            sql += " order by id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            # 如果关闭分页，只添加排序信息到 SQL 语句中
            sql += " order by id desc"
        # 执行最终的 SQL 查询语句
        cursor.execute(sql)
        # 获取查询结果
        records = cursor.fetchall()
        # 获取查询结果的列名
        titile = [title[0] for title in cursor.description]
        # 将查询结果转换为字典列表
        data_list = [dict(list(zip(titile, item))) for item in records]
        # 返回包含查询结果数据列表和数据总数的字典
        return {"data": data_list, "total": total}

    # fxsj  无需与或非条件，本函数为与条件，应该是模糊匹配搜全部
    def get_fxsj(self,request):
        """
        查询分析事件（fxsj）相关数据的函数。
        根据请求参数动态构建 SQL 查询语句，支持条件查询、时间范围查询、排序和分页功能，
        同时对结果中的 `baogao` 字段进行 JSON 格式处理。

        :param request: 请求对象，包含查询所需的参数
        :return: 包含查询结果数据列表和数据总数的字典
        """
        # 从请求对象中获取请求数据
        params = request.data
        # 构建基础 SQL 查询语句，查询所有综合分析数据
        sql = ''' 
                        SELECT  * FROM    "WJYY_GZ_FXSJ"     where 1=1  '''
        # 用于存储动态生成的查询条件
        conditions = []
        # 定义时间相关参数、分页相关参数以及特殊处理的参数列表
        timearr = ["create_time_start", "create_time_end", "page_size", "page", "page_close", "sjfxxx", "huozhe", "fei",
                   "id", "create_user_id",
                   "mc"]
        # 定义数字类型的参数列表
        bumberarr = ["id", "create_user_id", "update_user_id", "fei", "mc"]


        # 如果请求参数中包含 'sjfxxx' 且不为空，修改基础 SQL 语句以进行 JSON 字段的模糊查询
        if "sjfxxx" in params and params["sjfxxx"] != "":
            sql = '''
                SELECT  * FROM    "WJYY_GZ_ZHFX"  WHERE EXISTS (
                            SELECT 1
                            FROM jsonb_each_text(baogao) AS jt(key, value)
                            WHERE jt.key LIKE '%{}%' OR jt.value LIKE '%{}%'
                        )  
            '''.format(params["sjfxxx"], params["sjfxxx"])

        # 遍历请求参数，根据参数类型和值动态生成查询条件
        for key, value in params.items():
            # 如果参数既不在时间相关参数列表，也不在数字类型参数列表，且值不为空字符串，使用模糊匹配
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"{key} LIKE '%{value}%'")
            # 如果参数不在时间相关参数列表，且值不为空字符串，使用精确匹配
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"{key} = {value}")

        # 如果生成了查询条件，将其添加到基础 SQL 语句中
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)

        # 如果请求参数中包含创建时间范围的起始时间且不为空，添加时间范围查询条件
        if "create_time_start" in params and params["create_time_start"] != None and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        if  "sjmc" in params and params["sjmc"] != None and str(params["sjmc"]) != "[]" and str(params["sjmc"]) != "":
            # 假设 params["id"] 是一个 Python 列表，如 [1, 3, 5]
            values = ', '.join(map(str, params["sjmc"]))
            sql += f" AND sjmc  IN ({values})"

        # 生成用于计算查询结果总数的 SQL 语句，将原 SQL 中的查询字段替换为 COUNT(*)
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")

        # 获取数据库连接的游标
        cursor = self.connection.cursor()
        # 执行总数查询 SQL 语句
        cursor.execute(total_sql)
        # 获取查询结果的第一行
        total_result = cursor.fetchone()
        # 提取总数，如果查询结果为空则总数为 0
        total = total_result[0] if total_result else 0

        # 判断是否关闭分页功能，默认为不关闭
        page_close = bool(params.get("page_close", False))
        if not page_close:
            # 如果不关闭分页，添加排序和分页信息到 SQL 语句中
            sql += " order by id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            # 如果关闭分页，只添加排序信息到 SQL 语句中
            sql += " order by id desc"

        # 执行最终的 SQL 查询语句
        cursor.execute(sql)
        # 获取查询结果的所有行
        records = cursor.fetchall()
        # 获取查询结果的列名
        titles = [title[0] for title in cursor.description]
        # 初始化存储查询结果的列表
        data_list = []
        # 遍历查询结果的每一行
        for item in records:
            # 将列名和该行数据组合成字典
            record_dict = dict(zip(titles, item))
            # 如果结果字典中包含 'sjfxxx' 字段
            if 'sjfxxx' in record_dict:
                try:
                    # 如果 'sjfxxx' 字段是字符串类型，尝试将其解析为 JSON 对象
                    if isinstance(record_dict['sjfxxx'], str):
                        record_dict['sjfxxx'] = json.loads(record_dict['sjfxxx'])
                except json.JSONDecodeError:
                    # 如果解析失败，忽略该错误
                    pass
            # 将处理后的结果字典添加到结果列表中
            data_list.append(record_dict)

        # 返回包含查询结果数据列表和数据总数的字典
        return {"data": data_list, "total": total}


    def get_bggl(self, request):
        """
        查询报告管理（bggl）相关数据的函数。
        根据请求参数动态构建 SQL 查询语句，支持条件查询、时间范围查询、排序和分页功能，
        同时对结果中的 `baogao` 字段进行 JSON 格式处理。

        :param request: 请求对象，包含查询所需的参数
        :return: 包含查询结果数据列表和数据总数的字典
        """
        # 从请求对象中获取请求数据
        params = request.data
        # 构建基础 SQL 查询语句，查询所有综合分析数据
        sql = ''' 
                 SELECT  * FROM    "WJYY_GZ_BGGL"     where 1=1  '''
        # 用于存储动态生成的查询条件
        conditions = []
        # 定义时间相关参数、分页相关参数以及特殊处理的参数列表
        timearr = ["create_time_start", "create_time_end", "page_size", "page", "page_close", "baogao", "huozhe","fei","id", "create_user_id",
                   "bg_type"]
        # 定义数字类型的参数列表
        bumberarr = ["id", "create_user_id", "update_user_id", "fei","bg_type"]

        # 如果请求参数中包含 'baogao' 且不为空，修改基础 SQL 语句以进行 JSON 字段的模糊查询
        if "baogao" in params and params["baogao"] != "":
            sql = '''
                SELECT  * FROM    "WJYY_GZ_ZHFX"  WHERE EXISTS (
                            SELECT 1
                            FROM jsonb_each_text(baogao) AS jt(key, value)
                            WHERE jt.key LIKE '%{}%' OR jt.value LIKE '%{}%'
                        )  
            '''.format(params["baogao"], params["baogao"])

        # 遍历请求参数，根据参数类型和值动态生成查询条件
        for key, value in params.items():
            # 如果参数既不在时间相关参数列表，也不在数字类型参数列表，且值不为空字符串，使用模糊匹配
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"{key} LIKE '%{value}%'")
            # 如果参数不在时间相关参数列表，且值不为空字符串，使用精确匹配
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"{key} = {value}")

        # 如果生成了查询条件，将其添加到基础 SQL 语句中
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)

        # 如果请求参数中包含创建时间范围的起始时间且不为空，添加时间范围查询条件
        if "create_time_start" in params and params["create_time_start"] != None and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        if  "bg_type" in params and params["bg_type"] != None and str(params["bg_type"]) != "[]" and str(params["bg_type"]) != "":
            # 假设 params["id"] 是一个 Python 列表，如 [1, 3, 5]
            values = ', '.join(map(str, params["bg_type"]))
            sql += f" AND bg_type  IN ({values})"

        # 生成用于计算查询结果总数的 SQL 语句，将原 SQL 中的查询字段替换为 COUNT(*)
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")

        # 获取数据库连接的游标
        cursor = self.connection.cursor()
        # 执行总数查询 SQL 语句
        cursor.execute(total_sql)
        # 获取查询结果的第一行
        total_result = cursor.fetchone()
        # 提取总数，如果查询结果为空则总数为 0
        total = total_result[0] if total_result else 0

        # 判断是否关闭分页功能，默认为不关闭
        page_close = bool(params.get("page_close", False))
        if not page_close:
            # 如果不关闭分页，添加排序和分页信息到 SQL 语句中
            sql += " order by id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            # 如果关闭分页，只添加排序信息到 SQL 语句中
            sql += " order by id desc"

        # 执行最终的 SQL 查询语句
        cursor.execute(sql)
        # 获取查询结果的所有行
        records = cursor.fetchall()
        # 获取查询结果的列名
        titles = [title[0] for title in cursor.description]
        # 初始化存储查询结果的列表
        data_list = []
        # 遍历查询结果的每一行
        for item in records:
            # 将列名和该行数据组合成字典
            record_dict = dict(zip(titles, item))
            # 如果结果字典中包含 'baogao' 字段
            if 'baogao' in record_dict:
                try:
                    # 如果 'baogao' 字段是字符串类型，尝试将其解析为 JSON 对象
                    if isinstance(record_dict['baogao'], str):
                        record_dict['baogao'] = json.loads(record_dict['baogao'])
                except json.JSONDecodeError:
                    # 如果解析失败，忽略该错误
                    pass
            # 将处理后的结果字典添加到结果列表中
            data_list.append(record_dict)

        # 返回包含查询结果数据列表和数据总数的字典
        return {"data": data_list, "total": total}

    def get_bggl_reverse(self, request):
        """
        查询综合分析（ZHFX）相关数据的反向选择函数。
        根据请求参数动态构建 SQL 查询语句，支持条件查询、时间范围查询、排序和分页功能，
        同时对结果中的 `baogao` 字段进行 JSON 格式处理。

        :param request: 请求对象，包含查询所需的参数
        :return: 包含查询结果数据列表和数据总数的字典
        """
        # 从请求对象中获取请求数据
        params = request.data
        # 构建基础 SQL 查询语句，查询所有综合分析数据
        sql = ''' 
                        SELECT  * FROM    "WJYY_GZ_BGGL"     where 1=1  '''
        # 用于存储动态生成的查询条件
        conditions = []
        # 定义时间相关参数、分页相关参数以及特殊处理的参数列表
        timearr = ["create_time_start", "create_time_end", "page_size", "page", "page_close", "baogao", "huozhe", "fei",
                   "id", "create_user_id",
                   "bg_type"]
        # 定义数字类型的参数列表
        bumberarr = ["id", "create_user_id", "update_user_id", "fei", "bg_type"]

        # 如果请求参数中包含 'baogao' 且不为空，修改基础 SQL 语句以进行 JSON 字段的模糊查询
        if "baogao" in params and params["baogao"] != "":
            sql = '''
                       SELECT  * FROM    "WJYY_GZ_ZHFX"  WHERE NOT EXISTS (
                                   SELECT 1
                                   FROM jsonb_each_text(baogao) AS jt(key, value)
                                   WHERE jt.key LIKE '%{}%' OR jt.value LIKE '%{}%'
                               )  
                   '''.format(params["baogao"], params["baogao"])

        # 遍历请求参数，根据参数类型和值动态生成查询条件
        for key, value in params.items():
            # 如果参数既不在时间相关参数列表，也不在数字类型参数列表，且值不为空字符串，使用模糊匹配的取反
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"{key} NOT LIKE '%{value}%'")
            # 如果参数不在时间相关参数列表，且值不为空字符串，使用精确匹配的取反
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"{key} != {value}")

        # 如果生成了查询条件，将其添加到基础 SQL 语句中
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)

        # 如果请求参数中包含创建时间范围的起始时间且不为空，添加时间范围查询条件的取反
        if "create_time_start" in params  and params["create_time_start"] != None and params["create_time_start"] != "":
            sql += " AND (create_time <= '{}' OR create_time >= '{}')".format(params["create_time_start"],
                                                                              params["create_time_end"])
        if "bg_type" in params and params["bg_type"] != None and str(params["bg_type"]) != "[]" and str(params["bg_type"]) != "":
            # 假设 params["bg_type"] 是一个 Python 列表，如 [1, 3, 5]
            values = ', '.join(map(str, params["bg_type"]))
            sql += f" AND bg_type NOT IN ({values})"

        # 生成用于计算查询结果总数的 SQL 语句，将原 SQL 中的查询字段替换为 COUNT(*)
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")

        # 获取数据库连接的游标
        cursor = self.connection.cursor()
        # 执行总数查询 SQL 语句
        cursor.execute(total_sql)
        # 获取查询结果的第一行
        total_result = cursor.fetchone()
        # 提取总数，如果查询结果为空则总数为 0
        total = total_result[0] if total_result else 0

        # 判断是否关闭分页功能，默认为不关闭
        page_close = bool(params.get("page_close", False))
        if not page_close:
            # 如果不关闭分页，添加排序和分页信息到 SQL 语句中
            sql += " order by id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            # 如果关闭分页，只添加排序信息到 SQL 语句中
            sql += " order by id desc"

        # 执行最终的 SQL 查询语句
        cursor.execute(sql)
        # 获取查询结果的所有行
        records = cursor.fetchall()
        # 获取查询结果的列名
        titles = [title[0] for title in cursor.description]
        # 初始化存储查询结果的列表
        data_list = []
        # 遍历查询结果的每一行
        for item in records:
            # 将列名和该行数据组合成字典
            record_dict = dict(zip(titles, item))
            # 如果结果字典中包含 'baogao' 字段
            if 'baogao' in record_dict:
                try:
                    # 如果 'baogao' 字段是字符串类型，尝试将其解析为 JSON 对象
                    if isinstance(record_dict['baogao'], str):
                        record_dict['baogao'] = json.loads(record_dict['baogao'])
                except json.JSONDecodeError:
                    # 如果解析失败，忽略该错误
                    pass
            # 将处理后的结果字典添加到结果列表中
            data_list.append(record_dict)

        # 返回包含查询结果数据列表和数据总数的字典
        return {"data": data_list, "total": total}

    def get_bggl_or(self, request):
        """
        查询报告管理（bggl）相关数据的函数。
        根据请求参数动态构建 SQL 查询语句，支持条件查询、时间范围查询、排序和分页功能，
        同时对结果中的 `baogao` 字段进行 JSON 格式处理。

        :param request: 请求对象，包含查询所需的参数
        :return: 包含查询结果数据列表和数据总数的字典
        """
        # 从请求对象中获取请求数据
        params = request.data
        # 定义时间相关参数、分页相关参数以及特殊处理的参数列表
        timearr = ["create_time_start", "create_time_end", "page_size", "page", "page_close", "baogao", "huozhe", "fei",
                   "id", "create_user_id",
                   "bg_type"]
        # 定义数字类型的参数列表
        bumberarr = ["id", "create_user_id", "update_user_id", "fei", "bg_type"]

        # 用于存储每个条件查询的结果
        all_results = []

        # 处理 baogao 条件
        if "baogao" in params and params["baogao"] != "":
            sql = '''
                SELECT  * FROM    "WJYY_GZ_ZHFX"  WHERE EXISTS (
                            SELECT 1
                            FROM jsonb_each_text(baogao) AS jt(key, value)
                            WHERE jt.key LIKE '%{}%' OR jt.value LIKE '%{}%'
                        )  
            '''.format(params["baogao"], params["baogao"])
            results = self.execute_query(sql, params)
            all_results.extend(results)

        # 遍历请求参数，根据参数类型和值动态生成查询条件并执行查询
        for key, value in params.items():
            if key in timearr:
                continue
            conditions = []
            if key not in bumberarr and str(value).strip() != "":
                conditions.append(f"{key} LIKE '%{value}%'")
            elif str(value).strip() != "":
                conditions.append(f"{key} = {value}")

            if conditions:
                base_sql = ''' 
                    SELECT  * FROM    "WJYY_GZ_BGGL"     where 1=1  
                '''
                sql = base_sql + ' and ' + " AND ".join(conditions)
                results = self.execute_query(sql, params)
                all_results.extend(results)

        # 处理创建时间范围条件
        if "create_time_start" in params and params["create_time_start"] != None and params["create_time_start"] != "":
            base_sql = ''' 
                SELECT  * FROM    "WJYY_GZ_BGGL"     where 1=1  
            '''
            sql = base_sql + " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                                     params["create_time_end"])
            results = self.execute_query(sql, params)
            all_results.extend(results)

        # 处理 bg_type 条件
        if "bg_type" in params and params["bg_type"] != None and str(params["bg_type"]) != "[]" and str(params["bg_type"]) != "":
            base_sql = ''' 
                SELECT  * FROM    "WJYY_GZ_BGGL"     where 1=1  
            '''
            values = ', '.join(map(str, params["bg_type"]))
            sql = base_sql + f" AND bg_type  IN ({values})"
            results = self.execute_query(sql, params)
            all_results.extend(results)

        # 去除重复数据
        unique_results = []
        unique_ids = set()
        for result in all_results:
            if result['id'] not in unique_ids:
                unique_ids.add(result['id'])
                unique_results.append(result)

        # 处理分页和排序
        page_close = bool(params.get("page_close", False))
        total = len(unique_results)  # 计算总数据量

        if not page_close:
            page_size = int(params.get("page_size", 10))
            page = int(params.get("page", 1))
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            # 检查是否有足够的数据显示在当前页
            if start_index < total:
                unique_results = sorted(unique_results, key=lambda x: x['id'], reverse=True)[
                                 start_index:end_index]
            else:
                unique_results = []
        else:
            unique_results = sorted(unique_results, key=lambda x: x['id'], reverse=True)

        # 返回包含查询结果数据列表和数据总数的字典
        return {"data": unique_results, "total": total}

    def execute_query(self, sql, params):
        # 生成用于计算查询结果总数的 SQL 语句，将原 SQL 中的查询字段替换为 COUNT(*)
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")

        # 获取数据库连接的游标
        cursor = self.connection.cursor()
        # 执行总数查询 SQL 语句
        cursor.execute(total_sql)
        # 获取查询结果的第一行
        total_result = cursor.fetchone()
        # 提取总数，如果查询结果为空则总数为 0
        total = total_result[0] if total_result else 0

        # 判断是否关闭分页功能，默认为不关闭
        page_close = True
        if not page_close:
            # 如果不关闭分页，添加排序和分页信息到 SQL 语句中
            sql += " order by id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            # 如果关闭分页，只添加排序信息到 SQL 语句中
            sql += " order by id desc"

        # 执行最终的 SQL 查询语句
        cursor.execute(sql)
        # 获取查询结果的所有行
        records = cursor.fetchall()
        # 获取查询结果的列名
        titles = [title[0] for title in cursor.description]
        # 初始化存储查询结果的列表
        data_list = []
        # 遍历查询结果的每一行
        for item in records:
            # 将列名和该行数据组合成字典
            record_dict = dict(zip(titles, item))
            # 如果结果字典中包含 'baogao' 字段
            if 'baogao' in record_dict:
                try:
                    # 如果 'baogao' 字段是字符串类型，尝试将其解析为 JSON 对象
                    if isinstance(record_dict['baogao'], str):
                        record_dict['baogao'] = json.loads(record_dict['baogao'])
                except json.JSONDecodeError:
                    # 如果解析失败，忽略该错误
                    pass
            # 将处理后的结果字典添加到结果列表中
            data_list.append(record_dict)

        return data_list















