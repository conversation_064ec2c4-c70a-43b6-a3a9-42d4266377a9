/**
 * 预警类型映射表
 * 后端返回数字字符串，前端映射为具体的类型名称
 */

// 预警类型映射表（后端数字 -> 前端类型名称）
export const warningTypeMap: Record<string, string> = {
  '1': '暴露垃圾',
  '2': '违章建筑',
  '3': '水域污染',
  '4': '道路损坏',
  '5': '设施损坏',
  '6': '车辆违停',
  '7': '流动摊贩',
  '8': '松材线虫病',
  '9': '在建农房',
  '10': '积存建筑垃圾',
  '11': '占道经营',
  '12': '河道漂浮物',
  '13': '工地未苫盖',
  '14': '噪音污染',
  '15': '光污染',
  '16': '大气污染',
  '17': '土壤污染',
  '18': '交通拥堵',
  '19': '路灯故障',
  '20': '井盖缺失',
  '21': '病虫害',
  '22': '森林火灾',
  '23': '安全隐患',
  '24': '危险品',
  '25': '消防通道堵塞',
  '26': '异常聚集',
  '27': '建筑工地',
  '28': '垃圾堆放',
  '29': '其他环境问题',
  '30': '其他安全问题'
}

// 反向映射表（前端类型名称 -> 后端数字）
export const warningTypeReverseMap: Record<string, string> = Object.fromEntries(
  Object.entries(warningTypeMap).map(([key, value]) => [value, key])
)

/**
 * 根据后端返回的数字获取预警类型名称
 * @param typeCode 后端返回的类型数字字符串
 * @returns 预警类型名称
 */
export function getWarningTypeName(typeCode: string): string {
  return warningTypeMap[typeCode] || `未知类型(${typeCode})`
}

/**
 * 根据预警类型名称获取后端数字
 * @param typeName 预警类型名称
 * @returns 后端数字字符串
 */
export function getWarningTypeCode(typeName: string): string {
  return warningTypeReverseMap[typeName] || '0'
}

/**
 * 获取所有预警类型选项（用于下拉选择等）
 * @returns 预警类型选项数组
 */
export function getAllWarningTypeOptions(): Array<{ code: string; name: string }> {
  return Object.entries(warningTypeMap).map(([code, name]) => ({
    code,
    name
  }))
}

/**
 * 检查是否为有效的预警类型代码
 * @param typeCode 类型代码
 * @returns 是否有效
 */
export function isValidWarningTypeCode(typeCode: string): boolean {
  return typeCode in warningTypeMap
}

/**
 * 检查是否为有效的预警类型名称
 * @param typeName 类型名称
 * @returns 是否有效
 */
export function isValidWarningTypeName(typeName: string): boolean {
  return typeName in warningTypeReverseMap
}

/**
 * 获取预警类型的分类信息
 * @param typeCode 类型代码
 * @returns 分类信息
 */
export function getWarningTypeCategory(typeCode: string): {
  category: string;
  subcategory: string;
} {
  const categoryMap: Record<string, { category: string; subcategory: string }> = {
    // 环境卫生类
    '1': { category: '环境卫生', subcategory: '垃圾处理' },
    '10': { category: '环境卫生', subcategory: '垃圾处理' },
    '28': { category: '环境卫生', subcategory: '垃圾处理' },
    
    // 建筑类
    '2': { category: '城市建设', subcategory: '违章建筑' },
    '9': { category: '城市建设', subcategory: '在建工程' },
    '13': { category: '城市建设', subcategory: '工地管理' },
    '27': { category: '城市建设', subcategory: '工地管理' },
    
    // 环境污染类
    '3': { category: '环境保护', subcategory: '水污染' },
    '12': { category: '环境保护', subcategory: '水污染' },
    '14': { category: '环境保护', subcategory: '噪音污染' },
    '15': { category: '环境保护', subcategory: '光污染' },
    '16': { category: '环境保护', subcategory: '大气污染' },
    '17': { category: '环境保护', subcategory: '土壤污染' },
    
    // 交通类
    '4': { category: '交通管理', subcategory: '道路设施' },
    '6': { category: '交通管理', subcategory: '违停管理' },
    '18': { category: '交通管理', subcategory: '交通秩序' },
    
    // 设施类
    '5': { category: '市政设施', subcategory: '设施维护' },
    '19': { category: '市政设施', subcategory: '照明设施' },
    '20': { category: '市政设施', subcategory: '道路设施' },
    
    // 商业管理类
    '7': { category: '商业管理', subcategory: '流动摊贩' },
    '11': { category: '商业管理', subcategory: '占道经营' },
    
    // 自然灾害类
    '8': { category: '生态保护', subcategory: '病虫害' },
    '21': { category: '生态保护', subcategory: '病虫害' },
    '22': { category: '生态保护', subcategory: '森林防火' },
    
    // 安全类
    '23': { category: '安全管理', subcategory: '安全隐患' },
    '24': { category: '安全管理', subcategory: '危险品管理' },
    '25': { category: '安全管理', subcategory: '消防安全' },
    '26': { category: '安全管理', subcategory: '公共秩序' },
    
    // 其他
    '29': { category: '其他', subcategory: '环境问题' },
    '30': { category: '其他', subcategory: '安全问题' }
  }
  
  return categoryMap[typeCode] || { category: '其他', subcategory: '未分类' }
}
