import logging
# -*- coding: utf-8 -*-
import os
from vgis_office.vgis_excel.excelTools import ExcelHelper
from my_project import settings

logger = logging.getLogger('django')
import datetime

class TaskManager:
    def __init__(self, connection):
        self.connection = connection

    def get_task(self, request):

        params = request.data
        sql = ''' 
                      SELECT  * FROM	tt_drone_tasks     where 1=1  '''
        conditions = []
        timearr = [ "create_time_start", "create_time_end", "page_size", "page", "page_close"]
        bumberarr = ["id" ]
        for key, value in params.items():
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"tt_drone_tasks.{key} LIKE '%{value}%'")
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"tt_drone_tasks.{key} = {value}")
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)

        if "create_time_start" in params and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        # 排序和分页前的原始SQL用于计算总数
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")
        # 添加排序和分页
        # 添加排序和分页
        page_close = bool(params.get("page_close", False))
        if not page_close:

            # 添加排序和分页
            sql += " order by id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            sql += " order by id desc"
        # 执行总数查询
        cursor = self.connection.cursor()
        cursor.execute(total_sql)
        total_result = cursor.fetchone()
        total = total_result[0] if total_result else 0

        # 执行主查询
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = [dict(list(zip(titile, item))) for item in records]

        # 返回数据和总数
        return {"data": data_list, "total": total}
    # 查询无人机基础信息
    def get_drone_basic_info(self, request):

        params = request.data
        sql = ''' 
                      SELECT  * FROM	tt_drone_basic_info     where 1=1  '''
        conditions = []
        timearr = [ "create_time_start", "create_time_end", "page_size", "page", "page_close","purchase_date"]
        bumberarr = ["drone_id", "max_flight_time_min","obstacle_avoidance"]
        for key, value in params.items():
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"tt_drone_basic_info.{key} LIKE '%{value}%'")
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"tt_drone_basic_info.{key} = {value}")
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)

        if "create_time_start" in params and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        # 排序和分页前的原始SQL用于计算总数
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")
        # 添加排序和分页
        # 添加排序和分页
        page_close = bool(params.get("page_close", False))
        if not page_close:

            # 添加排序和分页
            sql += " order by drone_id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            sql += " order by drone_id desc"
        # 执行总数查询
        cursor = self.connection.cursor()
        cursor.execute(total_sql)
        total_result = cursor.fetchone()
        total = total_result[0] if total_result else 0

        # 执行主查询
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = [dict(list(zip(titile, item))) for item in records]

        # 返回数据和总数
        return {"data": data_list, "total": total}
    #查询飞行记录
    def get_flight_records(self, request):

        params = request.data
        sql = ''' 
                      SELECT  * FROM	tt_drone_flight_records     where 1=1 and is_del <> 1 '''
        conditions = []
        timearr = [ "create_time_start", "create_time_end", "page_size", "page", "page_close","purchase_date"]
        bumberarr = ["flight_id", "drone_id","task_id"]
        for key, value in params.items():
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"tt_drone_basic_info.{key} LIKE '%{value}%'")
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"tt_drone_basic_info.{key} = {value}")
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)

        if "create_time_start" in params and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        # 排序和分页前的原始SQL用于计算总数
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")
        # 添加排序和分页
        # 添加排序和分页
        page_close = bool(params.get("page_close", False))
        if not page_close:

            # 添加排序和分页
            sql += " order by drone_id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            sql += " order by drone_id desc"
        # 执行总数查询
        cursor = self.connection.cursor()
        cursor.execute(total_sql)
        total_result = cursor.fetchone()
        total = total_result[0] if total_result else 0

        # 执行主查询
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = [dict(list(zip(titile, item))) for item in records]

        # 返回数据和总数
        return {"data": data_list, "total": total}

    def get_drone_alerts(self, request):

        params = request.data
        sql = ''' 
                      SELECT  * FROM	tt_drone_alerts     where 1=1   '''
        conditions = []
        timearr = [ "create_time_start", "create_time_end", "page_size", "page", "page_close","purchase_date"]
        bumberarr = ["flight_id", "alert_id","task_id"]
        for key, value in params.items():
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"tt_drone_alerts.{key} LIKE '%{value}%'")
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"tt_drone_alerts.{key} = {value}")
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)

        if "create_time_start" in params and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        # 排序和分页前的原始SQL用于计算总数
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")
        # 添加排序和分页
        # 添加排序和分页
        page_close = bool(params.get("page_close", False))
        if not page_close:

            # 添加排序和分页
            sql += " order by alert_id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            sql += " order by alert_id desc"
        # 执行总数查询
        cursor = self.connection.cursor()
        cursor.execute(total_sql)
        total_result = cursor.fetchone()
        total = total_result[0] if total_result else 0

        # 执行主查询
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = [dict(list(zip(titile, item))) for item in records]

        # 返回数据和总数
        return {"data": data_list, "total": total}

    def get_alerts_files(self, request):

        params = request.data
        sql = ''' 
                      SELECT  * FROM	tt_dron_alert_file_data     where 1=1   '''
        conditions = []
        timearr = [ "create_time_start", "create_time_end", "page_size", "page", "page_close","purchase_date"]
        bumberarr = ["flight_id", "alert_id","task_id"]
        for key, value in params.items():
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"tt_dron_alert_file_data.{key} LIKE '%{value}%'")
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"tt_dron_alert_file_data.{key} = {value}")
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)

        if "create_time_start" in params and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        # 排序和分页前的原始SQL用于计算总数
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")
        # 添加排序和分页
        # 添加排序和分页
        page_close = bool(params.get("page_close", False))
        if not page_close:

            # 添加排序和分页
            sql += " order by alert_id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            sql += " order by alert_id desc"
        # 执行总数查询
        cursor = self.connection.cursor()
        cursor.execute(total_sql)
        total_result = cursor.fetchone()
        total = total_result[0] if total_result else 0

        # 执行主查询
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = [dict(list(zip(titile, item))) for item in records]

        # 返回数据和总数
        return {"data": data_list, "total": total}
    def get_weather_data(self, request):

        params = request.data
        sql = ''' 
                      SELECT  * FROM	tt_weather_data     where 1=1   '''
        conditions = []
        timearr = [ "create_time_start", "create_time_end", "page_size", "page", "page_close","purchase_date","create_weather_date_start"
                    ,"create_weather_date_end"]
        bumberarr = ["flight_id", "alert_id","task_id","weather_id"]
        for key, value in params.items():
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f"tt_weather_data.{key} LIKE '%{value}%'")
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f"tt_weather_data.{key} = {value}")
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)



        if "create_time_start" in params and params["create_time_start"] != "":
            sql += " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])
        if "create_weather_date_start" in params and params["create_weather_date_start"] != "":
            sql += " AND weather_date > '{}' and weather_date < '{}'".format(params["create_weather_date_start"],
                                                                           params["create_weather_date_end"])
        # 排序和分页前的原始SQL用于计算总数
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")
        # 添加排序和分页
        # 添加排序和分页
        page_close = bool(params.get("page_close", False))
        if not page_close:

            # 添加排序和分页
            sql += " order by weather_id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            sql += " order by weather_id desc"
        # 执行总数查询
        cursor = self.connection.cursor()
        cursor.execute(total_sql)
        total_result = cursor.fetchone()
        total = total_result[0] if total_result else 0

        # 执行主查询
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = [dict(list(zip(titile, item))) for item in records]

        # 返回数据和总数
        return {"data": data_list, "total": total}

    def get_alert_group(self, request):

        params = request.data
        param_sql = ""
        if "create_time_start" in params and params["create_time_start"] != "":
            param_sql = " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                           params["create_time_end"])

        sql = ''' 
                     SELECT  dict.cname AS "告警类型", COUNT(CASE WHEN alerts.status = 2 THEN alerts.alert_id END) AS "已完成数量", COUNT(alerts.alert_id) AS "总数量" 
                    FROM 
                        tt_drone_alerts alerts
                    JOIN 
                        tt_select_dict dict ON alerts.alert_type_id = dict.id
                    GROUP BY 
                        dict.id, dict.cname
                    ORDER BY 
                        dict.id  
                       
             
             '''

        # ,
        # ROUND(
        #     COUNT(CASE
        # WHEN
        # alerts.status = 2
        # THEN
        # alerts.alert_id
        # END) *100.0 /
        #       NULLIF(COUNT(alerts.alert_id), 0), 2
        # ) AS
        # 完成率百分比

        # 执行总数查询
        cursor = self.connection.cursor()


        # 执行主查询
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = [dict(list(zip(titile, item))) for item in records]

        # 返回数据和总数
        return {"data": data_list}

    def get_jbzx(self, request):

        params = request.data
        param_sql = ""
        if "create_time_start" in params and params["create_time_start"] != "":
            param_sql = " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                                params["create_time_end"])

        sql = ''' 
                     SELECT  *
                    FROM 
                        tt_jbzx  LIMIT 1 OFFSET 0 


             '''

        # ,
        # ROUND(
        #     COUNT(CASE
        # WHEN
        # alerts.status = 2
        # THEN
        # alerts.alert_id
        # END) *100.0 /
        #       NULLIF(COUNT(alerts.alert_id), 0), 2
        # ) AS
        # 完成率百分比

        # 执行总数查询
        cursor = self.connection.cursor()

        # 执行主查询
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = [dict(list(zip(titile, item))) for item in records]

        # 返回数据和总数
        return {"data": data_list}

    def get_sjcg(self, request):

        params = request.data
        param_sql = ""
        if "create_time_start" in params and params["create_time_start"] != "":
            param_sql = " AND create_time > '{}' and create_time < '{}'".format(params["create_time_start"],
                                                                                params["create_time_end"])

        sql = ''' 
                     SELECT  *
                    FROM 
                        tt_sjcg  LIMIT 1 OFFSET 0 


             '''


        # 执行总数查询
        cursor = self.connection.cursor()

        # 执行主查询
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = [dict(list(zip(titile, item))) for item in records]

        # 返回数据和总数
        return {"data": data_list}