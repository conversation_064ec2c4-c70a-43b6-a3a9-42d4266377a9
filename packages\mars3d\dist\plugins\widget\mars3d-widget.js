!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("mars3d")):"function"==typeof define&&define.amd?define(["exports","mars3d"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self)["mars3d-widget"]={},e.mars3d)}(this,(function(e,t){"use strict";function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(i){if("default"!==i){var n=Object.getOwnPropertyDescriptor(e,i);Object.defineProperty(t,i,n.get?n:{enumerable:!0,get:function(){return e[i]}})}})),t.default=e,t}var n=i(t);const s=new RegExp("\\.css"),o=document.head||document.getElementsByTagName("head")[0],r=+navigator.userAgent.replace(/.*(?:AppleWebKit|AndroidWebKit)\/?(\d+).*/i,"$1")<536;function a(e){return"complete"===e.readyState||"loaded"===e.readyState}function c(e,t){let i;e.sheet&&(i=!0),setTimeout((function(){i?t():c(e,t)}),20)}function l(e,t,i){const n="onload"in e;function s(){e.onload=e.onreadystatechange=null,e=null,t()}"css"===i&&(r||!n)?setTimeout((function(){c(e,t)}),1):n?(e.onload=s,e.onerror=function(t){e.onerror=null,"css"===i?console.error("该css文件不存在："+e.href,t):console.error("该js文件不存在："+e.src,t),s()}):e.onreadystatechange=function(){a(e)&&s()}}function u(e,t,i,n){function r(){const i=t.indexOf(e);i>-1&&t.splice(i,1),0===t.length&&n()}e?s.test(e)?function(e,t,i){const n=document.createElement("link");n.rel="stylesheet",l(n,i,"css"),n.async=!0,n.href=e,o.appendChild(n)}(e,0,r):function(e,t,i){const n=document.createElement("script");n.charset="utf-8",l(n,i,"js"),n.async=!t.sync,n.src=e,o.appendChild(n)}(e,i,r):setTimeout((function(){r()}))}function d(e,t,i){const n=function(){i&&i()};if(0!==(e=Array.prototype.slice.call(e||[])).length)for(let i=0,s=e.length;i<s;i++)u(e[i],e,t,n);else n()}function f(e,t){if(a(e))t();else{const e=1500;let i=!1;window.addEventListener("load",(function(){i||(t(),i=!0)})),setTimeout((function(){i||(t(),i=!0)}),e)}}const h={async:function(e,t){f(document,(function(){d(e,{},t)}))},sync:function(e,t){f(document,(function(){d(e,{sync:!0},t)}))}},w={beforeCreate:"beforeCreate",created:"created",beforeActivate:"beforeActivate",activated:"activated",openView:"openView",beforeDisable:"beforeDisable",disabled:"disabled",loadBefore:"loadBefore",load:"load"},p=window.jQuery;if(!p)throw new Error("请引入 jQuery 库");let g,_,m,b,y="",v=[];const x=["_class"];function O(e,t={},i=""){g=e,y=i,v=[],_=n.Util.merge({windowOptions:{position:"rt",maxmin:!1,resize:!0},autoDisable:!0,disableOther:!0},t.defaultOptions),m=t.version,"time"===m&&(m=(new Date).getTime());let s=t.openAtStart;if(s&&s.length>0)for(let e=0;e<s.length;e++){const t=s[e];t.hasOwnProperty("uri")&&""!==t.uri?t.hasOwnProperty("visible")&&!t.visible||(t.autoDisable=!1,t.openAtStart=!0,t._nodebug=!0,I(t),t._firstConfigBak={...t},v.push(t)):console.error("widget未配置uri",t)}if(b=t.debugger,b){const e='<div id="widget-testbar" class="mars3d-widgetbar animation-slide-bottom no-print-view" >      <div style="height: 30px; line-height:30px;"><b style="color: #4db3ff;">widget测试栏</b>&nbsp;&nbsp;<button  id="widget-testbar-remove"  type="button" class="btn btn-link btn-xs">关闭</button> </div>     <button id="widget-testbar-disableAll" type="button" class="btn btn-info" ><i class="fa fa-globe"></i>漫游</button></div>';p("body").append(e),p("#widget-testbar-remove").click((function(e){H()})),p("#widget-testbar-disableAll").click((function(e){E()}))}if(s=t.widgets,s&&s.length>0){for(let e=0;e<s.length;e++){const t=s[e];if("group"===t.type){let e=' <div class="btn-group dropup">  <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-expanded="false"><i class="fa fa-align-justify"></i>'+t.name+' <span class="caret"></span></button> <ul class="dropdown-menu">';for(let i=0;i<t.children.length;i++){const n=t.children[i];n.hasOwnProperty("uri")&&""!==n.uri?(e+=' <li data-widget="'+n.uri+'" class="widget-btn" ><a href="#"><i class="fa fa-star"></i>'+n.name+"</a></li>",I(n),n._firstConfigBak={...n},v.push(n)):console.error("widget未配置uri",n)}e+="</ul></div>",b&&!t._nodebug&&p("#widget-testbar").append(e)}else{if(!t.hasOwnProperty("uri")||""===t.uri){console.error("widget未配置uri",t);continue}if(b&&!t._nodebug){const e='<button type="button" class="btn btn-primary widget-btn" data-widget="'+t.uri+'"  > <i class="fa fa-globe"></i>'+t.name+" </button>";p("#widget-testbar").append(e)}I(t),t._firstConfigBak={...t},v.push(t)}}b&&p("#widget-testbar .widget-btn").each((function(){p(this).click((function(e){const t=p(this).attr("data-widget");t&&""!==t&&(B(t)?k(t):C(t))}))}))}for(let e=0;e<v.length;e++){const t=v[e];(t.openAtStart||t.createAtStart)&&j.push(t)}if(p(window).resize((function(){for(let e=0;e<v.length;e++){const t=v[e];t._class&&t._class.indexResize()}})),b){const e=function(){let e=window.location.toString();if(-1===e.indexOf("#"))return"";if(e=e.split("#"),e&&e.length>0)return e[1]}();e&&C(e)}D()}function A(){return n.Util.clone(_.windowOptions,x)}function I(e){if(_)for(const t in _)"windowOptions"===t||e.hasOwnProperty(t)||(e[t]=_[t]);e.path=function(e){const t=e.lastIndexOf("/");return e.substring(0,t+1)}(y+e.uri),e.name=e.name||e.label}function C(e,t){let i;!g&&e.map&&O(e.map,{},e.basePath),"string"==typeof e?(e={uri:e},null!=t&&(e.disableOther=!t)):e.uri||console.error("activate激活widget时需要uri参数！",e);for(let t=0;t<v.length;t++){const n=v[t];if(e.uri===n.uri||n.id&&e.uri===n.id){if(i=n,i.isloading)return i;for(const t in e)"uri"!==t&&(i[t]=e[t]);break}}if(i||(I(e),i=e,e._firstConfigBak||(e._firstConfigBak={...e}),v.push(e)),b&&(console.log("开始激活widget："+i.uri),window.location.hash="#"+i.uri),i.disableOther&&(Array.isArray(i.disableOther)?k(i.disableOther):E(i.uri,i.group)),i.group&&P(i.group,i.uri),i._class)i._class.isActivate?i._class.update?i._class.update():function(e){clearInterval(W),e._class.disableBase(),W=setInterval((function(){e._class.isActivate||(e._class.activateBase(),clearInterval(W))}),200)}(i):i._class.activateBase();else{for(let e=0;e<j.length;e++)if(j[e].uri===i.uri)return j[e];j.push(i),1===j.length&&D()}return i}let W;function V(e){for(let t=0;t<v.length;t++){const i=v[t];if(e===i.uri||e===i.id)return i}}function T(e){const t=V(e);return t?t._class:null}function B(e){const t=T(e);return!!t&&t.isActivate}function k(e){if(!e)return!1;if(Array.isArray(e)){const t=e;for(let e=0;e<v.length;e++){const i=v[e];for(let e=0;e<t.length;e++){const n=t[e];if(i._class&&(n===i.uri||n===i.id)){i._class.disableBase(),t.splice(e,1);break}}}}else{"object"==typeof e&&(e=e.uri);for(let t=0;t<v.length;t++){const i=v[t];if(i._class&&(e===i.uri||e===i.id))return i._class.disableBase(),!0}}return!1}function E(e,t){for(let i=0;i<v.length;i++){const n=v[i];if(t&&n.group===t);else if(!0!==e&&!n.autoDisable)continue;(!e||e!==n.uri&&e!==n.id)&&(n._class&&n._class.disableBase())}}function P(e,t){if(e)for(let i=0;i<v.length;i++){const n=v[i];if(n.group===e){if(t&&(t===n.uri||t===n.id))continue;n._class&&n._class.disableBase()}}}const j=[];let z,S;function D(){if(0===j.length)return;if(S)return void setTimeout(D,500);S=!0,z=j[0],z.isloading=!0;let e=z.uri;m&&(-1===e.indexOf("?")?e+="?cache="+m:e+="&cache="+m),window.NProgress&&window.NProgress.start(),K(w.loadBefore,{sourceTarget:z}),h.async([y+e],(function(){S=!1,z.isloading=!1,window.NProgress&&window.NProgress.done(!0),j.shift(),D()}))}function H(){p("#widget-testbar").remove()}function N(){return m}const U=new n.BaseClass;function K(e,t,i){return U.fire(e,t,i)}var R={__proto__:null,init:O,getDefWindowOptions:A,activate:C,getWidget:V,getClass:T,isActivate:B,setViewShow:function(e,t,i){const n=T(e);return n&&n.setViewShow(t,i),null==n?void 0:n.isActivate},disable:k,disableAll:E,disableGroup:P,eachWidget:function(e){for(let t=0;t<v.length;t++){e(v[t])}},bindClass:function(e){if(K(w.load,{sourceTarget:e}),z)return z.isloading=!1,z._class=new e(g,z),z._class.activateBase(),z._class;{const t=function(){let e;const t=document.scripts;for(let i=t.length-1;i>=0;i--)if(e=t[i].src,e&&-1!==e.indexOf("widgets"))return e;return""}();for(let i=0;i<v.length;i++){const n=v[i];if(t.endsWith(n.uri))return n.isloading=!1,n._class=new e(g,n),n._class.activateBase(),n._class}}},removeDebugeBar:H,getCacheVersion:N,getBasePath:function(){return y},destroy:function(){for(let e=0;e<v.length;e++){const t=v[e];t._class&&(t._class.disableBase(),t._class.destroy&&t._class.destroy(),delete t._class)}g=null},eventTarget:U,on:function(e,t,i){return U.on(e,t,i)},off:function(e,t,i){return U.off(e,t,i)},fire:K,once:function(e,t,i){return U.once(e,t,i)},listens:function(e,t){return U.listens(e,t)}};const M=window.jQuery;if(!M)throw new Error("请引入 jQuery 库");const Q=window.layer;if(!Q)throw new Error("请引入 layer.js弹窗 库");const L=n.BaseClass;let F=[];n.widget=R,n.widget.BaseWidget=class extends L{constructor(e,t){super(t),this.map=e,this.options=t,this.config=t,this.path=t.path||"",this.isActivate=!1,this.isCreate=!1,this._viewcreate_allcount=0,this._viewcreate_okcount=0,this._viewConfig=this.view,this.init()}get resources(){return null}get view(){return null}activateBase(){const e=this;if(this.isActivate)this.eachView((function(e){e._dom&&(M(".layui-layer").each((function(){M(this).css("z-index",19891e3)})),M(e._dom).css("z-index",19891014))}));else{if(U.fire(w.beforeActivate,{sourceTarget:this}),this.beforeActivate(),this.isActivate=!0,!this.isCreate){if(U.fire(w.beforeCreate,{sourceTarget:this}),this.resources&&this.resources.length>0){const t=[];for(let e=0;e<this.resources.length;e++){let i=this.resources[e];i=this._getUrl(i),-1===F.indexOf(i)&&t.push(i)}return F=F.concat(t),void h.async(t,(function(){const t=e.create((function(){e._createWidgetView(),e.isCreate=!0}));if(U.fire(w.created,{sourceTarget:e}),!t){if(e.options.createAtStart)return e.options.createAtStart=!1,e.isActivate=!1,void(e.isCreate=!0);e._createWidgetView(),e.isCreate=!0}}))}{const t=this.create((function(){e._createWidgetView(),this.isCreate=!0}));if(U.fire(w.created,{sourceTarget:this}),t)return;if(e.options.createAtStart)return e.options.createAtStart=!1,e.isActivate=!1,void(e.isCreate=!0)}this.isCreate=!0}this._createWidgetView()}}init(){}create(){}_createWidgetView(){const e=this._viewConfig;if(void 0===e||null==e)this._startActivate();else if(Array.isArray(e)){this._viewcreate_allcount=e.length,this._viewcreate_okcount=0;for(let t=0;t<e.length;t++)this.createItemView(e[t])}else this._viewcreate_allcount=1,this._viewcreate_okcount=0,this.createItemView(e)}eachView(e,t){const i=this._viewConfig;if(void 0===i||null==i)return!1;if(Array.isArray(i)){let n=!1;if(null!=t)return e(i[t]);for(let t=0;t<i.length;t++)n=e(i[t]);return n}return e(i)}createItemView(e){const t=this;switch(e.type){case"divwindow":this._openDivWindow(e);break;case"append":t.getHtml(this._getUrl(e.url),(function(i){t._appendView(e,i)}));break;case"custom":e.open(this._getUrl(e.url),(function(i){t.winCreateOK(e,i),U.fire(w.openView,{sourceTarget:t,view:e,dom:i}),t._viewcreate_okcount++,t._viewcreate_okcount>=t._viewcreate_allcount&&t._startActivate(i)}),this);break;default:this._openWindow(e)}}_openWindow(e){const t=this._getUrl(e.url),i={type:2,content:[t,"no"],success:(i,s)=>{if(!this.isActivate)return void Q.close(s);e._layerIdx!==s&&(Q.close(e._layerIdx),e._layerIdx=s),e._layerOpening=!1,e._dom=i;const o=window[i.find("iframe")[0].name];var r;(o.map=this.map,o.mars3d=n,o.Cesium=n.Cesium,this.options.css&&M("#layui-layer"+e._layerIdx).css(this.options.css),e.windowOptions.hasOwnProperty("show")&&!e.windowOptions.show&&M(i).hide(),Q.setTop(i),this.winCreateOK(e,o),U.fire(w.openView,{sourceTarget:this,view:e,dom:i}),this._viewcreate_okcount++,this._viewcreate_okcount>=this._viewcreate_allcount&&this._startActivate(i),o&&o.initWidgetView)?(null!==(r=this.config)&&void 0!==r&&r.style&&M(o.document.body).addClass(this.config.style),o.initWidgetView(this)):n.Log.logError(t+"页面没有定义function initWidgetView(widget)方法，无法初始化widget页面!")}};e._layerIdx&&e._layerIdx>0&&(Q.close(e._layerIdx),e._layerIdx=-1),e._layerOpening=!0,e._layerIdx=Q.open(this._getWinOpt(e,i))}_openDivWindow(e){const t=this._getUrl(e.url);this.getHtml(t,(t=>{const i={type:1,content:t,success:(t,i)=>{this.isActivate?(e._layerIdx!==i&&(Q.close(e._layerIdx),e._layerIdx=i),e._layerOpening=!1,e._dom=t,e.windowOptions.hasOwnProperty("show")&&!e.windowOptions.show&&M(t).hide(),Q.setTop(t),this.winCreateOK(e,t),U.fire(w.openView,{sourceTarget:this,view:e,dom:t}),this._viewcreate_okcount++,this._viewcreate_okcount>=this._viewcreate_allcount&&this._startActivate(t)):Q.close(i)}};e._layerOpening=!0,e._layerIdx=Q.open(this._getWinOpt(e,i))}))}_getUrl(e){return(e=this.addCacheVersion(e)).startsWith("/")||e.startsWith(".")||e.startsWith("http")?e:this.path+e}_getWinOpt(e,t){const i={...A(),...e.windowOptions,...this.options.windowOptions};e.windowOptions=i;const n=this,s=this._getWinSize(i);let o=!1;i.noTitle||(o=this.options.name||" ",this.options.icon&&(o='<i class="'+this.options.icon+'" ></i>&nbsp;'+o));return{...{title:o,area:s.area,offset:s.offset,shade:0,maxmin:!1,beforeEnd:function(){n.beforeDisable()},end:function(){e._layerIdx=-1,e._dom=null,n.disableBase()},full:function(e){n.winFull(e)},min:function(e){n.winMin(e)},restore:function(e){n.winRestore(e)}},...i,...t}}_getWinSize(e){let t=this.bfb2Number(e.width,document.documentElement.clientWidth,e),i=this.bfb2Number(e.height,document.documentElement.clientHeight,e),n="";const s=e.position;if(s)if("string"==typeof s)n=s;else if("object"==typeof s){let o,r;if(s.hasOwnProperty("top")&&null!=s.top&&(o=this.bfb2Number(s.top,document.documentElement.clientHeight,e)),s.hasOwnProperty("bottom")&&null!=s.bottom){e._hasresize=!0;const t=this.bfb2Number(s.bottom,document.documentElement.clientHeight,e);null!=o?i=document.documentElement.clientHeight-o-t:o=document.documentElement.clientHeight-i-t}if(s.hasOwnProperty("left")&&null!=s.left&&(r=this.bfb2Number(s.left,document.documentElement.clientWidth,e)),s.hasOwnProperty("right")&&null!=s.right){e._hasresize=!0;const i=this.bfb2Number(s.right,document.documentElement.clientWidth,e);null!=r?t=document.documentElement.clientWidth-r-i:r=document.documentElement.clientWidth-t-i}null!=o&&void 0!==o||(o=(document.documentElement.clientHeight-i)/2),null!=r&&void 0!==r||(r=(document.documentElement.clientWidth-t)/2),n=[o+"px",r+"px"]}let o;return e.hasOwnProperty("minHeight")&&i<e.minHeight&&(e._hasresize=!0,i=e.minHeight),e.hasOwnProperty("maxHeight")&&i>e.maxHeight&&(e._hasresize=!0,i=e.maxHeight),e.hasOwnProperty("minWidth")&&t<e.minWidth&&(e._hasresize=!0,t=e.minWidth),e.hasOwnProperty("maxWidth")&&t>e.maxWidth&&(e._hasresize=!0,t=e.maxWidth),o=t&&i?[t+"px",i+"px"]:t+"px",{area:o,offset:n}}indexResize(){if(!this.isActivate)return;const e=this;this.eachView((function(t){if(!t._layerIdx||-1===t._layerIdx||!t.windowOptions||!t.windowOptions._hasresize)return;const i=t._dom.attr("maxmin");if("min"===i)return;if("full"===i)return void Q.full(t._layerIdx,t);const n=e._getWinSize(t.windowOptions),s={};Array.isArray(n.area)&&(n.area[0]&&(s.width=n.area[0]),n.area[1]&&(s.height=n.area[1])),Array.isArray(n.offset)&&(n.offset[1]&&(s.top=n.offset[0]),n.offset[1]&&(s.left=n.offset[1])),M(t._dom).attr("myTopLeft",!0),Q.style(t._layerIdx,s),"divwindow"===t.type&&Q.iframeAuto(t._layerIdx)}))}_appendView(e,t){e._dom=M(t).appendTo(e.parent||"body"),this.options.css&&M(e._dom).css(this.options.css),this.winCreateOK(e,t),this._viewcreate_okcount++,this._viewcreate_okcount>=this._viewcreate_allcount&&this._startActivate(t)}winCreateOK(e,t){}winFull(){}winMin(){}minView(){this.eachView((function(e){e._layerIdx&&Q.min(e._layerIdx,e)}))}restoreView(){this.eachView((function(e){e._layerIdx&&Q.restore(e._layerIdx)}))}fullView(){this.eachView((function(e){e._layerIdx&&Q.full(e._layerIdx,e)}))}winRestore(){}_startActivate(e){this.activate(e),U.fire(w.activated,{sourceTarget:this}),this.options.success&&(this.options.success(this),delete this.options.success),this.isActivate||this.disableBase()}beforeActivate(){}activate(){}disableBase(){this.isActivate&&(this.isActivate=!1,this.beforeDisable(),U.fire(w.beforeDisable,{sourceTarget:this}),this.eachView((function(e){return e._layerIdx&&e._layerIdx>0?(Q.close(e._layerIdx),e._layerOpening||(e._layerIdx=-1),!0):("append"===e.type&&e._dom&&(e._dom.remove(),e._dom=null),"custom"===e.type&&e.close&&e.close(),!1)})),this.disable(),this.options.autoReset&&this.resetConfig(),U.fire(w.disabled,{sourceTarget:this}))}beforeDisable(){}disable(){}bfb2Number(e,t,i){return"string"==typeof e&&-1!==e.indexOf("%")?(i._hasresize=!0,t*Number(e.replace("%",""))/100):e}addCacheVersion(e){if(!e)return e;const t=N();return t&&(-1===e.indexOf("?")?e+="?cache="+t:-1===e.indexOf("cache="+t)&&(e+="&cache="+t)),e}resetConfig(){if(this.options._firstConfigBak){const e=this.options._firstConfigBak;for(const t in e)"uri"!==t&&(this.options[t]=e[t])}}setViewShow(e,t){this.eachView((function(t){t._layerIdx&&t._layerIdx>0?e?M("#layui-layer"+t._layerIdx).show():M("#layui-layer"+t._layerIdx).hide():"append"===t.type&&t._dom&&(e?M(t._dom).show():M(t._dom).hide())}),t)}setViewCss(e,t){this.eachView((function(t){null!=t._layerIdx&&t._layerIdx>0?Q.style(t._layerIdx,e):"append"===t.type&&t._dom&&M(t._dom).css(e)}),t)}setTitle(e,t){this.eachView((function(t){t._dom&&t._dom.find(".layui-layer-title").html(e)}),t)}getHtml(e,t){M.ajax({url:e,type:"GET",dataType:"html",timeout:0,success:function(e){t(e)}})}},n.widget.WidgetEventType=w,n.widget.EventType=w,e.widget=R,Object.defineProperty(e,"__esModule",{value:!0})}));
