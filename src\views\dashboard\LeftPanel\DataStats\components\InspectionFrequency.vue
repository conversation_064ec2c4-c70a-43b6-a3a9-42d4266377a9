<template>
  <!-- 巡检频次 -->
  <ScreenCard title="巡检频次" :icon="'mdi:chart-bar'">
    <template #header-control>
      <ControlButton
        v-for="item in inspectionTypes"
        :key="item.value"
        :active="activeType === item.value"
        :label="item.label"
        @click="handleInspectionTypeChange(item.value)"
      />
    </template>
    <div class="inspection-content">
      <!-- 统一的数据展示布局 -->
      <div class="inspection-display">
        <div class="inspection-data">
          <div v-for="item in currentDisplayItems" :key="item.title">
            <DataDisplay
              :value="item.value"
              :title="item.title"
              :unit="item.unit"
              :icon="item.icon"
              :iconColor="item.iconColor"
            />
          </div>
        </div>
        <div class="inspection-chart">
          <BarChart
            :data="currentChartData.data"
            :title="currentChartData.title"
            :unit="currentUnit"
          />
        </div>
      </div>
    </div>
  </ScreenCard>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ScreenCard from '@/components/common/ScreenCard/ScreenCard.vue'
import ControlButton from '@/components/common/ControlButton/ControButton.vue'
import DataDisplay from '@/components/common/DataDisplay/DataDisplay.vue'
import BarChart from '@/components/common/BarChart/BarChart.vue'
import type { InspectionFrequencyUIData } from '@/types/ui/stats'
import type { DataDisplayItem } from '@/types/ui/common'

// ===== Props =====
interface Props {
  data: InspectionFrequencyUIData
}

const props = defineProps<Props>()

// ===== 本地状态 =====

// 巡检频次的按钮类型
type InspectionType = 'times' | 'mileage' | 'duration'

// 当前激活的类型（在组件内部管理）
const activeType = ref<InspectionType>('times')

// 定义按钮类型数组，用于循环渲染
const inspectionTypes: { value: InspectionType; label: string }[] = [
  { value: 'times', label: '次数' },
  { value: 'mileage', label: '里程' },
  { value: 'duration', label: '时长' },
]

// 类型配置
const typeConfig = {
  times: {
    dailyTitle: '日均巡检次数',
    totalTitle: '总巡检次数',
    unit: '次',
    chartTitle: '巡检次数趋势',
  },
  mileage: {
    dailyTitle: '日均巡检里程',
    totalTitle: '总巡检里程',
    unit: 'km',
    chartTitle: '巡检里程趋势',
  },
  duration: {
    dailyTitle: '日均巡检时长',
    totalTitle: '总巡检时长',
    unit: '小时',
    chartTitle: '巡检时长趋势',
  },
}

// ===== 计算属性 =====

// 当前显示的数据项
const currentDisplayItems = computed((): DataDisplayItem[] => {
  const currentData = props.data[`${activeType.value}Data`]
  const config = typeConfig[activeType.value]

  return [
    {
      title: config.dailyTitle,
      value: currentData.dailyAverage,
      unit: config.unit,
      icon: 'mdi:chart-line',
      iconColor: '#409EFF',
    },
    {
      title: config.totalTitle,
      value: currentData.totalCount,
      unit: config.unit,
      icon: 'mdi:chart-bar',
      iconColor: '#67C23A',
    },
  ]
})

// 当前图表数据
const currentChartData = computed(() => {
  const currentData = props.data[`${activeType.value}Data`]
  const config = typeConfig[activeType.value]

  return {
    title: config.chartTitle,
    data: currentData.chartData,
  }
})

// 当前单位
const currentUnit = computed(() => {
  return typeConfig[activeType.value].unit
})

// ===== 事件处理 =====

// 切换激活的按钮类型
const handleInspectionTypeChange = (type: InspectionType) => {
  console.log(`切换到${type}类型`)
  activeType.value = type
}
</script>

<style scoped lang="scss">
.inspection-content {
  .inspection-display {
    display: grid;
    grid-template-rows: auto 1fr;
    gap: 0.2rem;

    .inspection-data {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.5rem;
    }
    .inspection-chart {
      width: 100%;
      box-shadow: inset 0 -1rem 1rem -1rem $glow-color;
    }
  }
}
</style>
