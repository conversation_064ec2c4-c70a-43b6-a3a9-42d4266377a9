/**
 * 视频流相关类型定义
 */

// 视频流类型枚举
export type VideoStreamType = 'nest_inside' | 'nest_outside' | 'live_stream'

// 视频流类型配置
export const VIDEO_STREAM_TYPES = {
  nest_inside: { value: 'nest_inside', label: '巢内监控' },
  nest_outside: { value: 'nest_outside', label: '巢外监控' },
  live_stream: { value: 'live_stream', label: '实时图传' },
} as const

// 视频质量枚举
export type VideoQuality = 'HD' | 'SD' | 'LOW'

// 视频流信息
export interface VideoStreamInfo {
  droneId: string
  streamUrl?: string
  streamType: VideoStreamType
  isActive: boolean
  quality?: VideoQuality
}

// 视频卡片数据类型（组合基础信息 + 环境数据）
export interface VideoCardData {
  id: string
  droneId: string
  deviceName: string // 设备名称
  imageUrl?: string // 视频截图
  // 从飞控实时数据中提取的环境信息
  windSpeed?: string
  weather?: string
  humidity?: string
  // 视频流信息
  streamInfo?: VideoStreamInfo
}

// 多屏监控UI数据
export interface MultiScreenUIData {
  videoData: {
    [key in VideoStreamType]: VideoCardData[]
  }
}

// 监控卡片UI数据
export interface MonitorCardsUIData {
  cards: Array<{
    id: string
    title: string
    type: 'video' | 'map'
    content?: unknown
  }>
}
