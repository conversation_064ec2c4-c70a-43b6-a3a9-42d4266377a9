/**
 * 预警信息相关类型定义
 */

// 预警图片时间线类型 - 记录不同时间段的详情图
export interface WarningImageTimeline {
  id: string
  imageUrl: string
  captureTime: string // 拍摄时间
  description?: string // 图片描述
  imageType: '检测图' | '处理前' | '处理中' | '处理后' // 图片类型
}

// 预警处理时间线类型 - 记录处理过程的各个阶段
export interface WarningProcessTimeline {
  id: string
  title: string // 时间线标题
  status: '新发现' | '反馈处理' | '已办结' | '典型案例' | string // 添加string增强兼容性
  timestamp: string // 时间戳
  operator?: string // 操作人
  description?: string // 处理描述
  attachments?: string[] // 附件图片
}

// 预警详情完整数据类型（简化版）
export interface WarningDetailData {
  // 基础信息
  id: string
  type: string // 预警类型（后端返回数字字符串，前端映射为类型名称）
  status: '新发现' | '反馈处理' | '已办结' | string // 添加string增强兼容性

  // 检测时间和地点
  detectionTime: string // 检测时间
  inspectionRoute: string // 巡检航线

  // 单个地点信息
  location: {
    name: string // 地点名称
    address: string // 详细地址
    coordinates: {
      longitude: number // 经度
      latitude: number // 纬度
      altitude?: number // 海拔高度
    }
  }

  // 图片时间线（该地点的不同时间段照片）
  imageTimeline: WarningImageTimeline[]

  // 处理时间线
  processTimeline: WarningProcessTimeline[]

  // 其他信息
  isTypical?: boolean // 是否典型案例
  completedAt?: string // 完成时间
}

// 预警列表项数据类型 (保持向后兼容)
export interface WarningItemData {
  id: string
  imageUrl: string
  type: string
  status: '新发现' | '反馈处理' | '已办结' | string // 预警状态，添加string增强兼容性
  detectionTime: string // "YYYY-MM-DD HH:mm:ss"
  inspectionRoute: string // "【全覆盖】常山01-辉埠工业园"
  location: string // "浙江省衢州市常山县狮子口水库附近"
  latitude?: number
  longitude?: number
  isTypical?: boolean // 是否为典型案例
  completedAt?: string // 完成时间
}
