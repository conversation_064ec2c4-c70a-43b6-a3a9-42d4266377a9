!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("mars3d")):"function"==typeof define&&define.amd?define(["exports","mars3d"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self)["mars3d-supermap"]={},e.mars3d)}(this,(function(e,t){"use strict";function r(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,t}var o=r(t);const s=o.Cesium,i=o.layer.BaseLayer;class a extends i{get layer(){return this._layerArr}get s3mOptions(){return this.options.s3mOptions}set s3mOptions(e){for(const t in e){let r=e[t];this.options.s3mOptions[t]=r,"transparentBackColor"===t?r=s.Color.fromCssColorString(r):"transparentBackColorTolerance"===t&&(r=Number(r));for(let e=0;e<this._layerArr.length;e++){const o=this._layerArr[e];null!=o&&(o[t]=r)}}}_showHook(e){this.eachLayer((t=>{t.visible=e,t.show=e}),this)}_mountedHook(){if(!this._map.scene.open)throw new Error("请引入 超图版本Cesium库 或 超图S3M插件 ");const e=this._map.getCameraView();let t;t=this.options.layername?this._map.scene.addS3MTilesLayerByScp(this.options.url,{name:this.options.layername,autoSetView:this.options.flyTo,cullEnabled:this.options.cullEnabled}):this._map.scene.open(this.options.url,this.options.sceneName,{autoSetView:this.options.flyTo}),t.then((t=>{Array.isArray(t)?this._layerArr=t:this._layerArr=[t];for(let e=0;e<this._layerArr.length;e++){const t=this._layerArr[e];if(t)try{this._initModelItem(t)}catch(e){o.Log.logError("s3m图层初始化出错",e)}}this._showHook(this.show),this.options.flyTo?this.flyToByAnimationEnd():!1===this.options.flyTo&&this._map.setCameraView(e,{duration:0}),this._readyPromise.resolve(this),this.fire(o.EventType.load,{layers:this._layerArr})}),(e=>{var t;null!==(t=this._readyPromise)&&void 0!==t&&t.reject&&this._readyPromise.reject(e)}))}_initModelItem(e){var t;if(this.options.s3mOptions)for(const t in this.options.s3mOptions){const r=this.options.s3mOptions[t];e[t]="transparentBackColor"===t?s.Color.fromCssColorString(r):"transparentBackColorTolerance"===t?Number(r):r}this.options.highlight&&(e.selectedColor=o.Util.getColorByStyle(this.options.highlight)),null!==(t=this.options)&&void 0!==t&&null!==(t=t.position)&&void 0!==t&&t.alt&&(e.style3D.altitudeMode=s.HeightReference.NONE,e.style3D.bottomAltitude=this.options.position.alt,e.refresh&&e.refresh())}_addedHook(){this._showHook(this.show)}_removedHook(){this._showHook(!1)}eachLayer(e,t){if(this._layerArr)return this._layerArr.forEach((r=>{e.call(t,r)})),this}setOpacity(e){this.eachLayer((t=>{t.style3D.fillForeColor.alpha=e}),this)}flyTo(e={}){return this.options.center?this._map.setCameraView(this.options.center,e):this.options.extent?this._map.flyToExtent(this.options.extent,e):void 0}}o.layer.S3MLayer=a,o.LayerUtil.register("supermap_s3m",a);const n=o.Cesium,l=o.layer.BaseTileLayer;class h extends l{async _createImageryProvider(e){return await p(e)}_addedHook(){super._addedHook(),n.defined(this.options.transparentBackColor)&&(this._imageryLayer.transparentBackColor=o.Util.getCesiumColor(this.options.transparentBackColor),this._imageryLayer.transparentBackColorTolerance=this.options.transparentBackColorTolerance)}}async function p(e){return(e=o.LayerUtil.converOptions(e)).url instanceof n.Resource&&(e.url=e.url.url),n.defined(e.transparentBackColor)&&(delete e.transparentBackColor,delete e.transparentBackColorTolerance),new n.SuperMapImageryProvider(e)}h.createImageryProvider=p,o.layer.SmImgLayer=h;const c="supermap_img";o.LayerUtil.register(c,h),o.LayerUtil.registerImageryProvider(c,p);const y=o.Cesium,m=o.layer.BaseLayer;class d extends m{get layer(){return this._mvtLayer}_mountedHook(){this._mvtLayer=this._map.scene.addVectorTilesMap({viewer:this._map.viewer,canvasWidth:512,...this.options});const e=this._map.scene,t=new y.ScreenSpaceEventHandler(e.canvas);t.setInputAction((t=>{if(!this.show)return;const r=o.PointUtil.getCurrentMousePosition(e,t.position);this._mvtLayer.queryRenderedFeatures([r],{}).reduce(((e,s)=>{const i=s.feature.properties;if(!i)return;const a=o.Util.getPopupForConfig(this.options,i),n={data:i,event:t};this._map.openPopup(r,a,n)}))}),y.ScreenSpaceEventType.LEFT_CLICK),this.handler=t}_addedHook(){this._mvtLayer.show=!0}_removedHook(){this._mvtLayer&&(this._mvtLayer.show=!1)}setOpacity(e){this._mvtLayer&&(this._mvtLayer.alpha=parseFloat(e))}flyTo(e={}){return this.options.center?this._map.setCameraView(this.options.center,e):this.options.extent?this._map.flyToExtent(this.options.extent,e):this._mvtLayer?this._map.camera.flyTo({...e,destination:this._mvtLayer.rectangle}):Promise.resolve(!1)}}o.layer.SmMvtLayer=d,o.LayerUtil.register("supermap_mvt",d),e.S3MLayer=a,e.SmImgLayer=h,e.SmMvtLayer=d,Object.defineProperty(e,"__esModule",{value:!0})}));
