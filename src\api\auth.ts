/**
 * 认证API接口
 */
import { post, get, put, del } from '@/utils/request'
import type { LoginRequest, LoginResponse } from '@/types/auth'

/**
 * 认证API
 */
export const authApi = {
  // 登录
  login: (data: LoginRequest): Promise<LoginResponse> => {
    return post('/user/loginWithForce/', data)
  },

  // TODO: 待后端接口确认后补充其他认证相关接口
  // logout: (): Promise<any> => {
  //   return post('/user/logout')
  // },

  // refreshToken: (refreshToken: string): Promise<any> => {
  //   return post('/user/refresh', { refreshToken })
  // },

  // getUserInfo: (): Promise<UserInfo> => {
  //   return get('/user/info')
  // },
}
