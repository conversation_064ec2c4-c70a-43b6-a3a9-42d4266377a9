/**
 * 任务状态管理 Store
 * 管理任务列表、任务创建流程、任务执行状态等任务相关状态
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { TaskDetail, CreateTaskRequest, TaskStatus, TaskDroneEstimate } from '@/types/ui'
import { taskApi } from '@/api/task'

// 任务视图模式枚举
export type TaskViewMode = 'list' | 'create' | 'detail'

export const useTaskStore = defineStore('task', () => {
  // ===== 状态定义 =====

  // 任务列表
  const tasks = ref<TaskDetail[]>([])

  // 当前视图模式
  const currentViewMode = ref<TaskViewMode>('list')

  // 当前选中的任务ID
  const selectedTaskId = ref<string | null>(null)

  // 新建任务表单数据（模拟从地图拾取的数据）
  const createTaskForm = ref<Partial<CreateTaskRequest>>({
    // 地图拾取的地块信息
    name: '浙江省衢州市柯城区荷花街道巡检区域',
    shape: '面状',
    perimeter: 1594.98,
    area: 0.156134974163356,
    centerLongitude: 119.1565,
    centerLatitude: 29.1296,
    locationDetail: '浙江省衢州市柯城区荷花街道荷花西路128号周边区域',

    // 地图选择的最近无人机ID
    selectedDroneId: 'drone-001',

    // 用户可编辑的默认值
    purpose: '拍摄',
    urgency: '不紧急',
    remark: '',
  })

  // 任务创建流程状态
  const isCreatingTask = ref(false)

  // 任务无人机预计信息（根据地图计算得出）
  const taskDroneEstimate = computed((): TaskDroneEstimate | null => {
    const droneId = createTaskForm.value.selectedDroneId
    if (!droneId) return null

    // 模拟根据地图区域和选中无人机计算得出的预计信息
    return {
      droneId: droneId,
      droneName: '塔石测试机', // 这里可以从droneStore获取真实名称
      status: '在线',
      batteryLevel: 90,
      nestDistance: 2.5, // 机巢距离 2.5km
      estimatedTime: 1946, // 预计耗时 1946分钟
      estimatedMileage: 17.335, // 预计里程 17.335km
      flightSpeed: 13.9, // 飞行速度 13.9米/秒
      photoCount: 11, // 预计照片数量 11张
    }
  })

  // 任务列表加载状态
  const isLoadingTasks = ref(false)

  // 任务筛选条件
  const taskFilters = ref({
    status: null as TaskStatus | null,
    purpose: null as string | null,
    dateRange: null as [Date, Date] | null,
    searchKeyword: '',
  })

  // 分页信息
  const pagination = ref({
    currentPage: 1,
    pageSize: 10,
    total: 0,
  })

  // ===== 计算属性 =====

  // 当前选中的任务详情
  const selectedTask = computed(() => {
    if (!selectedTaskId.value) return null
    return tasks.value.find((task) => task.id === selectedTaskId.value) || null
  })

  // 过滤后的任务列表
  const filteredTasks = computed(() => {
    let result = tasks.value

    // 状态筛选
    if (taskFilters.value.status) {
      result = result.filter((task) => task.status === taskFilters.value.status)
    }

    // 用途筛选
    if (taskFilters.value.purpose) {
      result = result.filter((task) => task.purpose === taskFilters.value.purpose)
    }

    // 关键词搜索
    if (taskFilters.value.searchKeyword) {
      const keyword = taskFilters.value.searchKeyword.toLowerCase()
      result = result.filter(
        (task) =>
          task.name.toLowerCase().includes(keyword) ||
          task.locationDetail.toLowerCase().includes(keyword),
      )
    }

    // 日期范围筛选
    if (taskFilters.value.dateRange) {
      const [startDate, endDate] = taskFilters.value.dateRange
      result = result.filter((task) => {
        const taskDate = new Date(task.createdAt)
        return taskDate >= startDate && taskDate <= endDate
      })
    }

    return result
  })

  // 任务统计信息
  const taskStats = computed(() => {
    const stats = {
      total: tasks.value.length,
      pending: 0,
      executing: 0,
      completed: 0,
      cancelled: 0,
      error: 0,
      paused: 0,
    }

    tasks.value.forEach((task) => {
      switch (task.status) {
        case '待执行':
          stats.pending++
          break
        case '执行中':
          stats.executing++
          break
        case '已完成':
          stats.completed++
          break
        case '已取消':
          stats.cancelled++
          break
        case '异常':
          stats.error++
          break
        case '暂停中':
          stats.paused++
          break
      }
    })

    return stats
  })

  // ===== Actions =====

  /**
   * 切换视图模式
   */
  const setViewMode = (mode: TaskViewMode) => {
    currentViewMode.value = mode

    // 切换到列表视图时清除选中的任务
    if (mode === 'list') {
      selectedTaskId.value = null
    }
  }

  /**
   * 开始创建新任务
   */
  const startCreateTask = () => {
    // 保持默认的表单数据（从地图拾取的数据）
    // 不重置表单数据，保留模拟的地图拾取数据
    // 切换到创建视图
    setViewMode('create')
  }

  /**
   * 取消创建任务
   */
  const cancelCreateTask = () => {
    createTaskForm.value = {}
    setViewMode('list')
  }

  /**
   * 选择任务
   */
  const selectTask = (taskId: string) => {
    selectedTaskId.value = taskId
    setViewMode('detail')
  }

  /**
   * 更新创建任务表单数据
   */
  const updateCreateTaskForm = (data: Partial<CreateTaskRequest>) => {
    createTaskForm.value = { ...createTaskForm.value, ...data }
  }

  /**
   * 获取任务列表
   */
  const fetchTasks = async () => {
    try {
      isLoadingTasks.value = true

      const result = await taskApi.getTaskList({
        page: pagination.value.currentPage,
        pageSize: pagination.value.pageSize,
        // 只传递日期范围到后端，类型和状态筛选在前端进行
        dateRange: taskFilters.value.dateRange,
        searchKeyword: taskFilters.value.searchKeyword,
      })

      tasks.value = result.data
      pagination.value.total = result.total
    } catch (err) {
      console.error('获取任务列表失败:', err)
    } finally {
      isLoadingTasks.value = false
    }
  }

  /**
   * 添加任务到列表
   */
  const addTask = (task: TaskDetail) => {
    tasks.value.unshift(task)
  }

  /**
   * 更新任务
   */
  const updateTask = (taskId: string, updates: Partial<TaskDetail>) => {
    const index = tasks.value.findIndex((task) => task.id === taskId)
    if (index !== -1) {
      tasks.value[index] = { ...tasks.value[index], ...updates }
    }
  }

  /**
   * 删除任务
   */
  const removeTask = (taskId: string) => {
    const index = tasks.value.findIndex((task) => task.id === taskId)
    if (index !== -1) {
      tasks.value.splice(index, 1)
    }
  }

  /**
   * 更新筛选条件
   */
  const updateFilters = (filters: Partial<typeof taskFilters.value>) => {
    taskFilters.value = { ...taskFilters.value, ...filters }
  }

  /**
   * 重置筛选条件
   */
  const resetFilters = () => {
    taskFilters.value = {
      status: null,
      purpose: null,
      dateRange: null,
      searchKeyword: '',
    }
  }

  /**
   * 更新分页信息
   */
  const updatePagination = (paginationData: Partial<typeof pagination.value>) => {
    pagination.value = { ...pagination.value, ...paginationData }
  }

  return {
    // 状态
    tasks,
    currentViewMode,
    selectedTaskId,
    createTaskForm,
    isCreatingTask,
    isLoadingTasks,
    taskFilters,
    pagination,

    // 计算属性
    selectedTask,
    filteredTasks,
    taskStats,
    taskDroneEstimate,

    // 方法
    setViewMode,
    startCreateTask,
    cancelCreateTask,
    selectTask,
    updateCreateTaskForm,
    fetchTasks,
    addTask,
    updateTask,
    removeTask,
    updateFilters,
    resetFilters,
    updatePagination,
  }
})
