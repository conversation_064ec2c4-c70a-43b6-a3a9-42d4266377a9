<script setup lang="ts">
import { computed } from 'vue'
import type { FlightRealTimeData } from '@/types/ui'

// 组件属性 - 从父组件接收数据
interface Props {
  data: FlightRealTimeData['mission']
}

const props = defineProps<Props>()

// 任务数据 - 直接使用props传入的数据
const missionData = computed(() => {
  return props.data
})

// 计算任务进度颜色
const getProgressColor = (progress: number) => {
  if (progress < 30) return '#F44336' // 红色 - 低进度
  if (progress < 70) return '#FF9800' // 橙色 - 中等进度
  return '#4CAF50' // 绿色 - 高进度
}

// 计算数据同步状态显示
const dataSyncDisplay = computed(() => {
  return missionData.value.dataSync === '正常'
    ? { icon: 'mdi:sync', color: '#4CAF50' }
    : { icon: 'mdi:sync-alert', color: '#F44336' }
})
</script>

<template>
  <ScreenCard title="飞行任务" icon="mdi:airplane-takeoff">
    <template #header-control>
      <!-- 飞行距离和高度 -->
      <div class="flight-indicators">
        <div class="flight-item">
          <UIcon name="mdi:map-marker-distance" size="0.8rem" color="#00FFFE" />
          <span class="flight-value">{{ missionData.distance }}</span>
        </div>
        <div class="flight-item">
          <UIcon name="mdi:altimeter" size="0.8rem" color="#00FFFE" />
          <span class="flight-value">{{ missionData.altitude }}</span>
        </div>
      </div>
    </template>

    <div class="flight-mission-content">
      <!-- 第一行：地址/耗时/进度横向进度条 -->
      <div class="mission-row">
        <div class="progress-info">
          <div class="progress-header">
            <span class="progress-label">任务进度</span>
            <span
              class="progress-percent"
              :style="{ color: getProgressColor(missionData.progress) }"
            >
              {{ missionData.progress }}%
            </span>
          </div>
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{
                width: `${missionData.progress}%`,
                background: `linear-gradient(90deg, ${getProgressColor(missionData.progress)} 0%, rgba(${getProgressColor(missionData.progress)}, 0.7) 100%)`,
              }"
            ></div>
          </div>
          <div class="progress-details">
            <span class="detail-item">地址: {{ missionData.location }}</span>
            <span class="detail-item">耗时: {{ missionData.duration }}</span>
          </div>
        </div>
      </div>

      <!-- 第二行：速度和数据同步 - 使用DataDisplay -->
      <div class="mission-row data-display-row">
        <DataDisplay
          title="飞行速度"
          :value="missionData.speed"
          unit="m/s"
          icon="mdi:speedometer"
          iconColor="#00FFFE"
        />
        <DataDisplay
          title="数据同步"
          :value="missionData.dataSync"
          unit=""
          :icon="dataSyncDisplay.icon"
          :iconColor="dataSyncDisplay.color"
        />
      </div>

      <!-- 第三行：航点数量和预计拍照 - 使用DataDisplay -->
      <div class="mission-row data-display-row">
        <DataDisplay
          title="航点数量"
          :value="missionData.waypointCount"
          unit="个"
          icon="mdi:map-marker-multiple"
          iconColor="#00FFFE"
        />
        <DataDisplay
          title="预计拍照"
          :value="missionData.estimatedPhotos"
          unit="张"
          icon="mdi:camera"
          iconColor="#00FFFE"
        />
      </div>
    </div>
  </ScreenCard>
</template>

<style scoped lang="scss">
.flight-indicators {
  display: flex;
  gap: 0.5rem;

  .flight-item {
    display: flex;
    align-items: center;
    gap: 0.2rem;

    .flight-value {
      font-size: $font-size-panel-caption;
      color: $text-default;
      font-weight: bold;
    }
  }
}

.flight-mission-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.5rem 0;

  .mission-row {
    display: flex;
    gap: 0.5rem;

    &.data-display-row {
      justify-content: space-between;
    }

    .progress-info {
      flex: 1;
      padding: 0.5rem;
      background: rgba($bg-card, 0.3);
      border-radius: $border-radius-small;
      border: 1px solid rgba($border-color, 0.3);

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.3rem;

        .progress-label {
          font-size: $font-size-panel-caption;
          color: $text-secondary;
        }

        .progress-percent {
          font-size: $font-size-panel-caption;
          color: $text-active;
          font-weight: bold;
        }
      }

      .progress-bar {
        width: 100%;
        height: 0.3rem;
        background: rgba($border-color, 0.3);
        border-radius: 0.15rem;
        overflow: hidden;
        margin-bottom: 0.3rem;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, $primary-color 0%, rgba($primary-color, 0.7) 100%);
          transition: width 0.3s ease;
        }
      }

      .progress-details {
        display: flex;
        justify-content: space-between;
        gap: 0.5rem;

        .detail-item {
          font-size: $font-size-panel-micro;
          color: $text-secondary;
        }
      }
    }

    .mission-param {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 0.3rem;
      padding: 0.5rem;
      background: rgba($bg-card, 0.3);
      border-radius: $border-radius-small;
      border: 1px solid rgba($border-color, 0.3);

      .param-info {
        display: flex;
        flex-direction: column;
        gap: 0.2rem;

        .param-label {
          font-size: $font-size-panel-caption;
          color: $text-secondary;
        }

        .param-value {
          font-size: $font-size-panel-label;
          color: $text-default;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
