<template>
  <div class="data-display">
    <div class="data-container">
      <div class="data-icon">
        <UIcon :name="props.icon" width="1rem" height="1rem" />
      </div>
      <div class="data-content">
        <div class="data-title">{{ title }}</div>
        <div class="data-value">{{ formattedValue }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import UIcon from '@/components/common/UIcon/UIcon.vue'
interface Props {
  title: string
  value: number | string
  unit: string
  icon: string
}

const props = defineProps<Props>()

// 格式化数值，添加单位
const formattedValue = computed(() => {
  // 如果是数字，格式化显示
  if (typeof props.value === 'number') {
    // 对于大数字，使用简化显示
    if (props.value >= 10000) {
      return (props.value / 10000).toFixed(1) + ' 万' + props.unit
    }
    return props.value + props.unit
  }
  return props.value + props.unit
})
</script>

<style scoped lang="scss">
.data-display {
  display: flex;
  justify-content: center;
  padding: 0.1rem;
  width: 100%;

  .data-container {
    display: flex;
    align-items: center;
    gap: 0.4rem;

    .data-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 1.5rem;
      min-height: 1.5rem;
      color: #00fffe;
      opacity: 0.9;
      background-color: rgba(0, 255, 254, 0.08);
      border-radius: 2px;
      padding: 0.2rem;
      flex-shrink: 0;
    }

    .data-content {
      display: flex;
      flex-direction: column;
      min-height: 1.5rem;
      justify-content: space-between;
      overflow: hidden;
      gap: 0.2rem;

      .data-value {
        font-size: 0.9rem;
        font-weight: bold;
        color: #00fffe;
        line-height: 1;
        letter-spacing: 0.02rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .data-title {
        font-size: 0.5rem;
        color: rgba(255, 255, 255, 0.7);
        line-height: 1;
        letter-spacing: 0.01rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
