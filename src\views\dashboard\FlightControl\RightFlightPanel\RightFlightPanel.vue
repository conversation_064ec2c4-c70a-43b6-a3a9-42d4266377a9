<script setup lang="ts">
import { computed } from 'vue'
import { useDroneStore } from '@/stores/droneStore'
import DirectionSpeedPanel from './DirectionSpeedPanel.vue'
import FlightParametersPanel from './FlightParametersPanel.vue'

// 使用Store
const droneStore = useDroneStore()

// 为BasePanel创建一个单一的tab
const tabs = [{ id: 'flight-data', name: '新型号无人机' }]

const activeTab = ref('flight-data')

const handleTabChange = (tabId: string) => {
  activeTab.value = tabId
}

// 从Store获取飞控实时数据
const flightData = computed(() => {
  return droneStore.flightRealTimeData
})
</script>

<template>
  <BasePannel :tabs="tabs" :defaultActiveTab="activeTab" @activeTabChange="handleTabChange">
    <template #content>
      <div class="right-flight-panel-content">
        <!-- 使用CSS Grid布局，3个组件排列 -->
        <div v-if="flightData" class="flight-data-grid">
          <!-- 方向速度面板 -->
          <DirectionSpeedPanel :data="flightData.navigation" />

          <!-- 飞行参数面板 -->
          <FlightParametersPanel :data="flightData.parameters" />
        </div>
        <div v-else class="loading-state">
          <span>正在加载飞控数据...</span>
        </div>
      </div>
    </template>
  </BasePannel>
</template>

<style scoped lang="scss">
.right-flight-panel-content {
  height: 100%;
  padding: 0.5rem;

  .flight-data-grid {
    height: 100%;
    display: grid;
    grid-template-rows: auto, 1fr;
    gap: 0.5rem;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
</style>
