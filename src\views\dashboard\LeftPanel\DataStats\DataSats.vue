<template>
  <div class="data-stats-container">
    <div class="date-picker">
      <DateRangePicker v-model:dateRange="timeRange" @dateRangeChange="handleDateRangeChange" />
    </div>
    <div class="data-content">
      <!-- 巡检频次 -->
      <InspectionFrequency v-if="inspectionFrequencyData" :data="inspectionFrequencyData" />

      <!-- 事件办结 -->
      <EventCompletion v-if="eventCompletionData" :data="eventCompletionData" />

      <!-- 降本增效 -->
      <CostReduction v-if="costReductionData" :data="costReductionData" />

      <!-- 数据成果 -->
      <DataAchievement v-if="dataAchievementData" :data="dataAchievementData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import InspectionFrequency from './components/InspectionFrequency.vue'
import EventCompletion from './components/EventCompletion.vue'
import CostReduction from './components/CostReduction.vue'
import DataAchievement from './components/DataAchievement.vue'
import DateRangePicker from '../../../../components/common/DateRangePicker/DateRangePicker.vue'
import { useDataStatsStore } from '@/stores/dataStatsStore'

// 使用数据统计Store
const dataStatsStore = useDataStatsStore()

// 时间范围 - 从Store获取初始值
const timeRange = ref<[Date, Date]>([
  dataStatsStore.currentDateRange.startDate,
  dataStatsStore.currentDateRange.endDate,
])

// 使用computed保持响应式连接
const inspectionFrequencyData = computed(() => dataStatsStore.inspectionFrequencyData)
const eventCompletionData = computed(() => dataStatsStore.eventCompletionData)
const costReductionData = computed(() => dataStatsStore.costReductionData)
const dataAchievementData = computed(() => dataStatsStore.dataAchievementData)

// ===== 事件处理 =====

// 处理日期范围变化
const handleDateRangeChange = ({ startDate, endDate }: { startDate: Date; endDate: Date }) => {
  console.log('时间范围变化:', startDate.toLocaleDateString(), endDate.toLocaleDateString())

  // 更新Store中的时间范围，触发数据重新获取
  dataStatsStore.setDateRange({ startDate, endDate })

  // 同步更新本地时间范围
  timeRange.value = [startDate, endDate]
}

// 组件挂载时获取初始数据
onMounted(() => {
  dataStatsStore.fetchAllData()
})
</script>

<style scoped lang="scss">
.data-stats-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  align-items: center;
  padding: 0.5rem;
  row-gap: 0.5rem;

  .data-content {
    height: 100%;
    display: grid;
    grid-template-rows: repeat(4, auto);
    gap: 0.2rem;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
</style>
