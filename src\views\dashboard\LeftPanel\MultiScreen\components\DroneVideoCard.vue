<template>
  <div class="drone-video-card">
    <div class="video-container">
      <!-- 视频内容区域 -->
      <slot name="video">
        <div class="video-placeholder">
          <img v-if="data.imageUrl" :src="data.imageUrl" alt="视频画面" />
          <div v-else class="no-video">暂无视频信号</div>
        </div>
      </slot>

      <!-- 底部信息覆盖层 -->
      <div class="bottom-overlay">
        <!-- 左下角信息卡片 -->
        <div class="info-card">
          <div class="info-col">
            <div v-for="item in infoCardItems" :key="item.id" class="info-item">
              <UIcon :name="item.icon" color="#00fffe" size="0.8rem" />
              <span>{{ item.text }}</span>
            </div>
          </div>
        </div>

        <!-- 右下角监控卡片控制按钮 -->
        <div class="monitor-control-btn" @click.stop="handleMonitorControlClick">
          <UIcon name="mdi:monitor-multiple" color="#00fffe" size="0.7rem" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { VideoCardData } from '@/types/ui'

// 组件属性
interface Props {
  data: VideoCardData
}

// 组件事件
interface Emits {
  (e: 'monitor-control-click', droneId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 信息项显示类型
interface InfoItemDisplay {
  id: string
  icon: string
  text: string
}

// 计算属性，用于生成信息卡片项的数组
const infoCardItems = computed<InfoItemDisplay[]>(() => {
  const items: InfoItemDisplay[] = []

  // 风速信息
  if (props.data.windSpeed) {
    items.push({
      id: 'windSpeed',
      icon: 'mdi:weather-windy',
      text: props.data.windSpeed,
    })
  }

  // 天气信息
  if (props.data.weather) {
    items.push({
      id: 'weather',
      icon: 'mdi:weather-sunny',
      text: props.data.weather,
    })
  }

  // 湿度信息
  if (props.data.humidity) {
    items.push({
      id: 'humidity',
      icon: 'mdi:water-percent',
      text: props.data.humidity,
    })
  }

  // 设备名称
  if (props.data.deviceName) {
    items.push({
      id: 'deviceName',
      icon: 'mdi:map-marker-outline',
      text: props.data.deviceName,
    })
  }

  return items
})

// 处理监控卡片控制按钮点击事件
const handleMonitorControlClick = () => {
  emit('monitor-control-click', props.data.droneId)
}
</script>

<style scoped lang="scss">
.drone-video-card {
  width: 100%;
  height: 100%;
  min-height: 10rem;
  max-height: 12rem;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: inset 0 0 1rem rgba(0, 255, 254, 0.7);
  border: 1px solid transparent;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease,
    border-color 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: inset 0 0 1rem rgba(0, 255, 254, 0.9);
    border: 1px solid rgba(0, 255, 254, 0.5);
  }

  .video-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .bottom-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      padding: 0.5rem;
      box-sizing: border-box;
      pointer-events: none;

      & > * {
        pointer-events: auto;
      }
    }

    .video-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .no-video {
        color: #999;
        font-size: 0.8rem;
      }
    }

    .info-card {
      background: rgba(0, 0, 0, 0.7);
      border-radius: 4px;
      padding: 0.3rem 0.5rem;
      backdrop-filter: blur(4px);

      .info-col {
        display: flex;
        flex-direction: column;
        gap: 0.2rem;

        .info-item {
          display: flex;
          align-items: center;
          gap: 0.3rem;
          font-size: 0.7rem;
          color: #fff;
          white-space: nowrap;
        }
      }
    }

    .monitor-control-btn {
      background: rgba(0, 255, 254, 0.2);
      border: 1px solid rgba(0, 255, 254, 0.5);
      border-radius: 50%;
      padding: 0.4rem;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(4px);

      &:hover {
        background: rgba(0, 255, 254, 0.3);
        border-color: rgba(0, 255, 254, 0.8);
        transform: scale(1.1);
      }
    }
  }
}
</style>
