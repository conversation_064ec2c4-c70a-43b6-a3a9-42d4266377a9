/**
 * 认证状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { AuthState, UserInfo, LoginRequest, LoginResponse } from '@/types/auth'
// import { authApi } from '@/api/auth' // 开发阶段暂时注释

export const useAuthStore = defineStore('auth', () => {
  // 认证状态
  const authState = ref<AuthState>({
    isAuthenticated: false,
    token: null,
    refreshToken: null,
    userInfo: null,
    loginTime: null,
    expiresIn: null,
  })

  // Getters
  const isAuthenticated = computed(() => authState.value.isAuthenticated)
  const token = computed(() => authState.value.token)
  const userInfo = computed(() => authState.value.userInfo)
  const isTokenExpired = computed(() => {
    if (!authState.value.loginTime || !authState.value.expiresIn) {
      return true
    }
    const now = Date.now()
    const expireTime = authState.value.loginTime + authState.value.expiresIn * 1000
    return now >= expireTime
  })

  // Actions

  /**
   * 登录方法
   */
  const login = async (loginData: LoginRequest): Promise<void> => {
    try {
      // 开发阶段：模拟登录响应数据
      const response: LoginResponse = {
        success: true,
        code: 0,
        info: '用户登录成功！',
        userid: 12,
        username: loginData.username, // 使用输入的用户名
        token: 'mock_token_' + Date.now(), // 生成模拟token
      }

      // 真实请求代码（开发阶段暂时注释）
      // const response: LoginResponse = await authApi.login(loginData)

      // 检查登录是否成功
      if (response.success && response.code === 0) {
        const userInfo: UserInfo = {
          userid: response.userid,
          username: response.username,
        }

        // 更新认证状态
        authState.value = {
          isAuthenticated: true,
          token: response.token,
          refreshToken: null, // 当前后端不返回 refreshToken
          userInfo,
          loginTime: Date.now(),
          expiresIn: null, // 当前后端不返回过期时间
        }

        // 持久化存储
        localStorage.setItem('token', response.token)
        localStorage.setItem('userInfo', JSON.stringify(userInfo))
        localStorage.setItem('loginTime', Date.now().toString())

        console.log('登录成功，用户信息:', userInfo)
      } else {
        throw new Error(response.info || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  /**
   * 设置认证信息
   */
  const setAuth = (authData: Partial<AuthState>) => {
    Object.assign(authState.value, authData)

    // 持久化存储
    if (authData.token) {
      localStorage.setItem('token', authData.token)
    }
    if (authData.userInfo) {
      localStorage.setItem('userInfo', JSON.stringify(authData.userInfo))
    }
    if (authData.loginTime) {
      localStorage.setItem('loginTime', authData.loginTime.toString())
    }
  }

  /**
   * 清除认证信息
   */
  const clearAuth = () => {
    authState.value = {
      isAuthenticated: false,
      token: null,
      refreshToken: null,
      userInfo: null,
      loginTime: null,
      expiresIn: null,
    }

    // 清除持久化存储
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('loginTime')
  }

  /**
   * 从本地存储恢复认证状态
   */
  const restoreAuth = () => {
    try {
      const token = localStorage.getItem('token')
      const userInfoStr = localStorage.getItem('userInfo')
      const loginTimeStr = localStorage.getItem('loginTime')

      if (token && userInfoStr) {
        const userInfo: UserInfo = JSON.parse(userInfoStr)
        const loginTime = loginTimeStr ? parseInt(loginTimeStr) : Date.now()

        authState.value = {
          isAuthenticated: true,
          token,
          refreshToken: null,
          userInfo,
          loginTime,
          expiresIn: null,
        }

        console.log('认证状态已恢复:', userInfo.username)
      }
    } catch (error) {
      console.error('恢复认证状态失败:', error)
      clearAuth()
    }
  }

  /**
   * 刷新token
   * TODO: 待后端接口确认后实现
   */
  const refreshToken = async () => {
    // try {
    //   if (!authState.value.refreshToken) {
    //     throw new Error('No refresh token available')
    //   }
    //
    //   const response = await authApi.refreshToken(authState.value.refreshToken)
    //
    //   setAuth({
    //     token: response.token,
    //     refreshToken: response.refreshToken,
    //     loginTime: Date.now(),
    //     expiresIn: response.expiresIn
    //   })
    //
    //   return response.token
    // } catch (error) {
    //   console.error('刷新token失败:', error)
    //   clearAuth()
    //   throw error
    // }
  }

  /**
   * 登出
   */
  const logout = async () => {
    try {
      // TODO: 待后端提供登出接口后调用
      // await authApi.logout()

      // 清除本地状态
      clearAuth()

      console.log('用户已登出')
    } catch (error) {
      console.error('登出失败:', error)
      // 即使接口失败也要清除本地状态
      clearAuth()
    }
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = (userInfo: Partial<UserInfo>) => {
    if (authState.value.userInfo) {
      Object.assign(authState.value.userInfo, userInfo)

      // 更新持久化存储
      localStorage.setItem('userInfo', JSON.stringify(authState.value.userInfo))
    }
  }

  return {
    // State
    authState: readonly(authState),

    // Getters
    isAuthenticated,
    token,
    userInfo,
    isTokenExpired,

    // Actions
    login,
    setAuth,
    clearAuth,
    restoreAuth,
    refreshToken,
    logout,
    updateUserInfo,
  }
})
