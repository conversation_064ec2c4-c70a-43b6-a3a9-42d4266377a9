<template>
  <div class="date-range-picker">
    <el-date-picker
      v-model="dateRange"
      type="daterange"
      range-separator="-"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      placeholder="选择时间范围"
      :format="dateFormat"
      :shortcuts="computedShortcuts"
      size="small"
    />
  </div>
</template>

<script setup lang="ts">
/*
 * 自定义时间选择器
 * 1. 支持v-model
 * 2. 支持日期格式化
 * 3. 支持日期范围选择
 * 4. 支持日期范围变化事件
 */

// 定义props
const props = defineProps({
  // 初始日期范围
  initialDateRange: {
    type: Array<Date>,
    default: () => [
      new Date(new Date().getFullYear(), new Date().getMonth(), 1), // 当月第一天
      new Date(), // 今天
    ],
  },
  // 日期格式
  dateFormat: {
    type: String,
    default: 'YYYY-MM-DD',
  },
  // 是否显示快捷选择
  showQuickSelect: {
    type: Boolean,
    default: false,
  },
  // 自定义快捷选择选项
  shortcuts: {
    type: Array,
    default: () => [],
  },
})

// 定义要发射的事件
const emit = defineEmits(['update:dateRange', 'dateRangeChange'])

// 本地日期范围状态
const dateRange = ref<[Date, Date]>(props.initialDateRange as [Date, Date])

// 预设的快捷选择选项
const defaultShortcuts = [
  {
    text: '本月',
    value: () => {
      const end = new Date()
      const fullYear = new Date().getFullYear()
      const month = new Date().getMonth()
      const start = new Date(fullYear, month, 1)
      return [start, end]
    },
  },
  {
    text: '近两月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 2)
      return [start, end]
    },
  },
  {
    text: '近三月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    },
  },
  {
    text: '半年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 6)
      return [start, end]
    },
  },
]

// 计算最终的快捷选择选项
const computedShortcuts = computed(() => {
  if (!props.showQuickSelect) {
    return []
  }
  // 如果传入了自定义快捷选择，使用自定义的，否则使用默认的
  return props.shortcuts.length > 0 ? props.shortcuts : defaultShortcuts
})

// 监听日期范围变化并发射事件
watch(
  () => dateRange.value,
  (newVal) => {
    if (newVal && newVal.length === 2) {
      // 发射更新事件，支持v-model
      emit('update:dateRange', newVal)

      // 发射变更事件，带上开始和结束日期
      const [startDate, endDate] = newVal
      emit('dateRangeChange', { startDate, endDate })
    }
  },
)
</script>

<style scoped lang="scss">
.date-range-picker {
  // 统一的容器样式 - 水平居中
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  box-sizing: border-box;

  // Element Plus 日期选择器样式已在全局主题文件中定义
  // 这里只需要确保容器的布局正确
  :deep(.el-date-editor) {
    width: 100% !important;
    max-width: 20rem; // 限制最大宽度，避免在大屏幕上过宽
  }
}
</style>
