<!-- src/components/common/UIcon/UIcon.vue -->
<template>
  <Icon :icon="name" :width="size" :height="size" :color="color" v-bind="$attrs" />
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

defineProps({
  name: {
    type: String,
    required: true,
  },
  size: {
    type: [String, Number],
    default: '1rem', // 默认大小调整为 '1em' 以便更好地适应文本
  },
  color: {
    type: String,
    default: undefined,
  },
})
</script>
