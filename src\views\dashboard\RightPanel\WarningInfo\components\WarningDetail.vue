<template>
  <div class="warning-detail">
    <!-- 详情头部 -->
    <div class="detail-header">
      <div class="header-left">
        <el-button size="small" :icon="ArrowLeft" @click="handleBack" class="back-btn">
          预警详情
        </el-button>
      </div>
      <div class="header-right">
        <el-button size="small" link @click="handleSetTypical" class="typical-btn">
          设为典型
        </el-button>
      </div>
    </div>

    <!-- 详情内容 -->
    <div class="detail-content">
      <el-scrollbar class="detail-scrollbar">
        <div class="detail-sections">
          <!-- 预警图片 -->
          <div class="warning-image-section">
            <div class="image-container">
              <img
                v-if="mainImage?.imageUrl"
                :src="mainImage.imageUrl"
                :alt="warningTypeName"
                class="warning-image"
              />
              <div v-else class="image-placeholder">
                <el-icon class="placeholder-icon"><Picture /></el-icon>
              </div>
            </div>
          </div>

          <!-- 预警信息卡片 -->
          <div class="warning-info-card">
            <div class="info-header">
              <div class="warning-type">
                <el-tag
                  :type="getWarningStatusTagType(props.warningDetail?.status || '')"
                  size="small"
                  effect="dark"
                >
                  {{ warningTypeName || '未知类型' }}
                </el-tag>
              </div>
              <div class="detection-time">{{ formatTime(props.warningDetail?.detectionTime) }}</div>
            </div>

            <div class="info-content">
              <div class="info-item">
                <span class="info-label">检测时间:</span>
                <span class="info-value">{{ formatTime(props.warningDetail?.detectionTime) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">巡检路线:</span>
                <span class="info-value">{{
                  props.warningDetail?.inspectionRoute || '【全覆盖】常山01-辉埠工业园'
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">状态:</span>
                <span class="info-value">{{ props.warningDetail?.status || '未知' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">地点:</span>
                <span class="info-value">{{
                  props.warningDetail?.location?.address || '未知'
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">坐标:</span>
                <span class="info-value">
                  {{
                    props.warningDetail?.location?.coordinates
                      ? `${props.warningDetail.location.coordinates.longitude.toFixed(4)}, ${props.warningDetail.location.coordinates.latitude.toFixed(4)}`
                      : '未知'
                  }}
                </span>
              </div>
            </div>
          </div>

          <!-- 处理时间线 -->
          <div class="timeline-section">
            <div class="timeline-title">处理过程</div>
            <el-timeline class="warning-timeline">
              <el-timeline-item
                v-for="(item, index) in props.warningDetail?.processTimeline || []"
                :key="index"
                :timestamp="formatTime(item.timestamp)"
                placement="top"
              >
                <div class="timeline-content">
                  <div class="timeline-header">
                    <span class="timeline-action">{{ item.title }}</span>
                    <span class="timeline-operator">{{ item.operator || '系统' }}</span>
                  </div>
                  <div v-if="item.description" class="timeline-description">
                    {{ item.description }}
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 底部操作按钮 -->
    <div class="detail-footer">
      <el-button size="default" @click="handleCancel" class="cancel-btn"> 取消 </el-button>
      <el-button type="primary" size="default" @click="handleComplete" class="complete-btn">
        办结
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { ArrowLeft, Picture } from '@element-plus/icons-vue'
import type { WarningDetailData } from '@/types/ui'
import { getWarningTypeName } from '@/utils/warningTypeMap'
import { getWarningStatusTagType } from '@/utils/warningIconMap'

// 定义props
interface Props {
  warningDetail: WarningDetailData | null
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  back: []
  'set-typical': [id: string]
  complete: [id: string]
}>()

// 计算属性：获取预警类型名称
const warningTypeName = computed(() => {
  if (!props.warningDetail) return ''
  return getWarningTypeName(props.warningDetail.type)
})

// 计算属性：获取主要图片（第一张图片）
const mainImage = computed(() => {
  if (!props.warningDetail?.imageTimeline?.length) return null
  return props.warningDetail.imageTimeline[0]
})

// 事件处理
const handleBack = () => {
  emit('back')
}

const handleSetTypical = () => {
  if (props.warningDetail?.id) {
    emit('set-typical', props.warningDetail.id)
  }
}

const handleCancel = () => {
  emit('back')
}

const handleComplete = () => {
  if (props.warningDetail?.id) {
    emit('complete', props.warningDetail.id)
  }
}

// 工具函数
const formatTime = (isoString?: string) => {
  if (!isoString) return '2025-03-17 11:21:13'
  const date = new Date(isoString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}
</script>

<style scoped lang="scss">
.warning-detail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba($bg-card, 0.1);
  border-radius: $border-radius-base;
}

// 详情头部
.detail-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid $border-color-light;

  .back-btn {
    background: transparent !important;
    border: none !important;
    color: $text-active !important;
    font-size: $font-size-panel-title;
    padding: 0 !important;

    &:hover {
      color: $primary-color !important;
    }
  }

  .typical-btn {
    color: $primary-color !important;
    font-size: $font-size-panel-label;
    padding: 0 !important;

    &:hover {
      color: rgba($primary-color, 0.8) !important;
    }
  }
}

// 详情内容
.detail-content {
  flex: 1;
  min-height: 0;

  .detail-scrollbar {
    height: 100%;
  }

  .detail-sections {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
}

// 预警图片
.warning-image-section {
  .image-container {
    width: 100%;
    height: 8rem;
    border-radius: $border-radius-base;
    overflow: hidden;
    background: rgba($bg-medium, 0.3);

    .warning-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .image-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: $text-inactive;

      .placeholder-icon {
        font-size: 3rem;
      }
    }
  }
}

// 预警信息卡片
.warning-info-card {
  background: rgba($bg-medium, 0.2);
  border-radius: $border-radius-base;
  padding: 0.75rem;
  border: 1px solid $border-color-light;

  .info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;

    .detection-time {
      font-size: $font-size-panel-caption;
      color: $text-secondary;
    }
  }

  .info-content {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      font-size: $font-size-panel-label;

      .info-label {
        color: $text-secondary;
        flex-shrink: 0;
        margin-right: 0.5rem;
      }

      .info-value {
        color: $text-default;
        text-align: right;
        word-break: break-all;
      }
    }
  }
}

// 时间线
.timeline-section {
  .timeline-title {
    font-size: $font-size-panel-normal;
    color: $text-active;
    font-weight: 500;
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid rgba($primary-color, 0.3);
  }

  .warning-timeline {
    padding-left: 0;
    margin-left: 0;

    :deep(.el-timeline-item) {
      padding-bottom: 1rem;

      .el-timeline-item__wrapper {
        position: relative;
        padding-left: 1.5rem;
        top: 0;
      }

      .el-timeline-item__tail {
        left: 0.25rem;
        border-left: 2px solid $border-color-light;
      }

      .el-timeline-item__node {
        left: 0;
        width: 0.5rem;
        height: 0.5rem;
        background-color: $primary-color;
        border: 2px solid $primary-color;
      }

      .el-timeline-item__timestamp {
        font-size: $font-size-panel-caption;
        color: $text-secondary;
        margin-bottom: 0.25rem;
      }

      .el-timeline-item__content {
        margin: 0;
        padding: 0;
      }
    }

    .timeline-content {
      .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.25rem;

        .timeline-action {
          font-size: $font-size-panel-label;
          color: $text-default;
          font-weight: 500;
        }

        .timeline-operator {
          font-size: $font-size-panel-caption;
          color: $text-secondary;
        }
      }

      .timeline-description {
        font-size: $font-size-panel-caption;
        color: $text-secondary;
        line-height: 1.4;
      }
    }
  }
}

// 详情底部
.detail-footer {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
  padding: 0.75rem;
  border-top: 1px solid $border-color-light;

  .cancel-btn {
    flex: 1;
    background: rgba($bg-medium, 0.3) !important;
    border-color: $border-color-light !important;
    color: $text-default !important;

    &:hover {
      background: rgba($bg-medium, 0.5) !important;
      border-color: rgba($primary-color, 0.4) !important;
    }
  }

  .complete-btn {
    flex: 1;
    background: $primary-color !important;
    border-color: $primary-color !important;
    color: $bg-dark !important;
    font-weight: 500;

    &:hover {
      background: rgba($primary-color, 0.8) !important;
      border-color: rgba($primary-color, 0.8) !important;
    }
  }
}
</style>
