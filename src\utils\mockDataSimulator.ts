/**
 * 模拟数据工具函数
 * 用于模拟WebSocket实时数据更新，将来会被真实的WebSocket替换
 */
import type { FlightRealTimeData } from '@/types/ui'
import { flightApi } from '@/api/flight'

class MockDataSimulator {
  private timer: number | null = null
  private updateCallback: ((data: FlightRealTimeData) => void) | null = null

  /**
   * 启动模拟数据更新
   * @param droneId 无人机ID
   * @param callback 数据更新回调函数
   * @param interval 更新间隔（毫秒），默认2秒
   */
  start(droneId: string, callback: (data: FlightRealTimeData) => void, interval: number = 2000) {
    // 停止之前的定时器
    this.stop()

    this.updateCallback = callback

    // 启动定时器
    this.timer = setInterval(async () => {
      try {
        // 获取基础数据
        const newData = await flightApi.getFlightRealTimeData(droneId)

        // 模拟数据变化
        this.simulateDataChanges(newData)

        // 调用回调函数更新数据
        if (this.updateCallback) {
          this.updateCallback(newData)
        }

        // console.log('模拟数据已更新', newData)
      } catch (err) {
        console.error('模拟数据更新失败:', err)
      }
    }, interval)

    console.log(`模拟数据更新已启动，间隔: ${interval}ms`)
  }

  /**
   * 停止模拟数据更新
   */
  stop() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
      this.updateCallback = null
      console.log('模拟数据更新已停止')
    }
  }

  /**
   * 检查是否正在运行
   */
  isRunning(): boolean {
    return this.timer !== null
  }

  /**
   * 模拟数据变化逻辑
   * @param data 要修改的数据对象
   */
  private simulateDataChanges(data: FlightRealTimeData) {
    // 导航数据模拟
    if (data.navigation) {
      data.navigation.heading = Math.floor(Math.random() * 360)
      data.navigation.gimbalAngle = Math.floor(Math.random() * 180) - 90
      data.navigation.horizontalSpeed = Math.floor(Math.random() * 20)
      data.navigation.verticalSpeed = parseFloat((Math.random() * 10 - 5).toFixed(1))
    }

    // 电池数据模拟
    if (data.battery) {
      // 电池电量缓慢下降，保留两位小数
      data.battery.droneBattery = parseFloat(
        Math.max(20, data.battery.droneBattery - Math.random() * 2).toFixed(2),
      )
      data.battery.controllerBattery = parseFloat(
        Math.max(30, data.battery.controllerBattery - Math.random() * 1).toFixed(2),
      )
      data.battery.current = parseFloat((Math.random() * 5).toFixed(2))
      data.battery.voltage = parseFloat((26 + Math.random() * 2).toFixed(2))
    }

    // 任务数据模拟
    if (data.mission) {
      // 任务进度缓慢增加，保留两位小数
      data.mission.progress = parseFloat(
        Math.min(100, data.mission.progress + Math.random() * 3).toFixed(2),
      )
      data.mission.speed = Math.floor(8 + Math.random() * 8)
    }

    // 温度数据模拟
    if (data.temperature) {
      // 温度小幅波动
      data.temperature.cabinTemperature = parseFloat((25 + Math.random() * 4).toFixed(1))
      data.temperature.batteryTemperature = parseFloat((24 + Math.random() * 3).toFixed(1))
    }

    // 参数数据模拟
    if (data.parameters) {
      // 信号强度变化
      const uploadSignal = -50 - Math.floor(Math.random() * 30)
      const downloadSignal = -45 - Math.floor(Math.random() * 25)
      data.parameters.uploadSignal = `${uploadSignal}dBm`
      data.parameters.downloadSignal = `${downloadSignal}dBm`

      // GPS卫星数量变化（6-15颗）
      data.parameters.gpsCount = Math.floor(6 + Math.random() * 10)

      // 高度和距离小幅变化
      data.parameters.groundAltitude = Math.floor(115 + Math.random() * 10)
      data.parameters.droneAltitude = Math.floor(80 + Math.random() * 10)
      data.parameters.droneDistance = parseFloat((1.0 + Math.random() * 0.5).toFixed(2))
    }
  }
}

// 导出单例实例
export const mockDataSimulator = new MockDataSimulator()

// 导出类型，方便其他地方使用
export type { FlightRealTimeData }
