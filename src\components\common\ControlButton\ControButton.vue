<template>
  <button class="control-btn" :class="{ active: active }" @click="handleClick" type="button">
    <slot>{{ label }}</slot>
  </button>
</template>

<script setup lang="ts">
const props = defineProps({
  active: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: 'default',
    validator: (value: string) => {
      return ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
    },
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  label: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['click'])

const handleClick = (event: MouseEvent) => {
  if (props.disabled) return
  emit('click', event, props.active)
}
</script>

<style scoped lang="scss">
.control-btn {
  display: inline-block;
  padding: 0.1rem 0.3rem;
  border: 0.5px solid rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  cursor: pointer;
  color: $text-inactive;
  background: transparent;
  transition: all 0.3s;
  font-size: 0.5rem;
  user-select: none;

  &.active {
    color: $text-active;
    background: rgba($text-active, 0.2);
    box-shadow: 0 0 10px rgba($text-active, 0.2);
  }

  &:hover {
    border-color: $text-active;
    color: $text-active;
  }
}
</style>
