/**
 * Mars3D平台插件,结合echarts可视化功能插件  mars3d-echarts
 *
 * 版本信息：v3.5.19
 * 编译日期：2024-10-29 13:42
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：维璟（北京）科技有限公司 ，2023-06-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.echarts || require('echarts')), (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'echarts', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-echarts"] = {}, global.echarts, global.mars3d));
})(this, (function (exports, echarts, mars3d) { 
'use strict';const _0x3bdfaf=_0x2847;(function(_0x542464,_0x32aa46){const _0x44ba25={_0x58b4b0:0x122,_0x4e735a:0x126,_0x4c6971:0x13b},_0x1e7f33=_0x2847,_0xcb19b6=_0x542464();while(!![]){try{const _0x2bfbfd=parseInt(_0x1e7f33(0x12c))/0x1+-parseInt(_0x1e7f33(_0x44ba25._0x58b4b0))/0x2+-parseInt(_0x1e7f33(0x149))/0x3*(parseInt(_0x1e7f33(_0x44ba25._0x4e735a))/0x4)+parseInt(_0x1e7f33(0x12d))/0x5*(parseInt(_0x1e7f33(0x140))/0x6)+parseInt(_0x1e7f33(0x14f))/0x7*(-parseInt(_0x1e7f33(_0x44ba25._0x4c6971))/0x8)+parseInt(_0x1e7f33(0x13f))/0x9*(parseInt(_0x1e7f33(0x13c))/0xa)+parseInt(_0x1e7f33(0x134))/0xb;if(_0x2bfbfd===_0x32aa46)break;else _0xcb19b6['push'](_0xcb19b6['shift']());}catch(_0x54ffba){_0xcb19b6['push'](_0xcb19b6['shift']());}}}(_0x1e7c,0x5e892));function _interopNamespace(_0x418ac6){const _0x313503={_0x277a44:0x123},_0x3e4164={_0x3e3434:0x159},_0x1bbcf2=_0x2847;if(_0x418ac6&&_0x418ac6['__esModule'])return _0x418ac6;var _0x1080ee=Object['create'](null);return _0x418ac6&&Object[_0x1bbcf2(_0x313503._0x277a44)](_0x418ac6)['forEach'](function(_0x58177c){const _0x394d49=_0x1bbcf2;if(_0x58177c!=='default'){var _0x41a1fd=Object['getOwnPropertyDescriptor'](_0x418ac6,_0x58177c);Object['defineProperty'](_0x1080ee,_0x58177c,_0x41a1fd[_0x394d49(_0x3e4164._0x3e3434)]?_0x41a1fd:{'enumerable':!![],'get':function(){return _0x418ac6[_0x58177c];}});}}),_0x1080ee['default']=_0x418ac6,_0x1080ee;}var echarts__namespace=_interopNamespace(echarts),mars3d__namespace=_interopNamespace(mars3d);function _0x2847(_0x428de4,_0x593ee9){const _0x1e7c2c=_0x1e7c();return _0x2847=function(_0x284738,_0x1309b9){_0x284738=_0x284738-0x11b;let _0x2559ba=_0x1e7c2c[_0x284738];return _0x2559ba;},_0x2847(_0x428de4,_0x593ee9);}const Cesium$1=mars3d__namespace['Cesium'];class CompositeCoordinateSystem{constructor(_0x4bed0c,_0x210394){const _0x25588f={_0x3e0630:0x128},_0x5b805e=_0x2847;this['_mars3d_scene']=_0x4bed0c,this['dimensions']=[_0x5b805e(0x131),'lat'],this['_mapOffset']=[0x0,0x0],this[_0x5b805e(_0x25588f._0x3e0630)]=_0x210394;}['setMapOffset'](_0x5dd448){const _0x4d9a59={_0x332942:0x136},_0x478c60=_0x2847;this[_0x478c60(_0x4d9a59._0x332942)]=_0x5dd448;}[_0x3bdfaf(0x13a)](){return this['_mars3d_scene'];}['dataToPoint'](_0x5d95e8){const _0xe2065b={_0x2d95be:0x15c,_0x371d4c:0x143,_0x25decf:0x124,_0x551db6:0x15c},_0x233ed5=_0x3bdfaf,_0x38db5c=this['_mars3d_scene'],_0x178ddc=[NaN,NaN];let _0x2b9c5e=_0x38db5c['echartsFixedHeight'];_0x38db5c[_0x233ed5(0x14c)]&&(_0x2b9c5e=_0x38db5c[_0x233ed5(_0xe2065b._0x2d95be)]['getHeight'](Cesium$1['Cartographic']['fromDegrees'](_0x5d95e8[0x0],_0x5d95e8[0x1])));const _0x55f4cf=Cesium$1['Cartesian3']['fromDegrees'](_0x5d95e8[0x0],_0x5d95e8[0x1],_0x2b9c5e);if(!_0x55f4cf)return _0x178ddc;const _0x72b778=mars3d__namespace['PointTrans']['toWindowCoordinates'](_0x38db5c,_0x55f4cf);if(!_0x72b778)return _0x178ddc;if(_0x38db5c[_0x233ed5(_0xe2065b._0x371d4c)]&&_0x38db5c[_0x233ed5(_0xe2065b._0x25decf)]===Cesium$1['SceneMode']['SCENE3D']){const _0x154e1a=new Cesium$1['EllipsoidalOccluder'](_0x38db5c[_0x233ed5(_0xe2065b._0x551db6)]['ellipsoid'],_0x38db5c[_0x233ed5(0x12b)]['positionWC']),_0x33db94=_0x154e1a['isPointVisible'](_0x55f4cf);if(!_0x33db94)return _0x178ddc;}return[_0x72b778['x']-this[_0x233ed5(0x136)][0x0],_0x72b778['y']-this['_mapOffset'][0x1]];}['getViewRect'](){const _0x9f9d83={_0x2d5fd3:0x11f},_0xf0e867=_0x3bdfaf,_0x2c0e47=this['_api'];return new echarts__namespace[(_0xf0e867(0x133))][(_0xf0e867(_0x9f9d83._0x2d5fd3))](0x0,0x0,_0x2c0e47['getWidth'](),_0x2c0e47['getHeight']());}['getRoamTransform'](){return echarts__namespace['matrix']['create']();}}CompositeCoordinateSystem['dimensions']=['lng',_0x3bdfaf(0x153)],CompositeCoordinateSystem[_0x3bdfaf(0x142)]=function(_0xb5c1a0,_0x213a13){const _0x5efda8={_0x4a2686:0x139},_0x4f1233=_0x3bdfaf;let _0x3ca5a7;const _0x2eeb27=_0xb5c1a0['scheduler'][_0x4f1233(_0x5efda8._0x4a2686)]['_mars3d_scene'];_0xb5c1a0[_0x4f1233(0x14a)]('mars3dMap',function(_0x2327c8){const _0x6ba417=_0x4f1233,_0x32f2ac=_0x213a13[_0x6ba417(0x130)]()['painter'];if(!_0x32f2ac)return;!_0x3ca5a7&&(_0x3ca5a7=new CompositeCoordinateSystem(_0x2eeb27,_0x213a13)),_0x2327c8['coordinateSystem']=_0x3ca5a7,_0x3ca5a7['setMapOffset'](_0x2327c8['__mapOffset']||[0x0,0x0]);}),_0xb5c1a0['eachSeries'](function(_0x1caca3){const _0x1bd04c=_0x4f1233;_0x1caca3['get'](_0x1bd04c(0x145))==='mars3dMap'&&(!_0x3ca5a7&&(_0x3ca5a7=new CompositeCoordinateSystem(_0x2eeb27,_0x213a13)),_0x1caca3[_0x1bd04c(0x145)]=_0x3ca5a7);});};if(echarts__namespace!==null&&echarts__namespace!==void 0x0&&echarts__namespace['init'])echarts__namespace[_0x3bdfaf(0x12f)](_0x3bdfaf(0x155),CompositeCoordinateSystem),echarts__namespace[_0x3bdfaf(0x144)]({'type':'mars3dMapRoam','event':_0x3bdfaf(0x13e),'update':'updateLayout'},function(_0x5282c0,_0x4bc3f9){}),echarts__namespace[_0x3bdfaf(0x13d)]({'type':'mars3dMap','getBMap':function(){return this['_mars3d_scene'];},'defaultOption':{'roam':![]}}),echarts__namespace['extendComponentView']({'type':'mars3dMap','init':function(_0x5d10a4,_0x9ba6e2){const _0x28b710={_0x3eff57:0x121},_0xb41efa=_0x3bdfaf;this['api']=_0x9ba6e2,this['scene']=_0x5d10a4[_0xb41efa(_0x28b710._0x3eff57)]['ecInstance']['_mars3d_scene'],this['scene']['postRender'][_0xb41efa(0x141)](this['moveHandler'],this);},'moveHandler':function(_0x2243d4,_0x33c7d0){const _0x3b855a=_0x3bdfaf;this[_0x3b855a(0x12a)]['dispatchAction']({'type':'mars3dMapRoam'});},'render':function(_0x7ec950,_0x305af3,_0x25ac1b){},'dispose':function(_0xe426dc){const _0x149d17={_0x4db548:0x151},_0x54893b=_0x3bdfaf;this['scene']['postRender'][_0x54893b(_0x149d17._0x4db548)](this['moveHandler'],this);}});else throw new Error('请引入\x20echarts\x20库\x20');const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer'][_0x3bdfaf(0x152)];class EchartsLayer extends BaseLayer{constructor(_0x46750d={}){const _0x4722b9={_0x492dc9:0x14e},_0x1cd29b=_0x3bdfaf;super(_0x46750d),this['_pointerEvents']=this['options'][_0x1cd29b(_0x4722b9._0x492dc9)];}get['layer'](){return this['_echartsInstance'];}get['pointerEvents'](){const _0x296d2c=_0x3bdfaf;return this[_0x296d2c(0x157)];}set['pointerEvents'](_0x10c43e){const _0x82c5a1=_0x3bdfaf;this[_0x82c5a1(0x157)]=_0x10c43e,this['_echartsContainer']&&(_0x10c43e?this['_echartsContainer']['style']['pointerEvents']='all':this['_echartsContainer']['style']['pointerEvents']=_0x82c5a1(0x127));}['_setOptionsHook'](_0x55de5c,_0x2525a1){this['setEchartsOption'](_0x55de5c);}['_showHook'](_0x397d52){const _0x582ee5=_0x3bdfaf;_0x397d52?this['_echartsContainer']['style']['visibility']='visible':this['_echartsContainer'][_0x582ee5(0x12e)]['visibility']='hidden';}['_mountedHook'](){const _0x38462e={_0x27d212:0x11e,_0x3a69f0:0x143},_0x54705e=_0x3bdfaf;this['_map'][_0x54705e(_0x38462e._0x27d212)][_0x54705e(_0x38462e._0x3a69f0)]=this['options']['depthTest']??!![],this['_map'][_0x54705e(_0x38462e._0x27d212)]['echartsAutoHeight']=this[_0x54705e(0x156)]['clampToGround']??![],this['_map']['scene']['echartsFixedHeight']=this[_0x54705e(0x156)][_0x54705e(0x11d)]??0x0;}['_addedHook'](){const _0x50dd97={_0xb44317:0x11e},_0x1862c3=_0x3bdfaf;this['_echartsContainer']=this['_createChartOverlay'](),this['_echartsInstance']=echarts__namespace['init'](this['_echartsContainer']),this[_0x1862c3(0x15b)]['_mars3d_scene']=this['_map'][_0x1862c3(_0x50dd97._0xb44317)],this['setEchartsOption'](this['options']);}['_removedHook'](){const _0x3760b1={_0x4381fe:0x137},_0x2caded=_0x3bdfaf;this[_0x2caded(0x15b)]&&(this['_echartsInstance']['clear'](),this['_echartsInstance']['dispose'](),delete this['_echartsInstance']),this['_echartsContainer']&&(this['_map'][_0x2caded(0x147)][_0x2caded(_0x3760b1._0x4381fe)](this['_echartsContainer']),delete this['_echartsContainer']);}[_0x3bdfaf(0x138)](){const _0x2e98e3={_0x1c1613:0x129,_0x50d96c:0x12e,_0x11ee52:0x120},_0x3a764b=_0x3bdfaf,_0x475c86=mars3d__namespace[_0x3a764b(_0x2e98e3._0x1c1613)]['create']('div','mars3d-echarts',this['_map']['container']);return _0x475c86['id']=this['id'],_0x475c86[_0x3a764b(_0x2e98e3._0x50d96c)]['position']='absolute',_0x475c86['style']['top']='0px',_0x475c86[_0x3a764b(0x12e)]['left']='0px',_0x475c86['style']['width']=this['_map'][_0x3a764b(0x11e)]['canvas'][_0x3a764b(0x148)]+'px',_0x475c86[_0x3a764b(0x12e)]['height']=this['_map']['scene']['canvas']['clientHeight']+'px',_0x475c86[_0x3a764b(_0x2e98e3._0x50d96c)]['pointerEvents']=this['_pointerEvents']?_0x3a764b(0x11b):_0x3a764b(0x127),_0x475c86['style']['zIndex']=this['options'][_0x3a764b(_0x2e98e3._0x11ee52)]??0x9,_0x475c86;}['resize'](){const _0x13fc93={_0x400fe9:0x15a,_0x5634ce:0x135,_0x4ad702:0x15b},_0x2fc2a8=_0x3bdfaf;if(!this['_echartsInstance'])return;this['_echartsContainer'][_0x2fc2a8(0x12e)]['width']=this[_0x2fc2a8(_0x13fc93._0x400fe9)]['scene']['canvas']['clientWidth']+'px',this['_echartsContainer']['style']['height']=this[_0x2fc2a8(0x15a)]['scene'][_0x2fc2a8(_0x13fc93._0x5634ce)]['clientHeight']+'px',this[_0x2fc2a8(_0x13fc93._0x4ad702)]['resize']();}[_0x3bdfaf(0x11c)](_0x5c4ac4,_0x244f1d,_0x4cdcef){const _0x439c2d=_0x3bdfaf;this['_echartsInstance']&&(_0x5c4ac4['mars3dMap']=_0x5c4ac4[_0x439c2d(0x155)]||{},this[_0x439c2d(0x15b)][_0x439c2d(0x132)](_0x5c4ac4,_0x244f1d,_0x4cdcef));}[_0x3bdfaf(0x158)](_0x413951){const _0x294fbe={_0x23ad75:0x14d,_0xd8275c:0x150},_0x386acf={_0x315690:0x125},_0x139c8a=_0x3bdfaf;let _0x367270,_0x2298c3,_0x3d0ff2,_0x14a047;function _0x158993(_0x985b0c){const _0x2cd164=_0x2847;if(!Array[_0x2cd164(_0x386acf._0x315690)](_0x985b0c))return;const _0x4224ce=_0x985b0c[0x0]||0x0,_0x5426e4=_0x985b0c[0x1]||0x0;_0x4224ce!==0x0&&_0x5426e4!==0x0&&(_0x367270===undefined?(_0x367270=_0x4224ce,_0x2298c3=_0x4224ce,_0x3d0ff2=_0x5426e4,_0x14a047=_0x5426e4):(_0x367270=Math['min'](_0x367270,_0x4224ce),_0x2298c3=Math['max'](_0x2298c3,_0x4224ce),_0x3d0ff2=Math['min'](_0x3d0ff2,_0x5426e4),_0x14a047=Math['max'](_0x14a047,_0x5426e4)));}const _0x43fbb0=this['options'][_0x139c8a(_0x294fbe._0x23ad75)];_0x43fbb0&&_0x43fbb0['forEach'](_0x2dd6f9=>{_0x2dd6f9['data']&&_0x2dd6f9['data']['forEach'](_0x3b5bea=>{if(_0x3b5bea['value'])_0x158993(_0x3b5bea['value']);else _0x3b5bea['coords']&&_0x3b5bea['coords']['forEach'](_0x44de18=>{_0x158993(_0x44de18);});});});if(_0x367270===0x0&&_0x3d0ff2===0x0&&_0x2298c3===0x0&&_0x14a047===0x0)return null;return _0x413951!==null&&_0x413951!==void 0x0&&_0x413951['isFormat']?{'xmin':_0x367270,'xmax':_0x2298c3,'ymin':_0x3d0ff2,'ymax':_0x14a047}:Cesium['Rectangle'][_0x139c8a(_0x294fbe._0xd8275c)](_0x367270,_0x3d0ff2,_0x2298c3,_0x14a047);}['on'](_0x4938ac,_0x1386e2,_0x8003af){const _0x427374=_0x3bdfaf;return this[_0x427374(0x15b)]['on'](_0x4938ac,_0x1386e2,_0x8003af||this),this;}['onByQuery'](_0x44e6e5,_0x36b967,_0x30ee09,_0x231604){const _0x27f470={_0x3c4aa3:0x15b},_0x190cb5=_0x3bdfaf;return this[_0x190cb5(_0x27f470._0x3c4aa3)]['on'](_0x44e6e5,_0x36b967,_0x30ee09,_0x231604||this),this;}['off'](_0x2cb482,_0x40aea0,_0x1a43cf){const _0x130923={_0x480b42:0x15b},_0x35ee84=_0x3bdfaf;return this[_0x35ee84(_0x130923._0x480b42)]['off'](_0x2cb482,_0x40aea0,_0x1a43cf||this),this;}}function _0x1e7c(){const _0x2ca7fb=['27099nnmEUK','12zRltUg','addEventListener','create','echartsDepthTest','registerAction','coordinateSystem','hasOwnProperty','container','clientWidth','1122nWBAIc','eachComponent','EchartsLayer','echartsAutoHeight','series','pointerEvents','3717KVHzFl','fromDegrees','removeEventListener','BaseLayer','lat','echarts','mars3dMap','options','_pointerEvents','getRectangle','get','_map','_echartsInstance','globe','all','setEchartsOption','fixedHeight','scene','BoundingRect','zIndex','scheduler','563670FeqPjp','keys','mode','isArray','4012RDHZzD','none','_api','DomUtil','api','camera','247006Rztccn','1109270EjSgOZ','style','registerCoordinateSystem','getZr','lng','setOption','graphic','7518918dfLeVx','canvas','_mapOffset','removeChild','_createChartOverlay','ecInstance','getBMap','5608XxAnWJ','140GRCxsF','extendComponentModel','mars3dMapRoam'];_0x1e7c=function(){return _0x2ca7fb;};return _0x1e7c();}mars3d__namespace['LayerUtil']['register'](_0x3bdfaf(0x154),EchartsLayer),mars3d__namespace['layer'][_0x3bdfaf(0x14b)]=EchartsLayer,mars3d__namespace[_0x3bdfaf(0x154)]=echarts__namespace,exports['EchartsLayer']=EchartsLayer,Object[_0x3bdfaf(0x123)](echarts)['forEach'](function(_0x470da8){const _0x42fe8e=_0x3bdfaf;if(_0x470da8!=='default'&&!exports[_0x42fe8e(0x146)](_0x470da8))Object['defineProperty'](exports,_0x470da8,{'enumerable':!![],'get':function(){return echarts[_0x470da8];}});}),Object['defineProperty'](exports,'__esModule',{'value':!![]});
}));
