#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
# @Time    :  2023/3/13 15:59
# <AUTHOR> chenxw
# @Email   : <EMAIL>
# @File    : sysViews.py
# @Descr   :
# @Software: PyCharm
# !/usr/bin/python3.9
# -*- coding: utf-8 -*-
# @Time    :  2022/12/15 20:15
# <AUTHOR> chenxw
# @Email   : <EMAIL>
# @File    : sysViews.py
# @Descr   :
# @Software: PyCharm
import datetime
import json
import logging
import time

from django.db import connection
from loguru import logger
from rest_framework import viewsets
from rest_framework.decorators import action

from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from vgis_log.logTools import Lo<PERSON>Helper
from vgis_utils.vgis_http.httpTools import HttpHelper

from my_app.manage.sysManager import SysOperator
from my_app.manage.userManager import UserOperator
from my_app.models import SysConfig, SysD<PERSON>art<PERSON>, <PERSON>ysLog, SysMenu, Sys<PERSON>s, SysRole, SysRoleMenu, SysUser, \
    SysUserRole, SysUserToken, AuthUser, SysParam, TtServiceData
from my_app.serializers import SysConfigSerializer, SysDepartmentSerializer, SysLogSerializer, SysMenuSerializer, \
    SysOssSerializer, SysRoleSerializer, SysRoleMenuSerializer, SysUserSerializer, SysUserRoleSerializer, \
    SysUserTokenSerializer, AuthUserSerializer, SysParamSerializer, TtServiceDataSerializer
from my_app.utils.passwordUtility import PasswordHelper
from my_app.utils.snowflake_id_util import SnowflakeIDUtil
from my_app.utils.sysmanUtility import SysmanHelper
from my_app.views.custom import CustomModelViewSet
from my_app.views.response.baseRespone import Result
from my_project.settings import SUPERMAP_ISERVER_URL
from my_project.token import ExpiringTokenAuthentication

logger = logging.getLogger('django')


class TtServiceDataViewSet(viewsets.ModelViewSet):
    queryset = TtServiceData.objects.all().order_by('id')
    serializer_class = TtServiceDataSerializer
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)

    def get_object(self, *args, **kwargs):
        result = ""
        # 更新会进来
        if self.action == "update":
            return super().get_object(*args, **kwargs)
        else:
            result = super().get_object(*args, **kwargs)
            result.service_url =SUPERMAP_ISERVER_URL+result.service_url
            return result
    @action(detail=False, methods=['POST'], url_path='get_data_service_details')
    # 获取数据服务详情
    def get_data_service_details(self, request, *args, **kwargs):
        id = request.data.get('id')
        sysOperator = SysOperator(connection)
        res = sysOperator.get_data_service_details(request, id)
        return Response(res)

    @action(detail=False, methods=['POST'], url_path='get_data_service_records_by_datasource_dataset')
    # 获取数据服务要素详情-根据数据源和数据集
    def get_data_service_records_by_datasource_dataset(self, request, *args, **kwargs):
        sysOperator = SysOperator(connection)
        res = sysOperator.get_data_service_records_by_datasource_dataset(request)
        return Response(res)

    def list(self, request, *args, **kwargs):

        # interface_type_category = self.request.query_params.get('interface_type_category')
        results = TtServiceData.objects.all()
        data = []
        for result in results:
            result.service_url = SUPERMAP_ISERVER_URL + result.service_url
            data.append(TtServiceDataSerializer(result).data)
        results = {'results': data}
        return Response(results)

    # 获取可导入服务列表
    @action(detail=False, methods=['GET'], url_path='detail')
    def importable_service(self, request):
        sysOperator = SysOperator(connection)
        res = sysOperator.get_importable_service(request)
        return Response(res)


    # 获取可导入服务列表
    @action(detail=False, methods=['GET'], url_path='importable_service')
    def importable_service(self, request):
        sysOperator = SysOperator(connection)
        res = sysOperator.get_importable_service(request)
        return Response(res)


    # 导入服务
    def create(self, request, *args, **kwargs):

        start = time.perf_counter()
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        service_ename = request.data["service_ename"]
        if len(TtServiceData.objects.filter(service_ename=service_ename)) > 0:
            res = {
                'success': False,
                'info': "服务{}已导入，请导入其他服务".format(service_ename)
            }

            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            end = time.perf_counter()
            t = end - start
            logger.info("总共用时{}秒".format(t))
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "导入服务失败",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            return Response(res)
        else:
            request.data["id"] = SnowflakeIDUtil.snowflakeId()
            # 入库只保留相对路径
            request.data["service_url"] = request.data["service_url"].replace(SUPERMAP_ISERVER_URL, "")
            request.data["create_user_id"] = request.auth.user_id
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            TtServiceData.objects.create(**request.data)

            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            end = time.perf_counter()
            t = end - start
            logger.info("总共用时{}秒".format(t))
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "新增视口书签",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))

            res = {
                'success': True,
                'info': "导入服务:{}成功".format(request.data["service_ename"])
            }
            return Response(res)

    # 查询
    @action(detail=False, methods=['POST'], url_path='query_list')
    def query_list(self, request):
        try:
            function_title = "服务数据分页查询"
            fail_flag = "失败"
            start = LoggerHelper.set_start_log_info(logger)
            operator = SysOperator(connection)
            response_data = operator.query_service_data_list(request)
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            return response_data
        except Exception as exp:
            msg = "{}{}".format(function_title, fail_flag)
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path,
                                                       request.auth.user, request,
                                                       function_title, msg, None)
            return Result.fail(msg, str(exp))





    # 设置服务状态
    @action(detail=False, methods=['POST'], url_path='set_status')
    def set_status(self, request):
        sysOperator = SysOperator(connection)
        res = sysOperator.set_service_status(request)
        return Response(res)






class SysConfigViewSet(viewsets.ModelViewSet):
    queryset = SysConfig.objects.all().order_by('id')
    serializer_class = SysConfigSerializer
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)




'''
   部门相关操作 

'''


class SysDepartmentViewSet(CustomModelViewSet):
    queryset = SysDepartment.objects.all().order_by('department_id')
    serializer_class = SysDepartmentSerializer
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)
    # 默认创建一个 企业管理层

    '''
        创建部门 --
    '''

    def create(self, request, *args, **kwargs):
        function_title = "新增部门请求时间：{}".format(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        data = request.data
        #parent_id = data.get('parent_id')
        try:
            parent_id = data['parent_id']
            # 调用日志工具类的方法，记录操作开始的日志信息，如开始时间等
            start = LoggerHelper.set_start_log_info(logger)
            api_path = "sysDepartment/create/"
            verify_msg = None
            if not parent_id:
                verify_msg = "parent_id 是必填参数"
            if verify_msg:
                return Result.fail(verify_msg, verify_msg)

            max_num = SysmanHelper.getDepartOrderNum(parent_id, connection)
            department_id = SnowflakeIDUtil.snowflakeId()

            request.data['department_id'] =department_id
            request.data["order_num"] = max_num + 1
            request.data["del_flag"] = 0
            request.data["create_user_id"] = request.auth.user_id
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            SysDepartment.objects.create(**request.data)

            res = {
                'success': True,
                'info': "新增部门:{}成功".format(request.data["department_name"])
            }
            function_title += "返回时间：{}".format(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 此处注释掉的日志记录代码，可根据实际需求决定是否启用
            LoggerHelper.insert_log_info(SysLog, request.auth, function_title, request.path,
                                         HttpHelper.get_params_request(request),
                                         0, HttpHelper.get_ip_request(request))
            return Response(res)
        except Exception as exp:
            res = {
                'success': False,
                'info': "新增部门失败：{}".format(str(exp))
            }
            return Response(res)
    @action(detail=False, methods=['POST'])
    def update_dept(self, request, *args, **kwargs):
        title = "更新部门"
        print('-------------------------------------------------------')
        try:
            start = time.perf_counter()
            logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

            # id = kwargs["pk"]
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            end = time.perf_counter()
            t = end - start
            logger.info("总共用时{}秒".format(t))
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "修改部门",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))

            department_id = request.data["department_id"]

            print(department_id)
            tis = SysDepartment.objects.filter(department_id=department_id)
            if len(tis) > 0:

                tis.update(**request.data)


                res = {
                    'success': True,
                    'info': "修改部门成功"
                }
            else:
                res = {
                    'success': False,
                    'info': "修改部门失败数据已被删除"
                }

        except Exception as exp:

            print(exp)

            print(exp.__traceback__.tb_lineno)  # 发生异常所在的行数
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            end = time.perf_counter()
            t = end - start
            logger.info("总共用时{}秒".format(t))
            # 日志入库
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            return Response(res)

    # 获取部门列表-sql

    @action(detail=False, methods=['GET'], url_path='sqlsearch', permission_classes=([AllowAny]))
    def sql_search(self, request):
        sysOperator = SysOperator(connection)
        department_name = self.request.query_params.get('department_name', '')
        department_status = self.request.query_params.get('department_status', '')
        res = sysOperator.sql_search_department(request, department_name, department_status)

        return Response(res)

    # 获取部门状态列表
    @action(detail=False, methods=['GET'], url_path='departstatus')
    def status_list(self, request):
        start = time.perf_counter()
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        res = {
            'success': True,
            'info': [{'status': '正常', 'code': 1}, {'status': '停用', 'code': 0}]
        }
        logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        end = time.perf_counter()
        t = end - start
        logger.info("总共用时{}秒".format(t))
        LoggerHelper.insert_log_info(SysLog, request.auth.user, "获取部门状态列表",
                                     request.path,
                                     HttpHelper.get_params_request(request),
                                     t, HttpHelper.get_ip_request(request))
        return Response(res)

    # 修改部门状态
    @action(detail=False, methods=['POST'], url_path='setstatus')
    def set_status(self, request):
        sysOperator = SysOperator(connection)
        res = sysOperator.set_department_status(request)
        return Response(res)

    # 逻辑删除部门及下属部门
    @action(detail=False, methods=['POST'], url_path='delete')
    def delete_department(self, request):
        function_title = "删除部门请求时间：{}".format(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        #查询部门下面有没有用户，没有就删除，有则不执行删除
        #SysUser.objects.filter(department_id=request.data)
        print(request.data['department_id'])
        try:
            id = request.data['department_id']
            user = AuthUser.objects.filter(department_id=id)
            if len(user) > 0:
                res = {
                    'success': False,
                    'info': "删除失败，请先删除部门下的用户，在删除部门！"
                }
                return Response(res)
            else:
                sysOperator = SysOperator(connection)
                res = sysOperator.delete_department(request)
                start = time.perf_counter()
                end = time.perf_counter()
                t = end - start
                function_title += "返回时间：{}".format(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                LoggerHelper.insert_log_info(SysLog, request.auth.user, "删除部门",
                                             request.path,
                                             HttpHelper.get_params_request(request),
                                             t, HttpHelper.get_ip_request(request), function_title)
                return Response(res)
        except Exception as exp:
            res = {
                'success': False,
                'info': "删除部门失败：{}".format(str(exp))
            }
            return Response(res)


class SysLogViewSet(viewsets.ModelViewSet):
    queryset = SysLog.objects.all().order_by('id')
    serializer_class = SysLogSerializer
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)

    # 获取日志列表-sql
    @action(detail=False, url_path='quer_log' , methods=['POST'])
    def quer_log(self, request):
        function_title = "获取日志列表"

        try:
            sysOperator = SysOperator(connection)

            static_value = sysOperator.sql_search_log(request)
            # 构建操作成功的消息
            msg = "{}成功".format(function_title)
            # 返回成功结果，包含操作成功的消息和查询结果
            return Result.sucess(msg, static_value)
        except Exception as exp:

            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))

            return Result.fail("查询失败", error_info)



    # #删除日志
    def destroy(self, request, *args, **kwargs):
        title = "删除日志"
        res = ""
        id = kwargs["pk"]
        start = time.perf_counter()
        try:
            super().destroy(request, *args, **kwargs)
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            end = time.perf_counter()
            t = end - start
            logger.info("总共用时{}秒".format(t))
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title,
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            res = {
                'success': True,
                'info': "{}(编号为{})成功".format(title, id)
            }
        except Exception as exp:
            info = "{}(编号为{})失败，原因为：{}".format(title, id, str(exp))
            res = {
                'success': True,
                'info': info
            }
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path,
                                                       request.auth.user, request,
                                                       "删除日志失败", info, None)
        finally:
            res

        return Response(res)


class SysMenuViewSet(viewsets.ModelViewSet):
    queryset = SysMenu.objects.all().order_by('menu_id')
    serializer_class = SysMenuSerializer
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)

    @action(detail=False, methods=['POST'], url_path='menu_list', permission_classes=([IsAuthenticated]))
    def menu_list(self, request, *args, **kwargs):
        title = "查询菜单"

        try:
            sysOperator = SysOperator(connection)
            values = sysOperator.sql_search_menue(request)
            # sql_search_menue
            # results = SysMenu.objects.all().order_by('menu_id')
            # data = []
            # for result in results:
            #     data.append(SysMenuSerializer(result).data)

            return Result.sucess("{}成功".format(title),values)
        except Exception as exp:
            error_info = "{}失败，可能原因是{}".format(title, str(exp))

            return Result.fail("查询失败", error_info)

    def create(self, request, *args, **kwargs):
        function_title = "新增菜单"
        start = LoggerHelper.set_start_log_info(logger)
        api_path = request.path
        menu_name = request.data["name"]

        parent_id = request.data["parent_id"]

        verify_msg = None
        if parent_id is None or str(parent_id) == "":
            verify_msg = "parent_id 是必填参数"
        if verify_msg:
            return Result.fail(verify_msg, verify_msg)

        if len(SysMenu.objects.filter(name=menu_name)) > 0:
            error_info = "新增的菜单名:{}已存在，请换个名称".format(menu_name)
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, error_info, None)
            return Result.fail(error_info, error_info)
        else:
            request.data["menu_id"] = SnowflakeIDUtil.snowflakeId()
            request.data["create_user_id"] = request.auth.user_id
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # max_num = SysmanHelper.getMenuOrderNum(parent_id, connection)
            # request.data["order_num"] = max_num + 1
            SysMenu.objects.create(**request.data)
            # super().create(request)

            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, None)

    def update(self, request, *args, **kwargs):
        function_title = "修改菜单"
        start = LoggerHelper.set_start_log_info(logger)
        api_path = request.path
        id = kwargs["pk"]
        if len(SysMenu.objects.filter(menu_id=id)) > 0:
            old_menu_name = SysMenu.objects.filter(menu_id=id)[0].name

        new_menu_name = request.data["name"]
        if old_menu_name != new_menu_name and len(SysMenu.objects.filter(name=new_menu_name)) > 0:

            error_info = "更新的菜单名:{}已存在，请换个名称".format(new_menu_name)
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, error_info, None)
            return Result.fail(error_info, error_info)
        else:
            super().update(request, *args, **kwargs)
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, None)

    # #删除菜单的同时，将sys_role_menu里的菜单删除
    def destroy(self, request, *args, **kwargs):
        title = "删除菜单"
        res = ""
        id = kwargs["pk"]
        start = time.perf_counter()
        try:
            SysRoleMenu.objects.filter(menu_id=id).delete()
            SysMenu.objects.filter(parent_id=id).delete()
            super().destroy(request, *args, **kwargs)
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            end = time.perf_counter()
            t = end - start
            logger.info("总共用时{}秒".format(t))
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title,
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            res = {
                'success': True,
                'info': "{}(编号为{})成功".format(title, id)
            }
        except Exception as exp:
            res = {
                'success': True,
                'info': "{}(编号为{})失败，原因为：{}".format(title, id, str(exp))
            }
        finally:
            res

        return Response(res)


class SysOssViewSet(viewsets.ModelViewSet):
    queryset = SysOss.objects.all().order_by('id')
    serializer_class = SysOssSerializer
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)


class SysRoleViewSet(viewsets.ModelViewSet):
    queryset = SysRole.objects.all().order_by('role_id')
    serializer_class = SysRoleSerializer
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)

    def list(self, request, *args, **kwargs):
        results = SysRole.objects.all().order_by('role_id')

        data = []
        for result in results:
            data.append(SysRoleSerializer(result).data)
        results = {'results': data}
        return Response(results)

    def create(self, request, *args, **kwargs):
        start = time.perf_counter()
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        role_name = request.data["role_name"]
        if len(SysRole.objects.filter(role_name=role_name)) > 0:
            res = {
                'success': False,
                'info': "新增的角色名:{}已存在，请换个名称".format(role_name)
            }

            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            end = time.perf_counter()
            t = end - start
            logger.info("总共用时{}秒".format(t))
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "新增角色失败",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            return Response(res)
        else:
            request.data["role_id"] = SnowflakeIDUtil.snowflakeId()
            request.data["create_user_id"] = request.auth.user_id
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            menuIdList = request.data["menuIdList"]
            request.data.pop("menuIdList")
            SysRole.objects.create(**request.data)
            # returnrole = super().create(request)
            for menu_id in menuIdList:
                obj = {}
                obj["id"]= SnowflakeIDUtil.snowflakeId()
                obj["role_id"] = request.data["role_id"]
                obj["menu_id"] = menu_id
                SysRoleMenu.objects.create(**obj)

            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            end = time.perf_counter()
            t = end - start
            logger.info("总共用时{}秒".format(t))
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "新增角色",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))

            res = {
                'success': True,
                'info': "新增角色:{}成功".format(request.data["role_name"])
            }
            return Response(res)

    def update(self, request, *args, **kwargs):
        start = time.perf_counter()
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        id = kwargs["pk"]
        if len(SysRole.objects.filter(role_id=id)) > 0:
            old_role_name = SysRole.objects.filter(role_id=id)[0].role_name

        new_role_name = request.data["role_name"]
        if old_role_name != new_role_name and len(SysRole.objects.filter(role_name=new_role_name)) > 0:
            res = {
                'success': False,
                'info': "更新的角色名:{}已存在，请换个名称".format(new_role_name)
            }
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            end = time.perf_counter()
            t = end - start
            logger.info("总共用时{}秒".format(t))
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "更新角色失败",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            return Response(res)
        else:
            SysRoleMenu.objects.filter(role_id=id).delete()
            for menu_id in request.data["menuIdList"]:
                obj = {}
                obj["id"] = SnowflakeIDUtil.snowflakeId()
                obj["role_id"] = id
                obj["menu_id"] = menu_id
                SysRoleMenu.objects.create(**obj)
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            end = time.perf_counter()
            t = end - start
            logger.info("总共用时{}秒".format(t))
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "更新角色",
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            super().update(request, *args, **kwargs)
            res = {
                'success': True,
                'info': "修改角色成功"
            }
            return Response(res)

    # # 删除角色的同时，将sys_role_menu \sys_user_role里的角色删除
    def destroy(self, request, *args, **kwargs):
        title = "删除角色"
        res = ""
        id = kwargs["pk"]
        start = time.perf_counter()
        try:
            SysUserRole.objects.filter(role_id=id).delete()
            SysRoleMenu.objects.filter(role_id=id).delete()
            super().destroy(request, *args, **kwargs)
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            end = time.perf_counter()
            t = end - start
            logger.info("总共用时{}秒".format(t))
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title,
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            res = {
                'success': True,
                'info': "{}(编号为{})成功".format(title, id)
            }
        except Exception as exp:
            res = {
                'success': True,
                'info': "{}(编号为{})失败，原因为：{}".format(title, id, str(exp))
            }
        finally:
            res

        return Response(res)

    # 获取角色列表-sql
    @action(detail=False, methods=['POST'], url_path='sqlsearch')
    def sql_search(self, request):
        sysOperator = SysOperator(connection)

        res = sysOperator.sql_search_role(request)
        return Response(res)


class SysRoleMenuViewSet(viewsets.ModelViewSet):
    queryset = SysRoleMenu.objects.all().order_by('id')
    serializer_class = SysRoleMenuSerializer
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)


class SysUserViewSet(viewsets.ModelViewSet):
    queryset = SysUser.objects.all().order_by('user_id')
    serializer_class = SysUserSerializer
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)


class AuthUserViewSet(viewsets.ModelViewSet):
    queryset = AuthUser.objects.all().order_by('id')
    serializer_class = AuthUserSerializer

    permission_classes = (AllowAny, )
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)

    # 删除用户，同步更新SysUserRole
    def destroy(self, request, *args, **kwargs):
        """
        删除用户的方法。
        该方法接收请求，根据请求中的用户 ID 删除对应的用户信息及用户角色关联信息，并记录操作日志。
        :param request: 请求对象，包含请求相关信息
        :param args: 可变位置参数
        :param kwargs: 可变关键字参数，其中 'pk' 表示要删除用户的 ID
        :return: 返回包含操作结果信息的响应
        """
        self.permission_classes = (IsAuthenticated,)
        # 定义操作标题
        title = "删除用户"
        # 初始化结果信息
        res = ""
        # 从 kwargs 中获取要删除用户的 ID
        id = kwargs["pk"]
        api_path = "/authUser/destroy"
        # 记录操作开始时间
        start = time.perf_counter()
        function_title = "删除用户，请求时间：{}".format(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 删除该用户对应的角色关联信息
            SysUserRole.objects.filter(user_id=id).delete()
            # 删除该用户信息
            AuthUser.objects.filter(id=id).delete()
            # 若需要调用父类的 destroy 方法，可取消注释
            # super().destroy(request, *args, **kwargs)
            # 记录操作结束时间
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的时间戳
            end = time.perf_counter()
            # 计算操作总共用时
            t = end - start
            # 记录操作总用时
            logger.info("总共用时{}秒".format(t))
            function_title += "返回时间：{}".format(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 插入操作日志信息
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title,
                                         request.path,
                                         HttpHelper.get_params_request(request),

                                         t, HttpHelper.get_ip_request(request),function_title)
            # 构建操作成功的结果信息
            res = {
                'success': True,
                'info': "{}(编号为{})成功".format(title, id)
            }
        except Exception as exp:
            # 若操作过程中出现异常，构建操作失败的结果信息
            res = {
                'success': False,
                'info': "{}(编号为{})失败，原因为：{}".format(title, id, str(exp))
            }
            function_title += "返回时间：{}".format(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request,
                                                       function_title, json.dumps(res), exp)
        finally:
            # 此处 'finally' 块中 'res' 单独出现无实际作用，可能需要进一步处理
            res
        # 返回包含操作结果信息的响应
        return Response(res)

    def create(self, request, *args, **kwargs):
        """
        新增用户的方法。
        该方法接收请求，根据请求中的数据创建新用户，并关联用户角色，同时进行用户名唯一性检查和操作日志记录。
        :param request: 请求对象，包含请求相关信息
        :param args: 可变位置参数
        :param kwargs: 可变关键字参数
        :return: 返回包含操作结果信息的响应
        """
        # 记录操作开始时间
        start = time.perf_counter()
        function_title = "新增用户，请求时间：{}".format(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        # 记录操作开始的具体时间
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 从请求数据中获取用户名
            username = request.data["username"]
            # 检查该用户名是否已存在
            if len(AuthUser.objects.filter(username=username)) > 0:
                # 若用户名已存在，构建操作失败的结果信息
                res = {
                    'success': False,
                    'info': "新增的用户名:{}已存在，请换个名称".format(username)
                }
                # 记录操作结束的具体时间
                logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                # 记录操作结束的时间戳
                end = time.perf_counter()
                # 计算操作总共用时
                t = end - start
                # 记录操作总用时
                logger.info("总共用时{}秒".format(t))
                # 插入警告日志信息
                LoggerHelper.insert_log_info(SysLog, request.auth.user, "新增用户重复",
                                             request.path,
                                             HttpHelper.get_params_request(request),
                                             t, HttpHelper.get_ip_request(request),"",3)
                if request.auth == None:
                    request.auth = ""
                    # 插入操作日志信息
                    LoggerHelper.insert_log_info(SysLog, request.auth, "新增用户",
                                                 request.path,
                                                 HttpHelper.get_params_request(request),
                                                 t, HttpHelper.get_ip_request(request))
                    # 返回包含操作失败信息的响应
                else:
                    # 插入操作日志信息
                    LoggerHelper.insert_log_info(SysLog, request.auth.user, "新增用户",
                                                 request.path,
                                                 HttpHelper.get_params_request(request),
                                                 t, HttpHelper.get_ip_request(request))
                    # 返回包含操作失败信息的响应
                return Response(res)
            else:
                # 若用户名不存在，设置用户不是超级用户
                request.data["is_superuser"] = False
                # 若请求数据中没有 'first_name' 字段或其值为 None，设置默认值
                if "first_name" not in request.data or request.data["first_name"] is None:
                    request.data["first_name"] = "null"
                # 若请求数据中没有 'last_name' 字段或其值为 None，设置默认值
                if "last_name" not in request.data or request.data["last_name"] is None:
                    request.data["last_name"] = "null"
                # 为新用户生成唯一 ID
                request.data["id"] = SnowflakeIDUtil.snowflakeId()
                # 设置用户是员工
                request.data["is_staff"] = True
                # 设置用户的创建时间
                request.data["date_joined"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                # 根据用户状态设置用户是否活跃
                request.data["is_active"] = True if int(request.data["status"]) == 1 else False
                # 对用户密码进行加密处理
                request.data["password"] = PasswordHelper.getEncrptPassword(request.data["password"])
                # 设置创建用户的 ID
                if request.auth == None:
                    request.data["create_user_id"] = request.data["id"]
                else:
                    # 设置创建用户的 ID
                    request.data["create_user_id"] = request.auth.user_id
                # 设置创建时间
                request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                if request.auth == None:
                    request.data["modify_user_id"] = request.data["id"]
                else:
                    # 设置修改用户的 ID
                    request.data["modify_user_id"] = request.auth.user_id
                # 设置修改时间
                request.data["modify_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                # 从请求数据中获取角色 ID 列表
                roleIdList = request.data["roleIdList"]
                # 从请求数据中移除角色 ID 列表
                request.data.pop("roleIdList")
                # 创建新用户
                AuthUser.objects.create(**request.data)
                # 若需要调用父类的 create 方法，可取消注释
                # returnuser = super().create(request)
                # 遍历角色 ID 列表，为新用户关联角色
                for role_id in roleIdList:
                    obj = {}
                    obj["id"] = SnowflakeIDUtil.snowflakeId()
                    obj["user_id"] = request.data["id"]
                    obj["role_id"] = role_id
                    SysUserRole.objects.create(**obj)
                # 记录操作结束的具体时间
                logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                # 记录操作结束的时间戳
                end = time.perf_counter()
                # 计算操作总共用时
                t = end - start
                # 记录操作总用时
                logger.info("总共用时{}秒".format(t))

                if request.auth == None:
                    request.auth = ""
                    # 插入操作日志信息
                    LoggerHelper.insert_log_info(SysLog, request.auth, "新增用户",
                                                 request.path,
                                                 HttpHelper.get_params_request(request),
                                                 t, HttpHelper.get_ip_request(request))
                else:
                    # 插入操作日志信息
                    LoggerHelper.insert_log_info(SysLog, request.auth.user, "新增用户",
                                                 request.path,
                                                 HttpHelper.get_params_request(request),
                                                 t, HttpHelper.get_ip_request(request))
                # 构建操作成功的结果信息
                res = {
                    'success': True,
                    'info': "新增用户:{}成功".format(request.data["username"])
                }
                # 返回包含操作成功信息的响应
                return Response(res)
        except Exception as exp:
            # 若操作过程中出现异常，构建操作失败的结果信息
            res = {
                'success': False,
                'info': "新增失败，原因为：{}".format(str(exp))
            }
            # 返回包含操作错误信息的响应
            return Response(res)
    def update(self, request, *args, **kwargs):
        """
        修改用户信息的方法。
        该方法接收请求，根据请求中的数据更新用户信息，同时进行用户名唯一性检查、更新用户角色关联信息，并记录操作日志。
        :param request: 请求对象，包含请求相关信息
        :param args: 可变位置参数
        :param kwargs: 可变关键字参数，其中 'pk' 表示要修改用户的 ID
        :return: 返回包含操作结果信息的响应
        """
        self.permission_classes = (IsAuthenticated,)
        # 记录操作开始时间
        start = time.perf_counter()
        # 记录操作开始的具体时间
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        # 定义操作标题
        title = "修改用户"
        try:
            # 从 kwargs 中获取要修改用户的 ID
            id = kwargs["pk"]
            # 根据 ID 过滤出要修改的用户信息
            afi = AuthUser.objects.filter(id=id)
            if len(afi) > 0:
                # 获取用户 ID
                user_id = afi[0].id
                # 获取用户原用户名
                old_username = afi[0].username
                # 设置用户是否为超级用户
                request.data["is_superuser"] = afi[0].is_superuser
                # 设置用户的名字
                request.data["first_name"] = afi[0].first_name
                # 设置用户的姓氏
                request.data["last_name"] = afi[0].last_name
                # 设置用户是否为员工
                request.data["is_staff"] = afi[0].is_staff
                # 设置用户的创建时间
                request.data["date_joined"] = afi[0].date_joined
                # 若请求中的密码为空，使用原密码；否则对新密码进行加密处理
                if request.data["password"] is None or len(request.data["password"]) == 0:
                #if request.data["password"] is None and len(request.data["password"]) == 0:
                    request.data["password"] = afi[0].password
                else:
                    request.data["password"] = PasswordHelper.getEncrptPassword(request.data["password"])
                # 根据用户状态设置用户是否活跃
                request.data["is_active"] = True if int(request.data["status"]) == 1 else False
                # 设置创建用户的 ID
                request.data["create_user_id"] = afi[0].create_user_id
                # 设置创建时间
                request.data["create_time"] = afi[0].create_time
                # 设置修改用户的 ID
                request.data["modify_user_id"] = request.auth.user_id
                # 设置用户性别
                request.data["sex"] = "男"
                # 设置修改时间
                request.data["modify_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 获取新用户名
            new_username = request.data["username"]
            # 检查新用户名是否已存在（原用户名和新用户名不同时）
            if old_username != new_username and len(AuthUser.objects.filter(username=new_username)) > 0:
                # 若新用户名已存在，构建操作失败的结果信息
                res = {
                    'success': False,
                    'info': "更新的用户名:{}已存在，请换个名称".format(new_username)
                }
                # 记录操作结束的具体时间
                logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                # 记录操作结束的时间戳
                end = time.perf_counter()
                # 计算操作总共用时
                t = end - start
                # 记录操作总用时
                logger.info("总共用时{}秒".format(t))
                # 插入操作日志信息
                LoggerHelper.insert_log_info(SysLog, request.auth.user, "更新用户",
                                             request.path,
                                             HttpHelper.get_params_request(request),
                                             t, HttpHelper.get_ip_request(request))
                # 返回包含操作失败信息的响应
                return Response(res)
            else:
                # 删除该用户对应的角色关联信息
                SysUserRole.objects.filter(user_id=user_id).delete()
                # 遍历角色 ID 列表，为用户重新关联角色
                for role_id in request.data["roleIdList"]:
                    obj = {}
                    obj["id"] = SnowflakeIDUtil.snowflakeId()
                    obj["user_id"] = user_id
                    obj["role_id"] = role_id
                    SysUserRole.objects.create(**obj)
                # 记录操作结束的具体时间
                logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                # 记录操作结束的时间戳
                end = time.perf_counter()
                # 计算操作总共用时
                t = end - start
                # 记录操作总用时
                logger.info("总共用时{}秒".format(t))
                # 插入操作日志信息
                LoggerHelper.insert_log_info(SysLog, request.auth.user, "更新用户",
                                             request.path,
                                             HttpHelper.get_params_request(request),
                                             t, HttpHelper.get_ip_request(request))
                # 调用父类的 update 方法更新用户信息
                super().update(request, *args, **kwargs)
                # 构建操作成功的结果信息
                res = {
                    'success': True,
                    'info': "修改用户成功"
                }
                # 返回包含操作成功信息的响应
                return Response(res)
        except Exception as exp:
            # 若操作过程中出现异常，构建操作失败的结果信息
            res = {
                'success': False,
                'info': "{}(编号为{})失败，原因为：{}".format(title, id, str(exp))
            }


    @action(detail=False, methods=['POST'], url_path='get_user_details', permission_classes = ([IsAuthenticated]))
    # 获取用户详情
    def get_user_details(self, request, *args, **kwargs):
        userid = request.data.get('userid')
        userOperator = UserOperator(connection)
        res = userOperator.get_user_details(request, userid)
        return Response(res)

    # 获取用户列表-sql

    @action(detail=False, methods=['GET'], url_path='sqlsearch', permission_classes = ([IsAuthenticated]))
    def sql_search(self, request):
        userOperator = UserOperator(connection)
        user_name = self.request.query_params.get('user_name')
        full_name = self.request.query_params.get('full_name')
        department_id=self.request.query_params.get('department_id')
        res = userOperator.sql_search(request, user_name, full_name,department_id)
        return Response(res)

    # 获取用户状态列表
    
    @action(detail=False, methods=['GET'], url_path='userstatus', permission_classes = ([IsAuthenticated]))
    def status_list(self, request):
        # log_file_path = os.path.join(settings.LOGGER_ROOT,
        #                              "{}_view.log".format(datetime.datetime.now().strftime('%Y%m%d%H%M%S')))
        # logger.add(log_file_path, format="{time} | {level} | {message}", level="INFO", rotation="50MB")
        start = time.perf_counter()
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        res = {
            'success': True,
            'info': [{'status': '正常', 'code': 1}, {'status': '禁用', 'code': 0}]
        }
        logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        end = time.perf_counter()
        t = end - start
        logger.info("总共用时{}秒".format(t))

        LoggerHelper.insert_log_info(SysLog, request.auth.user, "获取用户状态列表",
                                     request.path,
                                     HttpHelper.get_params_request(request),
                                     t, HttpHelper.get_ip_request(request), "")
        return Response(res)

    # 修改用户状态
    
    @action(detail=False, methods=['POST'], url_path='setstatus', permission_classes = ([IsAuthenticated]))
    def set_status(self, request):
        userOperator = UserOperator(connection)
        res = userOperator.set_status(request)
        return Response(res)


class SysUserRoleViewSet(viewsets.ModelViewSet):
    queryset = SysUserRole.objects.all().order_by('id')
    serializer_class = SysUserRoleSerializer
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)


class SysUserTokenViewSet(viewsets.ModelViewSet):
    queryset = SysUserToken.objects.all().order_by('user_id')
    serializer_class = SysUserTokenSerializer
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)


# 数据字典相关操作
class SysDictViewSet(viewsets.ModelViewSet):
    # 设置视图集的权限类，要求用户必须通过认证
    permission_classes = (IsAuthenticated,)
    # 自定义 token 认证，使用 ExpiringTokenAuthentication 类进行认证
    authentication_classes = (ExpiringTokenAuthentication,)

    # 获取数据字典类别下拉
    @action(detail=False, methods=['GET'], url_path='cateloglist')
    def get_dict_catelog_list(self, request, *args, **kwargsst):
        """
        获取数据字典类别下拉列表的接口方法。
        该方法接收 GET 请求，调用 SysOperator 类的方法获取数据字典类别列表。
        :param request: 请求对象，包含客户端的请求信息
        :param args: 可变位置参数
        :param kwargsst: 可变关键字参数
        :return: 返回包含数据字典类别列表的响应，若出现异常则返回失败结果
        """
        # 定义操作的功能标题，用于日志记录和结果反馈
        function_title = "获取数据字典类别下拉"
        # 调用日志工具类的方法，记录操作开始的日志信息，如开始时间等
        start = LoggerHelper.set_start_log_info(logger)
        # 获取当前请求的 API 路径，用于日志记录
        api_path = request.path
        try:
            # 创建 SysOperator 类的实例，传入数据库连接对象
            sysOperator = SysOperator(connection)
            # 调用 SysOperator 类的 get_dict_catelog_list 方法，获取数据字典类别列表
            res = sysOperator.get_dict_catelog_list(request, function_title)
            # 返回包含数据字典类别列表的响应
            return Response(res)
        except Exception as exp:
            # 若操作过程中出现异常，调用日志工具类的方法，记录操作异常结束的日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, str(exp), None)
            # 构建操作失败的消息
            msg = "{}失败".format(function_title)
            # 返回失败结果，包含失败消息和异常信息
            return Result.fail(msg, str(exp))

    # SQL 查询获取数据字典列表
    @action(detail=False, methods=['GET'], url_path='sqlsearch')
    def get_dict_list_by_sql(self, request, *args, **kwargsst):
        """
        通过 SQL 查询获取数据字典列表的接口方法。
        该方法接收 GET 请求，从请求参数中获取数据字典类别 ID，调用 SysOperator 类的方法进行查询。
        :param request: 请求对象，包含客户端的请求信息
        :param args: 可变位置参数
        :param kwargsst: 可变关键字参数
        :return: 返回包含数据字典列表的响应，若出现异常则返回失败结果
        """
        # 定义操作的功能标题，用于日志记录和结果反馈
        function_title = "查询获取数据字典列表"
        # 调用日志工具类的方法，记录操作开始的日志信息，如开始时间等
        start = LoggerHelper.set_start_log_info(logger)
        # 获取当前请求的 API 路径，用于日志记录
        api_path = request.path
        try:
            # 创建 SysOperator 类的实例，传入数据库连接对象
            sysOperator = SysOperator(connection)
            # 从请求的查询参数中获取数据字典类别 ID，若不存在则为空字符串
            dict_catelog_id = self.request.query_params.get('dict_catelog_id', '')
            # 调用 SysOperator 类的 sql_search_dict 方法，根据数据字典类别 ID 查询数据字典列表
            res = sysOperator.sql_search_dict(request, dict_catelog_id, function_title)
            # 返回包含数据字典列表的响应
            return Response(res)
        except Exception as exp:
            # 若操作过程中出现异常，调用日志工具类的方法，记录操作异常结束的日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, str(exp), None)
            # 构建操作失败的消息
            msg = "{}失败".format(function_title)
            # 返回失败结果，包含失败消息和异常信息
            return Result.fail(msg, str(exp))

    # 通过编号获取数据字典详情
    @action(detail=False, methods=['GET'], url_path='getdetail')
    def get_detail_by_condition(self, request, *args, **kwargsst):
        """
        通过编号获取数据字典详情的接口方法。
        该方法接收 GET 请求，从请求参数中获取数据字典类别 ID 和数据字典 ID，调用 SysOperator 类的方法获取详情。
        :param request: 请求对象，包含客户端的请求信息
        :param args: 可变位置参数
        :param kwargsst: 可变关键字参数
        :return: 返回包含数据字典详情的响应，若出现异常则返回失败结果
        """
        # 定义操作的功能标题，用于日志记录和结果反馈
        function_title = "通过编号获取数据字典详情"
        # 调用日志工具类的方法，记录操作开始的日志信息，如开始时间等
        start = LoggerHelper.set_start_log_info(logger)
        # 获取当前请求的 API 路径，用于日志记录
        api_path = request.path
        try:
            # 创建 SysOperator 类的实例，传入数据库连接对象
            sysOperator = SysOperator(connection)
            # 从请求的查询参数中获取数据字典类别 ID，若不存在则为空字符串
            dict_catelog_id = self.request.query_params.get('dict_catelog_id', '')
            # 从请求的查询参数中获取数据字典 ID，若不存在则为空字符串
            id = self.request.query_params.get('id', '')
            # 调用 SysOperator 类的 get_detail_by_condition 方法，根据数据字典类别 ID 和数据字典 ID 获取详情
            res = sysOperator.get_detail_by_condition(request, dict_catelog_id, id, function_title)
            # 返回包含数据字典详情的响应
            return Response(res)
        except Exception as exp:
            # 若操作过程中出现异常，调用日志工具类的方法，记录操作异常结束的日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, str(exp), None)
            # 构建操作失败的消息
            msg = "{}失败".format(function_title)
            # 返回失败结果，包含失败消息和异常信息
            return Result.fail(msg, str(exp))

    # 添加数据字典
    @action(detail=False, methods=['POST'], url_path='add')
    def add_dict_by_catelog(self, request, *args, **kwargsst):
        """
        添加数据字典的接口方法。
        该方法接收 POST 请求，调用 SysOperator 类的方法添加数据字典。
        :param request: 请求对象，包含客户端要添加的数据字典信息
        :param args: 可变位置参数
        :param kwargsst: 可变关键字参数
        :return: 返回添加操作的结果响应，若出现异常则返回失败结果
        """
        # 定义操作的功能标题，用于日志记录和结果反馈
        function_title = "添加数据字典"
        # 调用日志工具类的方法，记录操作开始的日志信息，如开始时间等
        start = LoggerHelper.set_start_log_info(logger)
        # 获取当前请求的 API 路径，用于日志记录
        api_path = request.path
        try:
            # 创建 SysOperator 类的实例，传入数据库连接对象
            sysOperator = SysOperator(connection)
            # 调用 SysOperator 类的 add_dict 方法，添加数据字典
            res = sysOperator.add_dict(request, function_title)
            # 返回添加操作的结果响应
            return Response(res)
        except Exception as exp:
            # 若操作过程中出现异常，调用日志工具类的方法，记录操作异常结束的日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, str(exp), None)
            # 构建操作失败的消息
            msg = "{}失败".format(function_title)
            # 返回失败结果，包含失败消息和异常信息
            return Result.fail(msg, str(exp))

    # 修改加数据字典
    @action(detail=False, methods=['POST'], url_path='update')
    def update_dict_by_catelog(self, request, *args, **kwargsst):
        """
        修改数据字典的接口方法。
        该方法接收 POST 请求，调用 SysOperator 类的方法修改数据字典。
        :param request: 请求对象，包含客户端要修改的数据字典信息
        :param args: 可变位置参数
        :param kwargsst: 可变关键字参数
        :return: 返回修改操作的结果响应，若出现异常则返回失败结果
        """
        # 定义操作的功能标题，用于日志记录和结果反馈
        function_title = "修改数据字典"
        # 调用日志工具类的方法，记录操作开始的日志信息，如开始时间等
        start = LoggerHelper.set_start_log_info(logger)
        # 获取当前请求的 API 路径，用于日志记录
        api_path = request.path
        try:
            # 创建 SysOperator 类的实例，传入数据库连接对象
            sysOperator = SysOperator(connection)
            # 调用 SysOperator 类的 update_dict 方法，修改数据字典
            res = sysOperator.update_dict(request, function_title)
            # 返回修改操作的结果响应
            return Response(res)
        except Exception as exp:
            # 若操作过程中出现异常，调用日志工具类的方法，记录操作异常结束的日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, str(exp), None)
            # 构建操作失败的消息
            msg = "{}失败".format(function_title)
            # 返回失败结果，包含失败消息和异常信息
            return Result.fail(msg, str(exp))

    # 删除数据字典
    @action(detail=False, methods=['POST'], url_path='delete')
    def delete_dict_by_catelog(self, request, *args, **kwargsst):
        """
        删除数据字典的接口方法。
        该方法接收 POST 请求，调用 SysOperator 类的方法删除数据字典。
        :param request: 请求对象，包含客户端要删除的数据字典信息
        :param args: 可变位置参数
        :param kwargsst: 可变关键字参数
        :return: 返回删除操作的结果响应，若出现异常则返回失败结果
        """
        # 定义操作的功能标题，用于日志记录和结果反馈
        function_title = "删除数据字典"
        # 调用日志工具类的方法，记录操作开始的日志信息，如开始时间等
        start = LoggerHelper.set_start_log_info(logger)
        # 获取当前请求的 API 路径，用于日志记录
        api_path = request.path
        try:
            # 创建 SysOperator 类的实例，传入数据库连接对象
            sysOperator = SysOperator(connection)
            # 调用 SysOperator 类的 delete_dict 方法，删除数据字典
            res = sysOperator.delete_dict(request, function_title)
            # 返回删除操作的结果响应
            return Response(res)
        except Exception as exp:
            # 若操作过程中出现异常，调用日志工具类的方法，记录操作异常结束的日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, str(exp), None)
            # 构建操作失败的消息
            msg = "{}失败".format(function_title)
            # 返回失败结果，包含失败消息和异常信息
            return Result.fail(msg, str(exp))


# 系统消息相关操作
class SysMessageViewSet(viewsets.ModelViewSet):
    # 设置视图集的权限类，要求用户必须通过认证
    permission_classes = (IsAuthenticated,)
    # 自定义 token 认证，使用 ExpiringTokenAuthentication 类进行认证
    authentication_classes = (ExpiringTokenAuthentication,)

    # SQL查询获取消息列表
    @action(detail=False, methods=['GET'], url_path='sqlsearch')
    def get_message_list_by_sql(self, request, *args, **kwargsst):
        """
        通过 SQL 查询获取消息列表的接口方法。
        该方法接收 GET 请求，从请求参数中获取用户名、查询开始时间和查询结束时间，
        调用 SysOperator 类的方法进行消息列表的查询。
        :param request: 请求对象，包含客户端的请求信息
        :param args: 可变位置参数
        :param kwargsst: 可变关键字参数
        :return: 返回包含消息列表的响应，若出现异常则返回失败结果
        """
        # 定义操作的功能标题，用于日志记录和结果反馈
        function_title = "查询获取消息列表"
        # 调用日志工具类的方法，记录操作开始的日志信息，如开始时间等
        start = LoggerHelper.set_start_log_info(logger)
        # 获取当前请求的 API 路径，用于日志记录
        api_path = request.path
        try:
            # 创建 SysOperator 类的实例，传入数据库连接对象
            sysOperator = SysOperator(connection)
            # 从请求的查询参数中获取用户名，若不存在则为空字符串
            username = self.request.query_params.get('username', '')
            # 从请求的查询参数中获取查询开始时间，若不存在则为空字符串
            querystarttime = self.request.query_params.get('querystarttime', '')
            # 从请求的查询参数中获取查询结束时间，若不存在则为空字符串
            queryendtime = self.request.query_params.get('queryendtime', '')
            # 调用 SysOperator 类的 sql_search_message 方法，根据参数查询消息列表
            res = sysOperator.sql_search_message(request, username, querystarttime, queryendtime, function_title)
            # 返回包含消息列表的响应
            return Response(res)
        except Exception as exp:
            # 若操作过程中出现异常，调用日志工具类的方法，记录操作异常结束的日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, str(exp), None)
            # 构建操作失败的消息
            msg = "{}失败".format(function_title)
            # 返回失败结果，包含失败消息和异常信息
            return Result.fail(msg, str(exp))

    # 删除数用户消息
    @action(detail=False, methods=['POST'], url_path='delete')
    def delete_message_by_id(self, request, *args, **kwargsst):
        """
        删除用户消息的接口方法。
        该方法接收 POST 请求，调用 SysOperator 类的方法删除用户消息。
        :param request: 请求对象，包含客户端要删除的消息信息
        :param args: 可变位置参数
        :param kwargsst: 可变关键字参数
        :return: 返回删除操作的结果响应，若出现异常则返回失败结果
        """
        # 定义操作的功能标题，用于日志记录和结果反馈
        function_title = "删除用户消息"
        # 调用日志工具类的方法，记录操作开始的日志信息，如开始时间等
        start = LoggerHelper.set_start_log_info(logger)
        # 获取当前请求的 API 路径，用于日志记录
        api_path = request.path
        try:
            # 创建 SysOperator 类的实例，传入数据库连接对象
            sysOperator = SysOperator(connection)
            # 调用 SysOperator 类的 delete_message 方法，删除用户消息
            res = sysOperator.delete_message(request, function_title)
            # 返回删除操作的结果响应
            return Response(res)
        except Exception as exp:
            # 若操作过程中出现异常，调用日志工具类的方法，记录操作异常结束的日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, str(exp), None)
            # 构建操作失败的消息
            msg = "{}失败".format(function_title)
            # 返回失败结果，包含失败消息和异常信息
            return Result.fail(msg, str(exp))


# 系统参数相关操作
class SysParamViewSet(viewsets.ModelViewSet):
    # 查询集，获取 SysParam 模型的所有对象，并按 id 排序
    queryset = SysParam.objects.all().order_by('id')
    # 序列化器类，用于将 SysParam 模型对象序列化为 JSON 数据
    serializer_class = SysParamSerializer
    # 设置视图集的权限类，要求用户必须通过认证
    permission_classes = (IsAuthenticated,)
    # 自定义 token 认证，使用 ExpiringTokenAuthentication 类进行认证
    authentication_classes = (ExpiringTokenAuthentication,)

    def list(self, request, *args, **kwargs):
        """
        获取系统参数列表的方法。
        该方法从数据库中获取所有系统参数对象，并将其序列化后返回。
        :param request: 请求对象，包含客户端的请求信息
        :param args: 可变位置参数
        :param kwargs: 可变关键字参数
        :return: 返回包含系统参数列表的响应
        """
        # 从数据库中获取所有 SysParam 对象，并按 id 排序
        results = SysParam.objects.all().order_by('id')
        # 初始化一个空列表，用于存储序列化后的数据
        data = []
        # 遍历所有 SysParam 对象
        for result in results:
            # 将每个对象进行序列化，并将序列化后的数据添加到 data 列表中
            data.append(SysParamSerializer(result).data)
        # 构建包含系统参数列表的结果字典
        results = {'results': data}
        # 返回包含系统参数列表的响应
        return Response(results)

    def create(self, request, *args, **kwargs):
        """
        新增系统参数的方法。
        该方法接收 POST 请求，检查参数英文名是否已存在，若不存在则创建新的系统参数。
        :param request: 请求对象，包含客户端要新增的系统参数信息
        :param args: 可变位置参数
        :param kwargs: 可变关键字参数
        :return: 返回新增操作的结果响应，若参数英文名已存在则返回失败结果
        """
        # 定义操作的功能标题，用于日志记录和结果反馈
        function_title = "新增参数"
        # 调用日志工具类的方法，记录操作开始的日志信息，如开始时间等
        start = LoggerHelper.set_start_log_info(logger)
        # 获取当前请求的 API 路径，用于日志记录
        api_path = request.path
        # 从请求数据中获取参数英文名
        param_en_key = request.data["param_en_key"]
        # 检查该参数英文名是否已存在
        if len(SysParam.objects.filter(param_en_key=param_en_key)) > 0:
            # 若已存在，构建错误信息
            error_info = "新增的参数英文名:{}已存在，请换个名称".format(param_en_key)
            # 调用日志工具类的方法，记录操作异常结束的日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, error_info, None)
            # 返回失败结果，包含错误信息
            return Result.fail(error_info, error_info)
        else:
            # 若不存在，为新参数生成唯一 ID
            request.data["id"] = SnowflakeIDUtil.snowflakeId()
            # 设置创建用户的 ID
            request.data["create_user_id"] = request.auth.user_id
            # 设置创建时间
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 创建新的系统参数对象
            SysParam.objects.create(**request.data)
            # 若需要调用父类的 create 方法，可取消注释
            # super().create(request)
            # 调用日志工具类的方法，记录操作正常结束的日志信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            # 构建操作成功的消息
            msg = "{}成功".format(function_title)
            # 返回成功结果，包含成功消息
            return Result.sucess(msg, None)

    def update(self, request, *args, **kwargs):
        """
        修改系统参数的方法。
        该方法接收 POST 请求，检查新的参数英文名是否已存在，若不存在则更新系统参数。
        :param request: 请求对象，包含客户端要修改的系统参数信息
        :param args: 可变位置参数
        :param kwargs: 可变关键字参数
        :return: 返回修改操作的结果响应，若新的参数英文名已存在则返回失败结果
        """
        # 定义操作的功能标题，用于日志记录和结果反馈
        function_title = "修改参数"
        # 调用日志工具类的方法，记录操作开始的日志信息，如开始时间等
        start = LoggerHelper.set_start_log_info(logger)
        # 获取当前请求的 API 路径，用于日志记录
        api_path = request.path
        # 从 kwargs 中获取要修改的系统参数的 ID
        id = kwargs["pk"]
        # 检查该 ID 的系统参数是否存在
        if len(SysParam.objects.filter(id=id)) > 0:
            # 若存在，获取原参数英文名
            old_param_en_key = SysParam.objects.filter(id=id)[0].param_en_key
        # 从请求数据中获取新的参数英文名
        new_param_en_key = request.data["param_en_key"]
        # 检查原参数英文名和新参数英文名是否不同，且新参数英文名是否已存在
        if old_param_en_key != new_param_en_key and len(SysParam.objects.filter(param_en_key=new_param_en_key)) > 0:
            # 若已存在，构建错误信息
            error_info = "更新的参数英文名:{}已存在，请换个名称".format(new_param_en_key)
            # 调用日志工具类的方法，记录操作异常结束的日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, error_info, None)
            # 返回失败结果，包含错误信息
            return Result.fail(error_info, error_info)
        else:
            # 若不存在，设置更新用户的 ID
            request.data["update_user_id"] = request.auth.user_id
            # 设置更新时间
            request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 调用父类的 update 方法，更新系统参数
            super().update(request, *args, **kwargs)
            # 调用日志工具类的方法，记录操作正常结束的日志信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            # 构建操作成功的消息
            msg = "{}成功".format(function_title)
            # 返回成功结果，包含成功消息
            return Result.sucess(msg, None)

    # 删除参数
    def destroy(self, request, *args, **kwargs):
        """
        删除系统参数的方法。
        该方法接收请求，根据参数 ID 删除对应的系统参数，并记录操作日志。
        :param request: 请求对象，包含客户端的请求信息
        :param args: 可变位置参数
        :param kwargs: 可变关键字参数，其中 'pk' 表示要删除的系统参数的 ID
        :return: 返回删除操作的结果响应，若出现异常则返回失败结果
        """
        # 定义操作的功能标题，用于日志记录和结果反馈
        title = "删除参数"
        # 初始化结果信息
        res = ""
        # 从 kwargs 中获取要删除的系统参数的 ID
        id = kwargs["pk"]
        # 记录操作开始时间
        start = time.perf_counter()
        try:
            # 调用父类的 destroy 方法，删除系统参数
            super().destroy(request, *args, **kwargs)
            # 记录操作结束的具体时间
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的时间戳
            end = time.perf_counter()
            # 计算操作总共用时
            t = end - start
            # 记录操作总用时
            logger.info("总共用时{}秒".format(t))
            # 插入操作日志信息
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title,
                                         request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
            # 构建操作成功的结果信息
            res = {
                'success': True,
                'info': "{}(编号为{})成功".format(title, id)
            }
        except Exception as exp:
            # 若操作过程中出现异常，构建操作失败的结果信息
            res = {
                'success': True,
                'info': "{}(编号为{})失败，原因为：{}".format(title, id, str(exp))
            }
        finally:
            # 此处 'finally' 块中 'res' 单独出现无实际作用，可能需要进一步处理
            res
        # 返回包含操作结果信息的响应
        return Response(res)
