<template>
  <!-- 事件办结 -->
  <ScreenCard title="事件办结" icon="mdi:comment-check">
    <template #header-control>
      <ControlButton
        :active="activeType === 'type'"
        label="类型"
        @click="handleEventTypeChange('type')"
      />
      <ControlButton
        :active="activeType === 'location'"
        label="归属地"
        @click="handleEventTypeChange('location')"
      />
    </template>
    <div class="event-content">
      <!-- 这里可以放置事件办结的图表或数据 -->
      <div v-if="activeType === 'type'" class="event-table">
        <!-- 表头 -->
        <div class="table-header">
          <div class="col-name">事件类型</div>
          <div class="col-accept">受理量</div>
          <div class="col-cooperate">协同量</div>
          <div class="col-rate">受理率</div>
        </div>

        <!-- 表格内容 -->
        <div class="table-body">
          <div v-for="item in currentEventList" :key="item.name" class="table-row">
            <div class="col-name">{{ item.name }}</div>
            <div class="col-accept">
              <div class="progress-container">
                <div class="accept-count">{{ item.acceptCount }}</div>
                <div
                  class="progress-inner"
                  :style="{ width: `${item.acceptCount * 10}%`, backgroundColor: item.color }"
                ></div>
              </div>
            </div>
            <div class="col-cooperate">{{ item.cooperateCount }}</div>
            <div class="col-rate">{{ item.percentage }}%</div>
          </div>
        </div>
      </div>

      <div v-else-if="activeType === 'location'" class="event-table">
        <!-- 表头 -->
        <div class="table-header">
          <div class="col-name">归属地</div>
          <div class="col-accept">受理量</div>
          <div class="col-cooperate">协同量</div>
          <div class="col-rate">受理率</div>
        </div>

        <!-- 表格内容 -->
        <div class="table-body">
          <div v-for="item in currentEventList" :key="item.name" class="table-row">
            <div class="col-name">{{ item.name }}</div>
            <div class="col-accept">
              <div class="progress-container">
                <div
                  class="progress-inner"
                  :style="{
                    width: `${item.acceptCount * 6}%`,
                    backgroundColor: getColorByPercentage(item.percentage),
                  }"
                ></div>
                <div class="accept-count">{{ item.acceptCount }}</div>
              </div>
            </div>
            <div class="col-cooperate">{{ item.cooperateCount }}</div>
            <div class="col-rate">{{ item.percentage }}%</div>
          </div>
        </div>
      </div>
    </div>
  </ScreenCard>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ScreenCard from '@/components/common/ScreenCard/ScreenCard.vue'
import ControlButton from '@/components/common/ControlButton/ControButton.vue'
import type { EventCompletionUIData } from '@/types/ui/stats'

// ===== Props =====
interface Props {
  data: EventCompletionUIData
}

const props = defineProps<Props>()

// ===== 本地状态 =====

// 当前激活的类型（在组件内部管理）
const activeType = ref<'type' | 'location'>('type')

// ===== 计算属性 =====

// 当前显示的事件列表
const currentEventList = computed(() => {
  return activeType.value === 'type' ? props.data.typeData : props.data.locationData
})

// ===== 工具函数 =====

// 根据百分比获取颜色
const getColorByPercentage = (percentage: number) => {
  if (percentage >= 90) return '#67C23A' // 绿色（良好）
  if (percentage >= 70) return '#E6A23C' // 黄色（一般）
  return '#F56C6C' // 红色（较差）
}

// ===== 事件处理 =====

// 切换激活的按钮类型
const handleEventTypeChange = (type: 'type' | 'location') => {
  console.log(`切换到${type}类型`)
  activeType.value = type
}
</script>

<style scoped lang="scss">
.event-content {
  padding: 0.3rem;
  // height: 100%;
  overflow-y: auto;
  font-size: 0.6rem;
  // max-height: 10rem;

  .event-table {
    width: 100%;

    .table-header {
      display: flex;
      background-color: rgba(0, 255, 254, 0.08);
      padding: 0.2rem 0;
      margin-bottom: 0.2rem;
      border-radius: 2px;
      color: rgba(255, 255, 255, 0.8);
      font-weight: 500;
      height: 1.2rem;
      align-items: center;
    }

    .table-body {
      display: flex;
      flex-direction: column;
      gap: 0.15rem;
    }

    .table-row {
      display: flex;
      padding: 0.1rem 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(0, 255, 254, 0.08);
        transform: translateX(2px);
        box-shadow: -2px 0 0 #00fffe;
      }
    }

    .col-name {
      flex: 1.5;
      padding-left: 0.2rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: flex;
      align-items: center;
      height: 1.2rem;
    }

    .col-accept {
      position: relative;
      flex: 1.5;
      display: flex;
      align-items: center;
      height: 1.2rem;
      .progress-container {
        position: relative;
        flex: 1;
        height: 0.5rem;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 0.2rem;
        overflow: hidden;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;

        .progress-inner {
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          border-radius: 0.2rem;
          opacity: 0.3;
          transition: all 0.3s ease;
          z-index: 0;
        }

        .accept-count {
          position: absolute;
          color: #00fffe;
          font-weight: bold;
          font-size: 0.6rem;
          z-index: 1;
          left: 0.1rem;
          transition: all 0.3s ease;
          text-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
        }
      }

      &:hover {
        .progress-container {
          .progress-inner {
            opacity: 0.5;
          }

          .accept-count {
            transform: scale(1.1);
          }
        }
      }
    }

    .col-cooperate {
      flex: 1;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 1.2rem;
    }

    .col-rate {
      flex: 1;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 1.2rem;
    }
  }
}
</style>
