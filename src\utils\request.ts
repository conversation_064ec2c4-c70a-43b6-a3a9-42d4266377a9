import axios from 'axios'
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios'
import { ElMessageBox } from 'element-plus'

// 处理401未授权错误
const handleUnauthorized = async () => {
  try {
    await ElMessageBox.confirm('您的登录状态已过期或无效，是否退出重新登录？', '登录状态异常', {
      confirmButtonText: '确认退出',
      cancelButtonText: '取消',
      type: 'warning',
      center: true,
    })

    // 用户确认退出，清除本地认证信息并跳转到登录页
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('loginTime')

    // 如果在浏览器环境中，跳转到登录页
    if (typeof window !== 'undefined') {
      window.location.href = '/login'
    }
  } catch {
    // 用户取消，什么也不做
    console.log('用户取消退出登录')
  }
}

// 创建 axios 实例
const request: AxiosInstance = axios.create({
  // API 请求的默认前缀
  baseURL: import.meta.env.VITE_API_BASE_URL || '/proxy_api',
  // 请求超时时间
  timeout: 30000,
  // 请求头
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
})

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在发送请求之前做些什么
    // 例如：获取并设置 token
    const token = localStorage.getItem('token')
    if (token) {
      // config.headers['Authorization'] = `Bearer ${token}`
      config.headers['Authorization'] = `Token ${token}`
    }
    return config
  },
  (error) => {
    // 对请求错误做些什么
    console.error('❌ [Request Error]:', error)
    return Promise.reject(error)
  },
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    // 对响应数据做点什么
    console.log(`📥 [HTTP Response] ${response.status} ${response.config.url}`)
    console.log(`📦 Response Data:`, response.data)

    const res = response.data

    // 直接返回响应数据，不做业务逻辑判断
    // 让各个接口自己处理返回结构
    return res
  },
  (error) => {
    // 对响应错误做点什么
    // 打印错误信息
    console.error('响应错误:', error)
    if (error.response) {
      const { status } = error.response
      // 根据状态码处理错误
      switch (status) {
        case 401:
          // 未授权，询问用户是否退出重新登录
          console.error('未授权，请重新登录')
          handleUnauthorized()
          break
        case 403:
          // 拒绝访问
          console.error('拒绝访问')
          break
        case 404:
          // 请求不存在
          console.error('请求的资源不存在')
          break
        case 500:
          // 服务器错误
          console.error('服务器错误')
          break
        default:
          console.error(`请求错误: ${error.message}`)
      }
    } else {
      console.error(`网络错误: ${error.message}`)
    }
    return Promise.reject(error)
  },
)

// GET 请求方法
export function get<T = any>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> {
  return request.get(url, { params, ...config })
}

// POST 请求方法
export function post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return request.post(url, data, config)
}

// PUT 请求方法
export function put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return request.put(url, data, config)
}

// DELETE 请求方法
export function del<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
  return request.delete(url, config)
}

// 上传文件方法
export function upload<T = any>(
  url: string,
  file: File | FormData,
  config?: AxiosRequestConfig,
): Promise<T> {
  const formData = file instanceof FormData ? file : new FormData()
  if (!(file instanceof FormData)) {
    formData.append('file', file)
  }

  return request.post(url, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    ...config,
  })
}

// 导出 axios 实例
export default request
