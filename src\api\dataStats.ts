/**
 * 数据统计相关API接口
 */
import { get } from '@/utils/request'
import type {
  InspectionFrequencyUIData,
  EventCompletionUIData,
  CostReductionUIData,
  DataAchievementUIData,
  DateRange,
} from '@/types/ui'

/**
 * 数据统计API
 */
export const dataStatsApi = {
  /**
   * 获取巡检频次数据
   * @param dateRange 时间范围
   */
  getInspectionFrequencyData: (dateRange: DateRange): Promise<InspectionFrequencyUIData> => {
    // 暂时返回模拟数据，后期对接真实API
    return Promise.resolve({
      timesData: {
        dailyAverage: 8.6,
        totalCount: 60,
        chartData: [
          { name: '2025-03-27', value: 10 },
          { name: '2025-03-28', value: 5 },
          { name: '2025-03-29', value: 20 },
          { name: '2025-03-30', value: 5 },
          { name: '2025-03-31', value: 3 },
          { name: '2025-04-01', value: 15 },
          { name: '2025-04-02', value: 8 },
        ],
      },
      mileageData: {
        dailyAverage: 125.8,
        totalCount: 881,
        chartData: [
          { name: '2025-03-27', value: 150 },
          { name: '2025-03-28', value: 80 },
          { name: '2025-03-29', value: 200 },
          { name: '2025-03-30', value: 90 },
          { name: '2025-03-31', value: 60 },
          { name: '2025-04-01', value: 180 },
          { name: '2025-04-02', value: 120 },
        ],
      },
      durationData: {
        dailyAverage: 4.2,
        totalCount: 29.4,
        chartData: [
          { name: '2025-03-27', value: 5.5 },
          { name: '2025-03-28', value: 2.8 },
          { name: '2025-03-29', value: 6.2 },
          { name: '2025-03-30', value: 3.1 },
          { name: '2025-03-31', value: 2.0 },
          { name: '2025-04-01', value: 5.8 },
          { name: '2025-04-02', value: 4.0 },
        ],
      },
    })
  },

  /**
   * 获取事件办结数据
   * @param dateRange 时间范围
   */
  getEventCompletionData: (dateRange: DateRange): Promise<EventCompletionUIData> => {
    return Promise.resolve({
      typeData: [
        {
          name: '松材线虫病',
          acceptCount: 10,
          cooperateCount: 10,
          percentage: 100,
          color: '#409EFF',
        },
        {
          name: '疫情垃圾',
          acceptCount: 6,
          cooperateCount: 6,
          percentage: 100,
          color: '#67C23A',
        },
        {
          name: '流动摊贩',
          acceptCount: 4,
          cooperateCount: 4,
          percentage: 100,
          color: '#E6A23C',
        },
        {
          name: '渣土乱倒',
          acceptCount: 4,
          cooperateCount: 4,
          percentage: 100,
          color: '#F56C6C',
        },
      ],
      locationData: [
        {
          name: '柯城区',
          acceptCount: 15,
          cooperateCount: 15,
          percentage: 100,
          color: '#409EFF',
        },
        { name: '衢江区', acceptCount: 8, cooperateCount: 8, percentage: 100, color: '#67C23A' },
        { name: '龙游县', acceptCount: 6, cooperateCount: 6, percentage: 100, color: '#E6A23C' },
      ],
    })
  },

  /**
   * 获取降本增效数据
   * @param dateRange 时间范围
   */
  getCostReductionData: (dateRange: DateRange): Promise<CostReductionUIData> => {
    return Promise.resolve({
      items: [
        { value: 60, unit: '人次', label: '代替人工' },
        { value: 6.7, unit: '万元', label: '节约成本' },
        { value: 0.06, unit: '吨', label: '减少碳排' },
      ],
    })
  },

  /**
   * 获取数据成果数据
   * @param dateRange 时间范围
   */
  getDataAchievementData: (dateRange: DateRange): Promise<DataAchievementUIData> => {
    return Promise.resolve({
      topRowData: [
        { title: '照片采集数', value: 30542, unit: '', icon: 'mdi:camera' },
        { title: '视频采集数', value: 4735, unit: '', icon: 'mdi:video' },
      ],
      bottomRowData: [
        { title: '数据存储量', value: 2.5, unit: 'TB', icon: 'mdi:database' },
        { title: '处理时长', value: 156, unit: '小时', icon: 'mdi:clock' },
        { title: '识别准确率', value: 95.8, unit: '%', icon: 'mdi:target' },
        { title: '响应速度', value: 2.3, unit: '秒', icon: 'mdi:speedometer' },
      ],
    })
  },

  /**
   * 获取所有数据统计数据
   * @param dateRange 时间范围
   */
  getAllDataStats: async (dateRange: DateRange) => {
    const [inspectionData, eventData, costData, achievementData] = await Promise.all([
      dataStatsApi.getInspectionFrequencyData(dateRange),
      dataStatsApi.getEventCompletionData(dateRange),
      dataStatsApi.getCostReductionData(dateRange),
      dataStatsApi.getDataAchievementData(dateRange),
    ])

    return {
      inspectionData,
      eventData,
      costData,
      achievementData,
    }
  },
}
