/**
 * 地图状态管理 Store
 * 管理地图实例、图层、绘制状态等核心地图相关状态
 */
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// 地图视图模式枚举
export type MapViewMode = '2D' | '3D'

// 底图类型枚举
export type BaseMapType = 'satellite' | 'terrain' | 'street' | 'dark'

// 图层类型枚举
export type LayerType = 'drone' | 'task' | 'warning' | 'route' | 'area'

// 图层信息接口
export interface LayerInfo {
  id: string
  name: string
  type: LayerType
  visible: boolean
  opacity: number
  zIndex: number
  data?: any
}

// 绘制状态枚举
export type DrawingState = 'none' | 'point' | 'line' | 'polygon' | 'rectangle' | 'circle'

// 地图配置接口
export interface MapConfig {
  center: {
    longitude: number
    latitude: number
  }
  zoom: number
  pitch: number
  bearing: number
}

export const useMapStore = defineStore('map', () => {
  // ===== 状态定义 =====
  
  // 地图实例
  const mapInstance = ref<any>(null)
  
  // 地图视图模式
  const viewMode = ref<MapViewMode>('2D')
  
  // 底图类型
  const baseMapType = ref<BaseMapType>('satellite')
  
  // 地图配置
  const mapConfig = ref<MapConfig>({
    center: {
      longitude: 116.397428,
      latitude: 39.90923
    },
    zoom: 10,
    pitch: 0,
    bearing: 0
  })
  
  // 图层列表
  const layers = ref<LayerInfo[]>([])
  
  // 绘制状态
  const drawingState = ref<DrawingState>('none')
  
  // 地图加载状态
  const isMapLoading = ref(false)
  
  // 地图初始化状态
  const isMapInitialized = ref(false)
  
  // 当前选中的要素
  const selectedFeature = ref<any>(null)
  
  // 坐标拾取状态
  const isPickingCoordinate = ref(false)
  
  // ===== 计算属性 =====
  
  // 可见图层列表
  const visibleLayers = computed(() => {
    return layers.value.filter(layer => layer.visible)
  })
  
  // 图层统计
  const layerStats = computed(() => {
    const stats = {
      total: layers.value.length,
      visible: 0,
      hidden: 0,
    }
    
    layers.value.forEach(layer => {
      if (layer.visible) {
        stats.visible++
      } else {
        stats.hidden++
      }
    })
    
    return stats
  })
  
  // 是否处于绘制状态
  const isDrawing = computed(() => {
    return drawingState.value !== 'none'
  })

  // ===== Actions =====
  
  /**
   * 设置地图实例
   */
  const setMapInstance = (instance: any) => {
    mapInstance.value = instance
    isMapInitialized.value = true
  }
  
  /**
   * 设置视图模式
   */
  const setViewMode = (mode: MapViewMode) => {
    viewMode.value = mode
  }
  
  /**
   * 设置底图类型
   */
  const setBaseMapType = (type: BaseMapType) => {
    baseMapType.value = type
  }
  
  /**
   * 更新地图配置
   */
  const updateMapConfig = (config: Partial<MapConfig>) => {
    mapConfig.value = { ...mapConfig.value, ...config }
  }
  
  /**
   * 添加图层
   */
  const addLayer = (layer: LayerInfo) => {
    layers.value.push(layer)
  }
  
  /**
   * 删除图层
   */
  const removeLayer = (layerId: string) => {
    const index = layers.value.findIndex(layer => layer.id === layerId)
    if (index !== -1) {
      layers.value.splice(index, 1)
    }
  }
  
  /**
   * 更新图层
   */
  const updateLayer = (layerId: string, updates: Partial<LayerInfo>) => {
    const index = layers.value.findIndex(layer => layer.id === layerId)
    if (index !== -1) {
      layers.value[index] = { ...layers.value[index], ...updates }
    }
  }
  
  /**
   * 设置图层可见性
   */
  const setLayerVisible = (layerId: string, visible: boolean) => {
    updateLayer(layerId, { visible })
  }
  
  /**
   * 设置图层透明度
   */
  const setLayerOpacity = (layerId: string, opacity: number) => {
    updateLayer(layerId, { opacity })
  }
  
  /**
   * 设置绘制状态
   */
  const setDrawingState = (state: DrawingState) => {
    drawingState.value = state
  }
  
  /**
   * 开始绘制
   */
  const startDrawing = (type: DrawingState) => {
    setDrawingState(type)
  }
  
  /**
   * 停止绘制
   */
  const stopDrawing = () => {
    setDrawingState('none')
  }
  
  /**
   * 设置地图加载状态
   */
  const setMapLoading = (loading: boolean) => {
    isMapLoading.value = loading
  }
  
  /**
   * 设置选中要素
   */
  const setSelectedFeature = (feature: any) => {
    selectedFeature.value = feature
  }
  
  /**
   * 开始坐标拾取
   */
  const startPickingCoordinate = () => {
    isPickingCoordinate.value = true
  }
  
  /**
   * 停止坐标拾取
   */
  const stopPickingCoordinate = () => {
    isPickingCoordinate.value = false
  }
  
  /**
   * 重置地图状态
   */
  const resetMapState = () => {
    drawingState.value = 'none'
    selectedFeature.value = null
    isPickingCoordinate.value = false
  }

  return {
    // 状态
    mapInstance,
    viewMode,
    baseMapType,
    mapConfig,
    layers,
    drawingState,
    isMapLoading,
    isMapInitialized,
    selectedFeature,
    isPickingCoordinate,
    
    // 计算属性
    visibleLayers,
    layerStats,
    isDrawing,
    
    // 方法
    setMapInstance,
    setViewMode,
    setBaseMapType,
    updateMapConfig,
    addLayer,
    removeLayer,
    updateLayer,
    setLayerVisible,
    setLayerOpacity,
    setDrawingState,
    startDrawing,
    stopDrawing,
    setMapLoading,
    setSelectedFeature,
    startPickingCoordinate,
    stopPickingCoordinate,
    resetMapState,
  }
})
