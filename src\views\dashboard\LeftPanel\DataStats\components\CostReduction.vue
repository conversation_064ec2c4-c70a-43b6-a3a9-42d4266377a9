<template>
  <!-- 降本增效 -->
  <ScreenCard title="降本增效" :icon="'mdi:trending-up'">
    <div class="cost-reduction-content">
      <div class="cost-items">
        <div v-for="(item, index) in data.items" :key="index" class="cost-item">
          <div class="cost-data">
            <div class="cost-value">{{ item.value }}</div>
            <div class="cost-unit">{{ item.unit }}</div>
          </div>
          <div class="cost-icon">
            <img :src="iconPaths[index]" :alt="item.label" />
          </div>
          <div class="cost-label">{{ item.label }}</div>
        </div>
      </div>
    </div>
  </ScreenCard>
</template>

<script setup lang="ts">
import ScreenCard from '@/components/common/ScreenCard/ScreenCard.vue'
import type { CostReductionUIData } from '@/types/ui/stats'

// ===== Props =====
interface Props {
  data: CostReductionUIData
}

defineProps<Props>()

// 图片路径
const iconPaths = [
  '/src/assets/images/yi.webp',
  '/src/assets/images/er.webp',
  '/src/assets/images/san.webp',
]
</script>

<style scoped lang="scss">
.cost-reduction-content {
  padding: 0 0.2rem;
  height: 100%;

  .cost-items {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.2rem;

    .cost-item {
      display: grid;
      grid-template-rows: repeat(2, 1fr);
      justify-content: center;
      align-items: center;
      padding: 0.2rem;
      border-radius: 4px;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        box-shadow: 0 0 8px rgba(0, 255, 254, 0.5);
        background-color: rgba(0, 255, 254, 0.05);
      }

      .cost-icon {
        width: 2.5rem;
        height: 2.5rem;
        margin-bottom: 0.2rem;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .cost-data {
        display: flex;
        align-items: baseline;
        // margin-bottom: 0.2rem;

        .cost-value {
          font-size: 1.2rem;
          font-weight: bold;

          &:first-child {
            color: $text-active;
          }

          &:nth-child(2) {
            color: $text-active;
          }

          &:nth-child(3) {
            color: $text-active;
          }
        }

        .cost-unit {
          font-size: 0.6rem;
          margin-left: 0.2rem;
          color: $text-default;
        }
      }

      .cost-label {
        font-size: 0.7rem;
        color: $text-default;
      }

      &:nth-child(1) .cost-value {
        color: $text-active;
      }

      &:nth-child(2) .cost-value {
        color: $text-active;
      }

      &:nth-child(3) .cost-value {
        color: $text-active;
      }
    }
  }
}
</style>
