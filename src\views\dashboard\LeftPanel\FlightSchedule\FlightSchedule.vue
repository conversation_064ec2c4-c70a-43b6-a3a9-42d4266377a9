<script lang="ts" setup>
import { ref, computed } from 'vue'
import FlightTimeline from '@/components/common/Timeline/FlightTimeline.vue'
import UIcon from '@/components/common/UIcon/UIcon.vue'

// 导入任务状态类型
type TaskStatus = 'planned' | 'in-progress' | 'completed'

// 定义当前激活的标签
const activeTab = ref<'plan' | 'route'>('plan')

// 搜索关键词
const searchKeyword = ref('')

// 切换标签
const switchTab = (tab: 'plan' | 'route') => {
  activeTab.value = tab
}

// 刷新任务列表
const refreshTasks = () => {
  // TODO: 这里可以调用后端 API 获取最新的任务数据
  console.log('刷新任务列表')
}

// 获取当前日期
const currentDate = computed(() => {
  const now = new Date()
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`
})

// 定义任务接口
interface Task {
  title: string
  description?: string
  time: string
  status: TaskStatus
  location?: string
  duration?: string
  photoCount?: number
  alertCount?: number
}

// 模拟任务计划数据
const flightTasks = ref<Task[]>([
  {
    title: '无人机A - 巡逻任务',
    description: '沿预设路线进行巡逻，监控异常情况',
    time: '08:30',
    status: 'completed' as TaskStatus,
    location: '常规巡逻区域',
    duration: '1小时30分钟',
    photoCount: 156,
    alertCount: 2,
  },
  {
    title: '无人机B - 视频采集',
    description: '对指定区域进行高清视频采集',
    time: '10:15',
    status: 'in-progress' as TaskStatus,
    location: '东部监控区',
    duration: '45分钟',
    photoCount: 68,
    alertCount: 0,
  },
  {
    title: '无人机C - 定点监控',
    description: '在重点区域进行定点监控',
    time: '13:45',
    status: 'planned' as TaskStatus,
    location: '西南安全区',
    duration: '2小时',
    photoCount: 0,
    alertCount: 0,
  },
  {
    title: '无人机A - 返航充电',
    description: '完成任务返航，进行充电维护',
    time: '14:30',
    status: 'planned' as TaskStatus,
    duration: '30分钟',
  },
  {
    title: '无人机D - 应急待命',
    description: '待命状态，随时准备执行任务',
    time: '16:00',
    status: 'in-progress' as TaskStatus,
    photoCount: 12,
    alertCount: 1,
  },
])
</script>

<template>
  <div class="flight-schedule">
    <!-- 头部搜索和标签切换 -->
    <div class="flight-schedule-header">
      <!-- 搜索框 -->
      <div class="search-box">
        <el-input v-model="searchKeyword" placeholder="请输入搜索内容" clearable size="small">
          <template #prefix>
            <UIcon name="mdi:magnify" />
          </template>
        </el-input>
        <div class="refresh-btn" @click="refreshTasks">
          <UIcon name="mdi:refresh" class="icon-instance" />
        </div>
      </div>

      <!-- 标签切换 -->
      <div class="tab-switcher">
        <div class="tab-item" :class="{ active: activeTab === 'plan' }" @click="switchTab('plan')">
          <UIcon name="mdi:calendar" class="icon-instance" />
          <span>任务计划</span>
        </div>
        <div
          class="tab-item"
          :class="{ active: activeTab === 'route' }"
          @click="switchTab('route')"
        >
          <UIcon name="mdi:map-marker" class="icon-instance" />
          <span>航线列表</span>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flight-schedule-content">
      <!-- 任务计划内容 -->
      <div v-if="activeTab === 'plan'" class="plan-content">
        <div class="date-header">
          <span class="date">今天 {{ currentDate }}</span>
        </div>
        <FlightTimeline :tasks="flightTasks" />
      </div>

      <!-- 航线列表内容 -->
      <div v-else class="route-content">
        <div class="empty-state">
          <UIcon name="mdi:airplane" size="3rem" class="empty-icon" />
          <div class="empty-text">航线列表 <br />《参考视频未展示》</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.flight-schedule {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0.5rem;

  .flight-schedule-header {
    display: flex;
    flex-direction: column;
    // gap: 0.5rem;

    .search-box {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      :deep(.el-input) {
        flex: 1;
        // margin-right: 0.5rem;
        --el-input-bg-color: rgba(0, 0, 0, 0.2);
        --el-input-border-color: rgba(0, 255, 254, 0.3);
        --el-input-hover-border-color: rgba(0, 255, 254, 0.5);
        --el-input-focus-border-color: #00fffe;
        --el-input-text-color: #e1e1e1;
        --el-input-placeholder-color: rgba(225, 225, 225, 0.5);

        .el-input__wrapper {
          box-shadow: none;
          background-color: rgba(0, 0, 0, 0.2);
          box-shadow: none;
          border: 1px solid rgba(0, 255, 254, 0.3);

          &:hover {
            border-color: rgba(0, 255, 254, 0.5);
          }

          &.is-focus {
            border-color: #00fffe;
          }
        }
      }

      .refresh-btn {
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;

        .icon-instance {
          font-size: 1rem;
          color: rgba(0, 255, 254, 0.8);
          transition: all 0.3s ease;
        }

        &:hover {
          border-color: rgba(0, 255, 254, 0.5);
          background-color: rgba(0, 255, 254, 0.2);

          .icon-instance {
            color: #00fffe;
            transform: rotate(180deg);
          }
        }
      }
    }

    .tab-switcher {
      display: flex;
      margin-top: 0.5rem;
      border-bottom: 1px solid rgba(0, 255, 254, 0.3);
      justify-content: space-around;

      .tab-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 0.5rem;
        cursor: pointer;
        color: #e1e1e1;
        transition: all 0.3s ease;
        position: relative;
        height: 2rem;

        .icon-instance {
          margin-right: 0.3rem;
          font-size: 1rem;
          color: #e1e1e1;
        }

        span {
          font-size: 0.6rem;
        }

        &.active {
          color: #00fffe;

          .icon-instance {
            color: #00fffe;
          }

          &::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #00fffe;
          }
        }
      }
    }
  }

  .flight-schedule-content {
    flex: 1;
    overflow-y: auto;
    gap: 1rem;

    .date-header {
      margin-bottom: 0.5rem;
      border-bottom: 1px solid rgba(0, 255, 254, 0.2);

      .date {
        font-size: 0.7rem;
        font-weight: bold;
        color: $text-inactive;
      }
    }

    .plan-content {
      height: 100%;
      padding: 0 0.5rem;
    }

    .route-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .empty-state {
        text-align: center;
        padding: 2rem;

        .empty-icon {
          color: rgba(0, 255, 254, 0.5);
          margin-bottom: 1rem;
        }

        .empty-text {
          color: #aaa;
          font-size: 1rem;
        }
      }
    }
  }
}
</style>
