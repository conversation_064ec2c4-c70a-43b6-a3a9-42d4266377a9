/**
 * 飞控数据模拟器组合式函数
 * 用于在组件中方便地使用模拟数据功能
 */
import { onUnmounted } from 'vue'
import { mockDataSimulator } from '@/utils/mockDataSimulator'
import { useDroneStore } from '@/stores/droneStore'

export function useFlightDataSimulator() {
  const droneStore = useDroneStore()

  /**
   * 启动飞控数据模拟
   * @param droneId 无人机ID
   * @param interval 更新间隔（毫秒），默认2秒
   */
  const startSimulation = (droneId: string, interval: number = 2000) => {
    mockDataSimulator.start(
      droneId,
      (data) => {
        // 使用Store的更新方法
        droneStore.updateFlightRealTimeData(data)
      },
      interval
    )
  }

  /**
   * 停止飞控数据模拟
   */
  const stopSimulation = () => {
    mockDataSimulator.stop()
  }

  /**
   * 检查模拟器是否正在运行
   */
  const isSimulating = () => {
    return mockDataSimulator.isRunning()
  }

  // 组件卸载时自动停止模拟
  onUnmounted(() => {
    stopSimulation()
  })

  return {
    startSimulation,
    stopSimulation,
    isSimulating,
  }
}
