# Django 核心配置
DJANGO_SECRET_KEY='&8ue%+g+nyf8oy6ctogjia!q$o_qv@^sr44&px*63_k!=^$192'
DEBUG=True
ALLOWED_HOSTS=*

# 数据库配置 - PostgreSQL
DB_NAME=WRJ2
DB_USER=postgres
DB_PASSWORD=123
DB_HOST=localhost
DB_PORT=5432
DB_CONN_MAX_AGE=60

# Redis 配置
REDIS_HOST=*************
REDIS_PORT=6379
REDIS_DB=0

# Celery 配置
CELERY_BROKER_URL=redis://*************:6379/0
CELERY_RESULT_BACKEND=redis://*************:6379/0
CELERY_TIMEZONE=Asia/Shanghai

# 许可证文件路径
WINDOWS_LICENSE_PATH=E:/NewDev/zhouzhou/drone-service/my_project/local.lic
LINUX_LICENSE_PATH=/home/<USER>/license/gzwj_license.lic

# 项目服务配置
PROJECT_WEB_PROTOCOL=http
PROJECT_SERVICE_IP=127.0.0.1
PROJECT_SERVICE_PORT=16789

# SuperMap iServer 配置
SUPERMAP_ISERVER_URL=http://************:8090/
SUPERMAP_ISERVER_PASSWORD=GZyy741258963!@#
SUPERMAP_ISERVER_USER=WJYY

# 业务配置
AUTH_TOKEN_AGE=10800
MAX_THREAD_COUNT=16
PAGE_MAX_SIZE=200
IS_USE_MOCK_DATA=False
IS_USE_VERIFICATION_CODE=False
IS_ENCRYPTION=False
LOGIN_ERROR_ATTEMPTS=4
LOGIN_LOCKED_TIME=600
TOKEN_USE_CACHE=False

# 其他配置
LANGUAGE_CODE=zh-Hans
TIME_ZONE=Asia/Shanghai
