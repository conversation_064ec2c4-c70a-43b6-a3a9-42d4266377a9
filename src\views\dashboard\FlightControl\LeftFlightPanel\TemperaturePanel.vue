<script setup lang="ts">
import { computed } from 'vue'
import type { FlightRealTimeData } from '@/types/ui'

// 组件属性 - 从父组件接收数据
interface Props {
  data: FlightRealTimeData['temperature']
}

const props = defineProps<Props>()

// 温度数据 - 直接使用props传入的数据
const temperatureData = computed(() => {
  return props.data
})

// 计算空调状态显示
const airConditionerDisplay = computed(() => {
  return temperatureData.value.airConditionerStatus === '开机'
    ? { label: '空调开机', icon: 'mdi:air-conditioner', color: '#00FFFE' }
    : { label: '空调关机', icon: 'mdi:air-conditioner-off', color: '#666' }
})

// 计算温度状态颜色
const getTemperatureColor = (temp: number) => {
  if (temp < 15) return '#2196F3' // 蓝色 - 低温
  if (temp > 35) return '#F44336' // 红色 - 高温
  return '#4CAF50' // 绿色 - 正常
}
</script>

<template>
  <ScreenCard title="温度参数" icon="mdi:thermometer">
    <div class="temperature-content">
      <!-- 三个参数放在同一行，使用DataDisplay组件 -->
      <div class="temperature-row">
        <DataDisplay
          title="空调状态"
          :value="temperatureData.airConditionerStatus"
          unit=""
          :icon="airConditionerDisplay.icon"
          :iconColor="airConditionerDisplay.color"
        />
        <DataDisplay
          title="仓内温度"
          :value="`${temperatureData.cabinTemperature}℃`"
          unit=""
          icon="mdi:home-thermometer"
          :iconColor="getTemperatureColor(temperatureData.cabinTemperature)"
        />
        <DataDisplay
          title="电池温度"
          :value="`${temperatureData.batteryTemperature}℃`"
          unit=""
          icon="mdi:battery"
          :iconColor="getTemperatureColor(temperatureData.batteryTemperature)"
        />
      </div>
    </div>
  </ScreenCard>
</template>

<style scoped lang="scss">
.temperature-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0;
  overflow-x: hidden;

  .temperature-row {
    display: flex;
    // gap: 0.5rem;
    width: 100%;
    justify-content: space-between;
  }
}
</style>
