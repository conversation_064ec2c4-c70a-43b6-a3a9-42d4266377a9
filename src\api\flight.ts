/**
 * 飞控实时数据相关API接口
 */
import type { FlightRealTimeData, FlightComponentsUIData } from '@/types/ui'

/**
 * 飞控实时数据API
 */
export const flightApi = {
  /**
   * 获取飞控实时数据
   * @param droneId 无人机ID
   */
  getFlightRealTimeData: (droneId: string): Promise<FlightRealTimeData> => {
    // 暂时返回模拟数据，后期通过WebSocket获取实时数据
    return Promise.resolve({
      droneId,

      // 实时位置信息
      position: {
        longitude: 116.3974, // 北京天安门经度
        latitude: 39.9093, // 北京天安门纬度
        altitude: 120, // 海拔高度 m
        groundSpeed: 12, // 地面速度 m/s
        verticalSpeed: 2.5, // 垂直速度 m/s
        heading: 245, // 航向角 度
        timestamp: new Date().toISOString(), // 当前时间戳
      },

      // 环境数据
      environment: {
        windSpeed: '2级',
        windLevel: '微风',
        weather: '多云',
        humidity: '46%',
        temperature: '26°C',
      },

      // 机巢状态数据
      nestStatus: {
        droneStatus: '在线',
        doorStatus: '关闭',
        platformStatus: '正常',
        centeringStatus: '已归中',
        signals: {
          wifi: true,
          battery: true,
          satellite: true,
        },
      },

      // 电池参数数据
      battery: {
        chargingStatus: '空闲',
        powerStatus: '已关机',
        current: 5.2,
        voltage: 27.5,
        cycleCount: 3,
        controllerBattery: 100,
        droneBattery: 90,
      },

      // 温度参数数据
      temperature: {
        airConditionerStatus: '开机',
        cabinTemperature: 26.2,
        batteryTemperature: 25,
      },

      // 飞行任务数据
      mission: {
        progress: 65,
        location: '财政局大楼',
        duration: '15分钟',
        speed: 12,
        dataSync: '正常',
        waypointCount: 24,
        estimatedPhotos: 156,
        distance: '1.2km',
        altitude: '120m',
      },

      // 方向速度数据
      navigation: {
        heading: 245,
        gimbalAngle: -15,
        horizontalSpeed: 12,
        verticalSpeed: 2.5,
      },

      // 飞行参数数据
      parameters: {
        groundAltitude: 120,
        droneAltitude: 85,
        droneDistance: 1.2,
        uploadSignal: '-65dBm',
        downloadSignal: '-58dBm',
        gpsCount: 12,
        flightMode: '自动',
        compassStatus: '正常',
        channelInterference: '无',
        videoSignal: '良好',
      },
    })
  },

  /**
   * 获取飞控组件UI数据
   * @param droneId 无人机ID
   */
  getFlightComponentsData: async (droneId: string): Promise<FlightComponentsUIData> => {
    const realTimeData = await flightApi.getFlightRealTimeData(droneId)

    return {
      topStatusBar: {
        environment: realTimeData.environment,
        isExiting: false,
      },
      leftPanel: {
        nestStatus: realTimeData.nestStatus,
        battery: realTimeData.battery,
        temperature: realTimeData.temperature,
        mission: realTimeData.mission,
      },
      rightPanel: {
        navigation: realTimeData.navigation,
        parameters: realTimeData.parameters,
      },
    }
  },

  /**
   * 模拟WebSocket连接获取实时数据
   * @param droneId 无人机ID
   * @param callback 数据回调函数
   */
  subscribeFlightData: (droneId: string, callback: (data: FlightRealTimeData) => void) => {
    // 模拟WebSocket实时数据推送
    const interval = setInterval(async () => {
      const data = await flightApi.getFlightRealTimeData(droneId)

      // 模拟一些数据变化
      // 模拟位置变化（小范围移动）
      data.position.longitude += (Math.random() - 0.5) * 0.001 // 经度微调
      data.position.latitude += (Math.random() - 0.5) * 0.001 // 纬度微调
      data.position.altitude = 120 + Math.floor(Math.random() * 20) - 10 // 高度变化
      data.position.groundSpeed = Math.floor(Math.random() * 20) // 地面速度
      data.position.verticalSpeed = parseFloat((Math.random() * 10 - 5).toFixed(1)) // 垂直速度
      data.position.heading = Math.floor(Math.random() * 360) // 航向角
      data.position.timestamp = new Date().toISOString() // 更新时间戳

      // 模拟导航数据变化
      data.navigation.heading = data.position.heading // 保持与位置航向一致
      data.navigation.gimbalAngle = Math.floor(Math.random() * 180) - 90
      data.navigation.horizontalSpeed = data.position.groundSpeed // 保持与位置速度一致
      data.navigation.verticalSpeed = data.position.verticalSpeed // 保持与位置垂直速度一致

      callback(data)
    }, 3000) // 每3秒更新一次

    // 返回取消订阅函数
    return () => clearInterval(interval)
  },
}
