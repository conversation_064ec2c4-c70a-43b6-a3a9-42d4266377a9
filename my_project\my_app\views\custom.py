# 从 rest_framework 模块导入 viewsets 和 Response 类
# viewsets 用于创建视图集，方便实现 CRUD 操作
# Response 用于返回 HTTP 响应
from rest_framework import viewsets
from rest_framework.response import Response
# 从自定义模块导入 Result 和 ResultCodeMsgEnum 类
# Result 类用于封装响应结果，ResultCodeMsgEnum 可能用于定义响应的状态码和消息
from my_app.views.response.baseRespone import Result, ResultCodeMsgEnum

# 定义一个自定义的视图集类 CustomModelViewSet，继承自 viewsets.ModelViewSet
# ModelViewSet 是一个包含了常见 CRUD 操作视图的基类
class CustomModelViewSet(viewsets.ModelViewSet):

    # 重写 list 方法，用于处理获取资源列表的请求
    def list(self, request, *args, **kwargs):
        try:
            # 对查询集进行过滤操作，根据请求的条件筛选出符合要求的对象
            queryset = self.filter_queryset(self.get_queryset())
            # 对过滤后的查询集进行分页处理
            page = self.paginate_queryset(queryset)
            # 如果分页成功，说明存在分页数据
            if page is not None:
                # 使用序列化器对分页后的数据进行序列化，将对象转换为 JSON 格式
                serializer = self.get_serializer(page, many=True)
                # 获取分页响应对象，包含分页信息和序列化后的数据
                page_response = self.get_paginated_response(serializer.data)
                # 使用 Result 类的 list 方法封装分页响应数据，返回统一格式的响应结果
                data = Result.list(page_response.data)
            # 返回包含分页数据的响应
            return Response(data)
        except Exception as ext:
            # 若在处理过程中出现异常，打印异常信息
            print(ext)

    # 重写 retrieve 方法，用于处理获取单个资源的请求
    def retrieve(self, request, *args, **kwargs):
        try:
            # 调用父类的 retrieve 方法，执行获取单个资源的操作
            return super().retrieve(request, args, kwargs)
        except Exception as ext:
            # 若出现异常，定义错误消息
            msg = "数据不存在"
            # 使用 Result 类的 fail 方法返回失败响应，包含错误消息
            return Result.fail(msg, msg)

    # 重写 destroy 方法，用于处理删除资源的请求
    def destroy(self, request, *args, **kwargs):
        try:
            # 获取要删除的对象实例
            instance = self.get_object()
            # 调用 perform_destroy 方法执行删除操作
            self.perform_destroy(instance)
            # 使用 Result 类的 ok 方法返回成功响应
            return Result.ok()
        except Exception as ext:
            # 若出现异常，定义错误消息
            msg = "数据不存在"
            # 使用 Result 类的 fail 方法返回失败响应，包含错误消息
            return Result.fail(msg, msg)