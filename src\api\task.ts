/**
 * 飞行任务相关API接口
 */
import type {
  TaskDetail,
  CreateTaskRequest,
  CreateTaskResponse,
  TaskListParams,
  TaskListResponse,
} from '@/types/ui'

/**
 * 飞行任务API
 */
export const taskApi = {
  /**
   * 获取任务列表
   * @param params 查询参数
   */
  getTaskList: async (params: TaskListParams = {}): Promise<TaskListResponse> => {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 返回模拟任务列表数据
    return generateMockTaskList(params)
  },

  /**
   * 创建任务
   * @param data 创建任务请求数据
   */
  createTask: async (data: CreateTaskRequest): Promise<CreateTaskResponse> => {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 800))

    console.log('创建任务:', data)

    return {
      code: 200,
      message: '任务创建成功',
      data: {
        taskId: `task_${Date.now()}`,
        createdAt: new Date().toISOString(),
      },
    }
  },
}

/**
 * 生成模拟任务列表数据（20个任务）
 * @param params 查询参数
 */
function generateMockTaskList(params: TaskListParams): TaskListResponse {
  // 完整的模拟数据（20个任务）
  const allTasks: TaskDetail[] = [
    {
      id: 'task_001',
      name: '浙江省衢州市柯城区荷花街道',
      shape: '面状',
      perimeter: 1594.98,
      area: 0.156134974163356,
      centerLongitude: 119.1565,
      centerLatitude: 29.1296,
      locationDetail: '浙江省衢州市柯城区荷花街道荷花西路128号',
      purpose: '全景',
      urgency: '不紧急',
      status: '执行中',
      executeTime: '暂无数据',
      createdAt: '2025-04-02T14:36:28Z',
      updatedAt: '2025-04-02T14:36:28Z',
      thumbnail: 'https://picsum.photos/120/90?random=1',
      markCount: 3,
      photoCount: 15,
      droneId: 'drone_001',
      droneName: '塔石测试机',
    },
    {
      id: 'task_002',
      name: '衢州市衢江区东港街道',
      shape: '线状',
      perimeter: 2340.56,
      area: 0.234567891234567,
      centerLongitude: 118.5089,
      centerLatitude: 28.8956,
      locationDetail: '浙江省衢州市衢江区东港街道东港大道256号',
      purpose: '拍摄',
      urgency: '一般',
      status: '待执行',
      executeTime: '暂无数据',
      createdAt: '2025-04-02T09:59:44Z',
      updatedAt: '2025-04-02T09:59:44Z',
      thumbnail: 'https://picsum.photos/120/90?random=2',
      markCount: 0,
      photoCount: 0,
    },
    {
      id: 'task_003',
      name: '常山县天马街道文峰西路',
      shape: '点状',
      perimeter: 890.23,
      area: 0.089023456789012,
      centerLongitude: 118.4967,
      centerLatitude: 28.8834,
      locationDetail: '浙江省衢州市常山县天马街道文峰西路88号',
      purpose: '拍摄',
      urgency: '不紧急',
      status: '待执行',
      executeTime: '暂无数据',
      createdAt: '2025-04-02T09:51:15Z',
      updatedAt: '2025-04-02T09:51:15Z',
      thumbnail: 'https://picsum.photos/120/90?random=3',
      markCount: 2,
      photoCount: 8,
    },
    {
      id: 'task_004',
      name: '开化县华埠镇紫石村',
      shape: '面状',
      perimeter: 3456.78,
      area: 0.345678901234567,
      centerLongitude: 118.5156,
      centerLatitude: 28.8978,
      locationDetail: '浙江省衢州市开化县华埠镇紫石村村委会',
      purpose: '全景',
      urgency: '紧急',
      status: '已完成',
      executeTime: '2025-04-01T17:09:14Z',
      createdAt: '2025-04-01T15:30:00Z',
      updatedAt: '2025-04-01T17:09:14Z',
      thumbnail: 'https://picsum.photos/120/90?random=4',
      markCount: 5,
      photoCount: 23,
      droneId: 'drone_002',
      droneName: '巡检无人机02',
    },
    {
      id: 'task_005',
      name: '龙游县龙洲街道兴龙北路',
      shape: '线状',
      perimeter: 1876.45,
      area: 0.187645098765432,
      centerLongitude: 118.4823,
      centerLatitude: 28.9234,
      locationDetail: '浙江省衢州市龙游县龙洲街道兴龙北路369号',
      purpose: '拍摄',
      urgency: '不紧急',
      status: '已完成',
      executeTime: '2025-04-01T17:01:42Z',
      createdAt: '2025-04-01T14:20:00Z',
      updatedAt: '2025-04-01T17:01:42Z',
      thumbnail: 'https://picsum.photos/120/90?random=5',
      markCount: 1,
      photoCount: 12,
    },
    {
      id: 'task_006',
      name: '江山市虎山街道江山大道',
      shape: '面状',
      perimeter: 2567.89,
      area: 0.256789012345678,
      centerLongitude: 118.5067,
      centerLatitude: 28.8912,
      locationDetail: '浙江省衢州市江山市虎山街道江山大道518号',
      purpose: '三维',
      urgency: '一般',
      status: '暂停中',
      executeTime: '暂无数据',
      createdAt: '2025-04-01T11:45:00Z',
      updatedAt: '2025-04-01T16:20:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=6',
      markCount: 4,
      photoCount: 18,
      droneId: 'drone_003',
      droneName: '测绘无人机03',
    },
    {
      id: 'task_007',
      name: '柯城区石梁镇石梁村',
      shape: '点状',
      perimeter: 1234.56,
      area: 0.123456789012345,
      centerLongitude: 118.6234,
      centerLatitude: 28.9567,
      locationDetail: '浙江省衢州市柯城区石梁镇石梁村村民委员会',
      purpose: '正射',
      urgency: '紧急',
      status: '待执行',
      executeTime: '暂无数据',
      createdAt: '2025-04-01T08:30:00Z',
      updatedAt: '2025-04-01T08:30:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=7',
      markCount: 0,
      photoCount: 0,
    },
    {
      id: 'task_008',
      name: '衢江区全旺镇楼山后村',
      shape: '线状',
      perimeter: 3789.12,
      area: 0.378912345678901,
      centerLongitude: 118.789,
      centerLatitude: 28.8123,
      locationDetail: '浙江省衢州市衢江区全旺镇楼山后村',
      purpose: '视频',
      urgency: '不紧急',
      status: '已完成',
      executeTime: '2025-03-31T16:45:30Z',
      createdAt: '2025-03-31T14:00:00Z',
      updatedAt: '2025-03-31T16:45:30Z',
      thumbnail: 'https://picsum.photos/120/90?random=8',
      markCount: 6,
      photoCount: 28,
    },
    {
      id: 'task_009',
      name: '常山县辉埠镇新昌村',
      shape: '面状',
      perimeter: 4567.23,
      area: 0.456723456789012,
      centerLongitude: 118.4567,
      centerLatitude: 28.789,
      locationDetail: '浙江省衢州市常山县辉埠镇新昌村',
      purpose: '全景',
      urgency: '一般',
      status: '异常',
      executeTime: '暂无数据',
      createdAt: '2025-03-31T10:15:00Z',
      updatedAt: '2025-03-31T15:30:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=9',
      markCount: 2,
      photoCount: 5,
      droneId: 'drone_004',
      droneName: '应急无人机04',
    },
    {
      id: 'task_010',
      name: '开化县马金镇霞山村',
      shape: '点状',
      perimeter: 987.65,
      area: 0.098765432109876,
      centerLongitude: 118.3456,
      centerLatitude: 28.9876,
      locationDetail: '浙江省衢州市开化县马金镇霞山村',
      purpose: '拍摄',
      urgency: '紧急',
      status: '执行中',
      executeTime: '暂无数据',
      createdAt: '2025-03-30T16:20:00Z',
      updatedAt: '2025-03-30T16:20:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=10',
      markCount: 3,
      photoCount: 14,
      droneId: 'drone_005',
      droneName: '巡检无人机05',
    },
    {
      id: 'task_011',
      name: '龙游县小南海镇团石村',
      shape: '线状',
      perimeter: 2134.67,
      area: 0.213467890123456,
      centerLongitude: 118.5678,
      centerLatitude: 28.8765,
      locationDetail: '浙江省衢州市龙游县小南海镇团石村',
      purpose: '三维',
      urgency: '不紧急',
      status: '已取消',
      executeTime: '暂无数据',
      createdAt: '2025-03-30T09:00:00Z',
      updatedAt: '2025-03-30T14:30:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=11',
      markCount: 0,
      photoCount: 0,
    },
    {
      id: 'task_012',
      name: '江山市峡口镇枫石村',
      shape: '面状',
      perimeter: 3421.89,
      area: 0.342189012345678,
      centerLongitude: 118.6789,
      centerLatitude: 28.7654,
      locationDetail: '浙江省衢州市江山市峡口镇枫石村',
      purpose: '正射',
      urgency: '一般',
      status: '已完成',
      executeTime: '2025-03-29T18:20:15Z',
      createdAt: '2025-03-29T13:45:00Z',
      updatedAt: '2025-03-29T18:20:15Z',
      thumbnail: 'https://picsum.photos/120/90?random=12',
      markCount: 7,
      photoCount: 35,
    },
    {
      id: 'task_013',
      name: '柯城区万田乡万田村',
      shape: '点状',
      perimeter: 1567.34,
      area: 0.156734567890123,
      centerLongitude: 118.4321,
      centerLatitude: 28.8654,
      locationDetail: '浙江省衢州市柯城区万田乡万田村',
      purpose: '视频',
      urgency: '紧急',
      status: '待执行',
      executeTime: '暂无数据',
      createdAt: '2025-03-29T07:30:00Z',
      updatedAt: '2025-03-29T07:30:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=13',
      markCount: 1,
      photoCount: 3,
    },
    {
      id: 'task_014',
      name: '衢江区莲花镇东湖村',
      shape: '面状',
      perimeter: 2890.45,
      area: 0.289045678901234,
      centerLongitude: 118.5432,
      centerLatitude: 28.9321,
      locationDetail: '浙江省衢州市衢江区莲花镇东湖村',
      purpose: '拍摄',
      urgency: '不紧急',
      status: '执行中',
      executeTime: '暂无数据',
      createdAt: '2025-03-28T15:20:00Z',
      updatedAt: '2025-03-28T15:20:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=14',
      markCount: 4,
      photoCount: 19,
      droneId: 'drone_006',
      droneName: '环保无人机06',
    },
    {
      id: 'task_015',
      name: '常山县青石镇砚瓦山村',
      shape: '线状',
      perimeter: 1789.56,
      area: 0.178956789012345,
      centerLongitude: 118.3789,
      centerLatitude: 28.8432,
      locationDetail: '浙江省衢州市常山县青石镇砚瓦山村',
      purpose: '全景',
      urgency: '一般',
      status: '暂停中',
      executeTime: '暂无数据',
      createdAt: '2025-03-28T11:00:00Z',
      updatedAt: '2025-03-28T16:45:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=15',
      markCount: 2,
      photoCount: 8,
      droneId: 'drone_007',
      droneName: '测绘无人机07',
    },
    {
      id: 'task_016',
      name: '开化县池淮镇立江村',
      shape: '点状',
      perimeter: 1345.78,
      area: 0.134578901234567,
      centerLongitude: 118.2345,
      centerLatitude: 28.9654,
      locationDetail: '浙江省衢州市开化县池淮镇立江村',
      purpose: '三维',
      urgency: '紧急',
      status: '已完成',
      executeTime: '2025-03-27T17:30:45Z',
      createdAt: '2025-03-27T14:15:00Z',
      updatedAt: '2025-03-27T17:30:45Z',
      thumbnail: 'https://picsum.photos/120/90?random=16',
      markCount: 5,
      photoCount: 22,
    },
    {
      id: 'task_017',
      name: '龙游县詹家镇浦山村',
      shape: '面状',
      perimeter: 4123.67,
      area: 0.412367890123456,
      centerLongitude: 118.6543,
      centerLatitude: 28.7321,
      locationDetail: '浙江省衢州市龙游县詹家镇浦山村',
      purpose: '正射',
      urgency: '不紧急',
      status: '异常',
      executeTime: '暂无数据',
      createdAt: '2025-03-27T08:45:00Z',
      updatedAt: '2025-03-27T12:20:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=17',
      markCount: 1,
      photoCount: 2,
      droneId: 'drone_008',
      droneName: '巡检无人机08',
    },
    {
      id: 'task_018',
      name: '江山市长台镇长台村',
      shape: '线状',
      perimeter: 2678.9,
      area: 0.267890123456789,
      centerLongitude: 118.7654,
      centerLatitude: 28.8987,
      locationDetail: '浙江省衢州市江山市长台镇长台村',
      purpose: '视频',
      urgency: '一般',
      status: '待执行',
      executeTime: '暂无数据',
      createdAt: '2025-03-26T16:30:00Z',
      updatedAt: '2025-03-26T16:30:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=18',
      markCount: 0,
      photoCount: 0,
    },
    {
      id: 'task_019',
      name: '柯城区石室乡荆溪村',
      shape: '面状',
      perimeter: 3567.12,
      area: 0.356712345678901,
      centerLongitude: 118.4987,
      centerLatitude: 28.8123,
      locationDetail: '浙江省衢州市柯城区石室乡荆溪村',
      purpose: '拍摄',
      urgency: '紧急',
      status: '执行中',
      executeTime: '暂无数据',
      createdAt: '2025-03-26T10:00:00Z',
      updatedAt: '2025-03-26T10:00:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=19',
      markCount: 6,
      photoCount: 27,
      droneId: 'drone_009',
      droneName: '应急无人机09',
    },
    {
      id: 'task_020',
      name: '衢江区高家镇高家村',
      shape: '点状',
      perimeter: 1456.89,
      area: 0.145689012345678,
      centerLongitude: 118.5876,
      centerLatitude: 28.9234,
      locationDetail: '浙江省衢州市衢江区高家镇高家村',
      purpose: '全景',
      urgency: '不紧急',
      status: '已取消',
      executeTime: '暂无数据',
      createdAt: '2025-03-25T13:45:00Z',
      updatedAt: '2025-03-25T18:20:00Z',
      thumbnail: 'https://picsum.photos/120/90?random=20',
      markCount: 0,
      photoCount: 0,
    },
  ]

  // 按查询参数筛选数据
  let filteredTasks = [...allTasks]

  // 按状态筛选
  if (params.status) {
    filteredTasks = filteredTasks.filter((task) => task.status === params.status)
  }

  // 按用途筛选
  if (params.purpose) {
    filteredTasks = filteredTasks.filter((task) => task.purpose === params.purpose)
  }

  // 按关键词搜索
  if (params.searchKeyword) {
    const keyword = params.searchKeyword.toLowerCase()
    filteredTasks = filteredTasks.filter(
      (task) =>
        task.name.toLowerCase().includes(keyword) ||
        task.locationDetail.toLowerCase().includes(keyword),
    )
  }

  // 按日期范围筛选
  if (params.dateRange && params.dateRange[0] && params.dateRange[1]) {
    const [startDate, endDate] = params.dateRange
    filteredTasks = filteredTasks.filter((task) => {
      const taskDate = new Date(task.createdAt)
      return taskDate >= startDate && taskDate <= endDate
    })
  }

  // 分页处理
  const page = params.page || 1
  const pageSize = params.pageSize || 10
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const paginatedTasks = filteredTasks.slice(start, end)

  return {
    data: paginatedTasks,
    total: filteredTasks.length,
  }
}
