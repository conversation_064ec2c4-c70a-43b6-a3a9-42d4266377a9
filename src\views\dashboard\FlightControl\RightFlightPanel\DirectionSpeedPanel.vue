<script setup lang="ts">
import { computed } from 'vue'
import type { FlightRealTimeData } from '@/types/ui'

// 组件属性 - 从父组件接收数据
interface Props {
  data: FlightRealTimeData['navigation']
}

const props = defineProps<Props>()

// 导航数据 - 直接使用props传入的数据
const navigationData = computed(() => {
  return props.data
})
</script>

<template>
  <ScreenCard title="方向速度" icon="mdi:compass">
    <template #header-control>
      <!-- RTK状态指示器 -->
      <div class="rtk-indicators">
        <div class="rtk-item">
          <span class="rtk-label">RTK</span>
          <span class="rtk-status unknown">未知</span>
        </div>
        <div class="rtk-item">
          <span class="rtk-label">启用</span>
          <UIcon name="mdi:check-circle" size="0.8rem" color="#00FFFE" />
        </div>
        <div class="rtk-item">
          <span class="rtk-label">关闭</span>
          <UIcon name="mdi:close-circle" size="0.8rem" color="#00FFFE" />
        </div>
      </div>
    </template>

    <div class="direction-speed-content">
      <!-- 四个真实仪表盘，2x2网格布局 -->
      <div class="dials-grid">
        <!-- 机头朝向 - 方向仪表盘 -->
        <div class="dial-container">
          <GaugeChart
            title="机头朝向"
            :value="navigationData.heading"
            :min="0"
            :max="360"
            unit="°"
            type="direction"
            color="#00FFFE"
          />
        </div>

        <!-- 云台角度 - 角度仪表盘 -->
        <div class="dial-container">
          <GaugeChart
            title="云台角度"
            :value="Math.abs(navigationData.gimbalAngle)"
            :min="0"
            :max="90"
            unit="°"
            type="altitude"
            color="#00FFFE"
          />
        </div>

        <!-- 水平速度 - 速度仪表盘 -->
        <div class="dial-container">
          <GaugeChart
            title="水平速度"
            :value="navigationData.horizontalSpeed"
            :min="0"
            :max="100"
            unit="m/s"
            type="speed"
            color="#00FFFE"
          />
        </div>

        <!-- 垂直速度 - 速度仪表盘 -->
        <div class="dial-container">
          <GaugeChart
            title="垂直速度"
            :value="Math.abs(navigationData.verticalSpeed)"
            :min="0"
            :max="50"
            unit="m/s"
            type="speed"
            color="#00FFFE"
          />
        </div>
      </div>
    </div>
  </ScreenCard>
</template>

<style scoped lang="scss">
.rtk-indicators {
  display: flex;
  // gap: 0.3rem;

  .rtk-item {
    display: flex;
    align-items: center;
    gap: 0.2rem;

    .rtk-label {
      font-size: $font-size-panel-micro;
      color: $text-secondary;
    }

    .rtk-status {
      font-size: $font-size-panel-micro;
      padding: 0.1rem 0.3rem;
      border-radius: $border-radius-small;

      &.unknown {
        background: rgba($warning-color, 0.2);
        color: $warning-color;
      }
    }
  }
}

.direction-speed-content {
  height: 100%;
  padding: 0.5rem 0;

  .dials-grid {
    height: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 0.5rem;

    .dial-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 60px;
      min-width: 60px;
      width: 100%;
      height: 100%;
      // padding: 0.25rem;
    }
  }
}
</style>
