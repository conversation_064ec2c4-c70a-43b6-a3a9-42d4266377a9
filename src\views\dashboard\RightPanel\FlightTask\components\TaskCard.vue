<template>
  <div class="task-card" @click="handleCardClick">
    <!-- 左侧缩略图 -->
    <div class="task-thumbnail">
      <img v-if="task.thumbnail" :src="task.thumbnail" :alt="task.name" class="thumbnail-image" />
      <div v-else class="thumbnail-placeholder">
        <el-icon class="placeholder-icon"><Picture /></el-icon>
      </div>

      <!-- 绝对定位的标记信息 -->
      <div class="thumbnail-overlay">
        <div class="overlay-item">
          <el-icon class="overlay-icon"><Location /></el-icon>
          <span class="overlay-text">{{ task.markCount || 0 }}</span>
        </div>
        <div class="overlay-item">
          <el-icon class="overlay-icon"><Picture /></el-icon>
          <span class="overlay-text">{{ task.photoCount || 0 }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="task-content">
      <!-- 上部：标题和状态标签 -->
      <div class="task-header">
        <div class="task-title">{{ task.name }}</div>
        <div class="task-status">
          <span :class="['status-badge', `status-${statusClass}`]">
            {{ getStatusText(task.status) }}
          </span>
        </div>
      </div>

      <!-- 下部：四个信息项 -->
      <div class="task-info">
        <div class="info-item">
          <span class="info-label">提交时间:</span>
          <span class="info-value">{{ task.updatedAt }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">执行时间:</span>
          <span class="info-value">{{ task.executeTime || '未分配' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">巡检用途:</span>
          <span class="info-value">{{ task.purpose }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">地点:</span>
          <span class="info-value location-text">{{ task.locationDetail }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { Picture, Location } from '@element-plus/icons-vue'
import type { TaskDetail } from '@/types/ui'
// 移除Store依赖，改为通过props和emits通信

// Props
const props = defineProps<{
  task: TaskDetail
}>()

// Emits
const emit = defineEmits<{
  actionClick: [taskId: string, action: string]
}>()

// 状态映射 - 将中文状态转换为英文状态类名
const statusClassMap: Record<string, string> = {
  待执行: 'pending',
  执行中: 'executing',
  已完成: 'completed',
  已取消: 'cancelled',
  异常: 'error',
  暂停中: 'paused',
}

// 计算属性 - 适配字段差异
const statusClass = computed(() => statusClassMap[props.task.status] || 'pending')

// 获取状态文本
const getStatusText = (status: string) => {
  return status
}

// 处理卡片点击事件
const handleCardClick = () => {
  // 通过emit向父组件传递任务点击事件
  emit('actionClick', props.task.id, 'view')
}
</script>

<style scoped lang="scss">
.task-card {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba($bg-card, 0.3);
  border: 1px solid $border-color-light;
  border-radius: $border-radius-base;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: rgba($primary-color, 0.3);
    background: rgba($bg-card, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba($primary-color, 0.1);
  }

  &:active {
    transform: translateY(0);
  }

  // 左侧缩略图 - 占满父元素
  .task-thumbnail {
    flex: 0 0 5rem;
    border-radius: $border-radius-small;
    overflow: hidden;
    background: rgba($bg-medium, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid $border-color-light;
    position: relative;

    .thumbnail-image {
      width: 100%;
      height: 100%;
      object-fit: fill;
    }

    .thumbnail-placeholder {
      color: $text-inactive;

      .placeholder-icon {
        font-size: 1.2rem;
      }
    }

    // 绝对定位的覆盖层信息
    .thumbnail-overlay {
      position: absolute;
      bottom: 0.1rem;
      left: 0.1rem;
      right: 0.1rem;
      display: flex;
      justify-content: space-between;
      gap: 0.15rem;

      .overlay-item {
        display: flex;
        align-items: center;
        gap: 0.1rem;
        background: rgba(0, 0, 0, 0.7);
        padding: 0.1rem 0.2rem;
        border-radius: $border-radius-small;
        font-size: $font-size-panel-micro;

        .overlay-icon {
          font-size: 0.45rem;
          color: $text-active;
        }

        .overlay-text {
          color: $text-default;
          font-weight: 500;
        }
      }
    }
  }

  // 右侧内容区域 - 自适应剩余空间
  .task-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0; // 防止内容溢出
    gap: 0.3rem;

    // 上部：标题和状态标签
    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .task-title {
        font-size: $font-size-panel-normal;
        color: $text-active;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        margin-right: 0.5rem;
      }

      .task-status {
        flex-shrink: 0;

        .status-badge {
          display: inline-block;
          padding: 0.125rem 0.375rem;
          font-size: $font-size-panel-caption;
          border-radius: $border-radius-small;
          font-weight: 500;

          &.status-executing {
            background: rgba($success-color, 0.2);
            color: $success-color;
          }

          &.status-pending {
            background: rgba($warning-color, 0.2);
            color: $warning-color;
          }

          &.status-completed {
            background: rgba($info-color, 0.2);
            color: $info-color;
          }

          &.status-paused {
            background: rgba($error-color, 0.2);
            color: $error-color;
          }

          &.status-cancelled {
            background: rgba($text-inactive, 0.2);
            color: $text-inactive;
          }

          &.status-error {
            background: rgba($error-color, 0.3);
            color: $error-color;
          }
        }
      }
    }

    // 下部：四个信息项
    .task-info {
      display: flex;
      flex-direction: column;
      gap: 0.15rem;

      .info-item {
        display: flex;
        align-items: center;
        gap: 0.2rem;
        font-size: $font-size-panel-label;

        .info-label {
          color: $text-secondary;
          flex-shrink: 0;
          max-width: 3rem;
        }

        .info-value {
          color: $text-default;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          // 地点信息的特殊样式
          &.location-text {
            color: $text-active;
          }
        }
      }
    }
  }
}
</style>
