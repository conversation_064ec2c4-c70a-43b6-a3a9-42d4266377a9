<script setup lang="ts">
import { computed } from 'vue'
import type { FlightRealTimeData } from '@/types/ui'

// 组件属性 - 从父组件接收数据
interface Props {
  data: FlightRealTimeData['nestStatus']
}

const props = defineProps<Props>()

// 机巢状态数据 - 直接使用props传入的数据
const nestData = computed(() => {
  return props.data
})

// 计算无人机状态显示
const droneStatusDisplay = computed(() => {
  const status = nestData.value.droneStatus

  switch (status) {
    case '在线':
      return { label: '无人机在线', icon: 'mdi:airplane', color: '#00FFFE' }
    case '飞行中':
      return { label: '无人机飞行中', icon: 'mdi:airplane-takeoff', color: '#4CAF50' }
    case '准备起降':
      return { label: '准备起降', icon: 'mdi:airplane-landing', color: '#FF9800' }
    case '维护中':
      return { label: '维护中', icon: 'mdi:airplane-settings', color: '#9C27B0' }
    case '异常':
      return { label: '状态异常', icon: 'mdi:airplane-alert', color: '#F44336' }
    case '下线':
    default:
      return { label: '无人机下线', icon: 'mdi:airplane-off', color: '#666' }
  }
})
</script>

<template>
  <ScreenCard title="机巢状态" icon="mdi:home-variant">
    <template #header-control>
      <!-- 状态指示器 -->
      <div class="status-indicators">
        <UIcon name="mdi:wifi" size="0.8rem" :color="nestData.signals.wifi ? '#00FFFE' : '#666'" />
        <UIcon
          name="mdi:battery"
          size="0.8rem"
          :color="nestData.signals.battery ? '#00FFFE' : '#666'"
        />
        <UIcon
          name="mdi:satellite-variant"
          size="0.8rem"
          :color="nestData.signals.satellite ? '#00FFFE' : '#666'"
        />
      </div>
    </template>

    <div class="drone-nest-content">
      <!-- 主状态显示区域 -->
      <div class="main-status">
        <!-- 状态文字 -->
        <div class="status-text">
          <span class="status-label">{{ droneStatusDisplay.label }}</span>
        </div>

        <!-- 状态图标 -->
        <div class="status-icon">
          <UIcon :name="droneStatusDisplay.icon" size="2rem" :color="droneStatusDisplay.color" />
        </div>
      </div>

      <!-- 底部状态标签行 -->
      <div class="bottom-status">
        <div class="status-tag">
          <span class="tag-label">舱门状态</span>
          <span class="tag-value">{{ nestData.doorStatus }}</span>
        </div>
        <div class="status-tag">
          <span class="tag-label">平台状态</span>
          <span class="tag-value">{{ nestData.platformStatus }}</span>
        </div>
        <div class="status-tag">
          <span class="tag-label">归中状态</span>
          <span class="tag-value">{{ nestData.centeringStatus }}</span>
        </div>
      </div>
    </div>
  </ScreenCard>
</template>

<style scoped lang="scss">
.status-indicators {
  display: flex;
  gap: 0.3rem;
}

.drone-nest-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  // gap: 1rem;

  .main-status {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // gap: 1rem;
    background: linear-gradient(135deg, rgba(255, 87, 34, 0.1) 0%, rgba(255, 87, 34, 0.05) 100%);
    border-radius: $border-radius-base;
    // padding: 1rem;

    .status-text {
      .status-label {
        font-size: $font-size-panel-normal;
        color: $text-default;
        font-weight: bold;
      }
    }

    .status-icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .bottom-status {
    display: flex;
    justify-content: space-between;
    gap: 0.5rem;

    .status-tag {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.2rem;
      padding: 0.3rem;
      background: rgba($bg-card, 0.5);
      border-radius: $border-radius-small;
      transition: all 0.2s ease;
      cursor: pointer;

      &:hover {
        background: rgba($bg-card, 0.7);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba($primary-color, 0.1);

        .tag-label,
        .tag-value {
          color: $text-active;
        }
      }

      .tag-label {
        font-size: $font-size-panel-caption;
        color: $text-secondary;
        transition: color 0.2s ease;
      }

      .tag-value {
        font-size: $font-size-panel-label;
        color: $text-default;
        font-weight: bold;
        transition: color 0.2s ease;
      }
    }
  }
}
</style>
