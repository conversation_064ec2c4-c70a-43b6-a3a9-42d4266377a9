import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
# from .models import DroneTrackingRecord


class DroneDataConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # 获取 URL 中的 drone_id 参数
        self.drone_id = self.scope['url_route']['kwargs']['drone_id']
        self.room_group_name = f'drone_{self.drone_id}'

        # 加入房间组
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        # 接受 WebSocket 连接
        await self.accept()

        # 可选：发送欢迎消息
        await self.send(text_data=json.dumps({
            'message': f'已连接到无人机 {self.drone_id} 的数据通道',
            'status': 'connected'
        }))

    async def disconnect(self, close_code):
        # 离开房间组
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    # 接收来自 WebSocket 的消息
    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message_type = text_data_json.get('type')

        if message_type == 'drone_data':
            # 处理无人机数据
            await self.process_drone_data(text_data_json)
        elif message_type == 'command':
            # 处理控制命令
            await self.process_command(text_data_json)

    # 处理无人机数据
    async def process_drone_data(self, data):
        # 保存数据到数据库
        await self.save_drone_data(data)

        # 广播数据给所有连接的客户端
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'drone_data_update',
                'data': data
            }
        )

    # 发送无人机数据更新
    async def drone_data_update(self, event):
        data = event['data']
        await self.send(text_data=json.dumps(data))

    # 处理控制命令
    async def process_command(self, command):
        # 实现命令处理逻辑
        await self.send(text_data=json.dumps({
            'type': 'command_ack',
            'command': command.get('command'),
            'status': 'received'
        }))

    # 保存数据到数据库的异步方法
    # @database_sync_to_async
    # def save_drone_data(self, data):
    #     # 提取数据字段
    #     drone_id = data.get('drone_id', self.drone_id)
    #     ground_altitude = data.get('ground_altitude')
    #     home_altitude = data.get('home_altitude')
    #     home_distance = data.get('home_distance')
    #     heading = data.get('heading')
    #     horizontal_speed = data.get('horizontal_speed')
    #     vertical_speed = data.get('vertical_speed')
    #     battery_temp = data.get('battery_temperature')
    #     latitude = data.get('latitude')
    #     longitude = data.get('longitude')
    #
    #     # 创建并保存记录
    #     record = DroneTrackingRecord(
    #         drone_id=drone_id,
    #         ground_altitude=ground_altitude,
    #         home_altitude=home_altitude,
    #         home_distance=home_distance,
    #         heading_degrees=heading,
    #         horizontal_speed=horizontal_speed,
    #         vertical_speed=vertical_speed,
    #         battery_temperature=battery_temp,
    #         latitude=latitude,
    #         longitude=longitude
    #     )
    #     record.save()
    #     return record.id
