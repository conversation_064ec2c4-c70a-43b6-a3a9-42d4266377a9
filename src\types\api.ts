/**
 * API 通用类型定义
 */

// 通用API响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data?: T
}

// 分页请求参数
export interface PaginationParams {
  page: number
  pageSize: number
}

// 分页响应数据
export interface PaginationResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 通用分页API响应
export interface PaginatedApiResponse<T> extends ApiResponse<PaginationResponse<T>> {}

// 请求状态枚举
export type RequestStatus = 'idle' | 'loading' | 'success' | 'error'

// 排序方向
export type SortOrder = 'asc' | 'desc'

// 通用排序参数
export interface SortParams {
  field: string
  order: SortOrder
}

// 通用筛选参数
export interface FilterParams {
  [key: string]: any
}

// 列表查询参数
export interface ListQueryParams extends PaginationParams {
  sort?: SortParams
  filter?: FilterParams
  search?: string
}
