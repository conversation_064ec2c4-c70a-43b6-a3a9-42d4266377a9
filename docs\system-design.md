# 无人机指挥仪表盘系统设计方案

## 项目概述

无人机指挥仪表盘系统是一个集成无人机管理、任务规划、实时监控和智能分析于一体的综合性平台。该系统通过自动化的无人机管理与拍摄，结合后台AI分析技术，实现对环境变化的智能识别与预警，为政府等部门提供生态环境监测、森林防火、城市管理等领域的智能化解决方案。

### 核心价值

1. **智能环境监测**：通过无人机高空巡航，实时监测生态环境变化，及时发现水体污染、植被破坏等环境问题
2. **火情早期预警**：利用热成像技术和AI分析，实现森林火灾的早期发现与预警，大幅提高防火效率
3. **城市管理辅助**：监测城市建设、交通流量、违建情况，为城市管理提供数据支持
4. **应急响应支持**：在自然灾害、突发事件中提供空中视角，辅助指挥决策和救援行动
5. **数据驱动决策**：通过长期数据积累和分析，为政府部门提供科学决策依据

### 技术优势

1. **自动化任务执行**：支持预设航线自动巡航，减少人力投入
2. **AI智能分析**：结合计算机视觉技术，自动识别异常情况并生成预警
3. **实时数据传输**：高速数据链路确保视频和传感器数据实时回传
4. **可视化指挥平台**：直观的地图界面和数据展示，提升监控效率
5. **系统集成能力**：可与现有政务系统无缝对接，形成完整的数据闭环

## 系统架构设计

### 1. 模块划分

系统主要分为四大核心模块：

#### 1.1 地图渲染与交互层

作为系统的核心展示界面，负责：
- 地图初始化与配置
- 多图层管理（无人机、任务、预警等）
- 坐标拾取与区域绘制
- 2D/3D视图切换
- 地图视角控制

#### 1.2 无人机管理模块

负责无人机资源的管理与监控：
- 无人机列表与状态展示
- 无人机实时位置追踪
- 无人机视频流展示
- 无人机控制指令发送
- 无人机数据统计分析

#### 1.3 任务管理模块

负责无人机任务的规划与执行：
- 任务创建与区域规划
- 航线设计与优化
- 任务执行监控
- 任务结果查看
- 任务历史记录管理

#### 1.4 预警信息模块

负责AI分析结果的展示与处理：
- 预警信息列表展示
- 预警信息分类与筛选
- 预警详情查看
- 预警处理流程管理
- 预警统计与分析

### 2. 状态管理设计

基于Vue 3和Pinia的状态管理方案，确保数据流清晰、组件解耦、状态可追踪。

#### 2.1 核心Store设计

系统采用模块化的状态管理设计，将不同功能领域的状态分离到独立的Store中：

##### 认证状态管理 (authStore)
管理用户认证、登录状态、权限控制等认证相关状态

##### 地图状态管理 (mapStore)
管理地图实例、图层、绘制状态等核心地图相关状态

##### 无人机状态管理 (droneStore)
管理无人机列表、状态、位置、视频流等无人机相关状态

##### 任务状态管理 (taskStore)
管理任务列表、任务创建流程、任务执行状态等任务相关状态

##### 预警状态管理 (warningStore)
管理预警信息列表、筛选条件、处理状态等预警相关状态

##### UI状态管理 (uiStore)
管理界面布局、面板状态、视图模式切换等UI相关状态

#### 2.2 状态管理原则

1. **单一数据源**：每类数据只在一个store中管理，避免状态重复
2. **最小状态集**：只存储必要的状态，派生状态通过getter计算
3. **状态隔离**：不同模块的状态相互隔离，通过明确的接口交互
4. **响应式设计**：所有状态变更都是响应式的，自动触发UI更新
5. **可调试性**：支持Vue DevTools，方便调试和状态追踪

#### 2.3 UI状态管理详细设计

UI状态管理(uiStore)是系统界面交互的核心，负责管理所有UI相关的状态：

##### 视图模式管理
- **viewMode**: 'normal' | 'flight' - 控制系统的两种主要视图模式
  - normal: 正常模式，显示地图背景 + 左右面板 + 底部面板
  - flight: 飞控模式，显示视频流背景 + 飞控专用面板
- **showMonitorCards**: 控制监控卡片的显示状态

##### 面板状态管理
- **activeRightPanelTab**: 'warning' | 'flight-task' | 'image-search' - 右侧面板当前激活的标签页
- **leftPanelState**: 'expanded' | 'collapsed' - 左侧面板展开/收起状态
- **bottomPanelState**: 'visible' | 'hidden' - 底部面板显示/隐藏状态
- **rightPanelVisible**: 右侧面板可见性控制
- **leftPanelVisible**: 左侧面板可见性控制

##### 全局UI状态
- **isFullscreen**: 全屏模式状态
- **isLoading**: 全局加载状态
- **errorMessage**: 错误信息显示
- **successMessage**: 成功信息显示

##### 核心交互方法
- **setViewMode()**: 设置视图模式
- **enterFlightMode()**: 进入飞控模式
- **exitFlightMode()**: 退出飞控模式
- **toggleLeftPanel()**: 切换左侧面板状态
- **setActiveRightPanelTab()**: 设置右侧面板激活标签
- **showError()** / **showSuccess()**: 显示消息提示

#### 2.4 地图状态管理详细设计

地图状态管理(mapStore)是系统地图功能的核心，负责管理所有地图相关的状态：

##### 地图实例管理
- **mapInstance**: 地图实例对象，Mars3D地图的核心引用
- **isMapInitialized**: 地图初始化状态
- **isMapLoading**: 地图加载状态

##### 视图与配置管理
- **viewMode**: '2D' | '3D' - 地图视图模式切换
- **baseMapType**: 'satellite' | 'terrain' | 'street' | 'dark' - 底图类型
- **mapConfig**: 地图配置对象，包含中心点、缩放级别、倾斜角度等

##### 图层管理
- **layers**: 图层列表，包含无人机、任务、预警等各类图层
- **visibleLayers**: 可见图层列表（计算属性）
- **layerStats**: 图层统计信息（计算属性）

##### 交互状态管理
- **drawingState**: 'none' | 'point' | 'line' | 'polygon' | 'rectangle' | 'circle' - 绘制状态
- **selectedFeature**: 当前选中的地图要素
- **isPickingCoordinate**: 坐标拾取状态

##### 核心操作方法
- **setMapInstance()**: 设置地图实例
- **setViewMode()**: 切换2D/3D视图
- **setBaseMapType()**: 切换底图类型
- **addLayer()** / **removeLayer()**: 图层添加/删除
- **startDrawing()** / **stopDrawing()**: 开始/停止绘制
- **startPickingCoordinate()**: 开始坐标拾取

#### 2.5 无人机状态管理详细设计

无人机状态管理(droneStore)负责管理所有无人机相关的状态和操作：

##### 无人机数据管理
- **drones**: 无人机列表，包含所有无人机的详细信息
- **selectedDroneId**: 当前选中的无人机ID
- **selectedDrone**: 当前选中的无人机对象（计算属性）

##### 状态统计
- **onlineDronesCount**: 在线无人机数量（计算属性）
- **droneStats**: 无人机状态统计，包含总数、在线、工作中、离线等各状态数量

##### 数据加载与视频流
- **isLoadingDrones**: 无人机数据加载状态
- **videoStreamActive**: 视频流激活状态

##### 无人机信息结构
```typescript
interface DroneInfo {
  id: string              // 无人机唯一标识
  name: string            // 无人机名称
  status: DroneStatus     // 状态：在线/工作中/离线/异常/维护
  batteryLevel: number    // 电池电量百分比
  workload?: number       // 工作负载百分比
  position?: {            // 位置信息
    longitude: number
    latitude: number
    altitude: number
  }
  videoStreamUrl?: string // 视频流地址
  lastUpdateTime?: string // 最后更新时间
}
```

##### 核心操作方法
- **setDrones()**: 设置无人机列表
- **addDrone()** / **removeDrone()**: 添加/删除无人机
- **updateDrone()**: 更新无人机信息
- **selectDrone()**: 选择无人机
- **setVideoStreamActive()**: 设置视频流状态

## 系统功能设计

### 1. 地图渲染与交互

系统采用Mars3D作为地图渲染引擎，提供以下核心功能：
- 2D/3D地图切换
- 多图层管理（无人机、任务、预警等）
- 坐标拾取与区域绘制
- 地图视角控制
- 实时数据图层更新

用户可以通过地图界面直观地查看无人机位置、任务区域、预警点等信息。

### 2. 无人机管理

无人机管理模块提供完整的无人机监控功能：
- 无人机列表与状态展示
- 实时位置追踪
- 视频流展示
- 控制指令发送
- 电池电量监控
- 飞行路径实时监控
- 数据统计分析

### 3. 任务管理

任务管理模块支持完整的任务生命周期管理：
- 任务创建与区域规划
- 航线设计与优化
- 任务执行监控
- 任务结果查看
- 任务分组与筛选
- 任务统计功能
- 历史记录管理

### 4. 预警信息

预警信息模块负责AI分析结果的展示与处理：
- 预警信息列表展示
- 预警信息分类与筛选
- 预警详情查看
- 预警处理流程管理
- 预警统计与分析
- 快速响应机制

### 5. 视图模式切换

系统支持两种主要视图模式：

#### 5.1 正常模式 (Normal Mode)
- 地图作为主背景
- 显示左侧面板（任务管理、无人机状态等）
- 显示右侧面板（预警信息、飞行任务、时空搜图）
- 显示底部面板（控制工具栏）

#### 5.2 飞控模式 (Flight Mode)
- 视频流作为主背景
- 显示飞控专用面板
- 地图缩小为右下角卡片
- 专门的飞控操作界面

## 系统性能优化

### 1. 数据传输优化
- 采用高速数据链路，确保视频和传感器数据实时回传
- 数据压缩和优化处理，减少传输量
- WebSocket连接管理，支持断线重连
- 数据缓存机制，提高响应速度

### 2. 状态管理优化
- 基于Vue 3和Pinia的响应式状态管理
- 模块化Store设计，避免状态重复和数据冗余
- 状态持久化，支持页面刷新后状态恢复
- 状态更新防抖，避免频繁渲染

### 3. 地图渲染优化
- Mars3D引擎优化配置
- 图层按需加载和卸载
- 大数据量图层分块渲染
- 视图更新节流处理
- 内存管理和垃圾回收

### 4. 组件性能优化
- 组件懒加载和代码分割
- 虚拟滚动处理大列表
- 图片懒加载和预加载
- 防抖和节流处理用户交互
- 组件缓存和复用

### 5. 网络请求优化
- 请求合并和批处理
- 接口缓存策略
- 请求超时和重试机制
- 并发请求控制
- 错误处理和降级方案

## 技术实现要点

### 1. 前端技术栈
- **Vue 3**: 使用组合式API，提供更好的TypeScript支持
- **TypeScript**: 完整的类型系统，提高代码质量
- **Vite**: 现代化构建工具，快速开发体验
- **Element Plus**: UI组件库，暗色主题适配
- **Mars3D**: 三维地图引擎，支持2D/3D切换
- **Pinia**: 状态管理，模块化设计

### 2. 开发规范
- ESLint + Prettier + OxLint 代码质量保证
- 组件自动导入，提升开发效率
- SCSS变量系统，统一样式管理
- TypeScript严格模式，类型安全

### 3. 部署与运维
- 开发环境代理配置，解决跨域问题
- 生产环境优化构建
- 环境变量管理
- 错误监控和日志收集

---

**文档版本**: v1.0  
**最后更新**: 2025-06-14  
**维护状态**: 活跃开发中
