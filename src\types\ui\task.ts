/**
 * 飞行任务相关类型定义
 */

// 任务用途枚举
export type TaskPurpose = '拍摄' | '全景' | '三维' | '正射' | '视频'

// 任务紧急度枚举
export type TaskUrgency = '紧急' | '不紧急' | '一般'

// 任务形状枚举
export type TaskShape = '面状' | '线状' | '点状'

// 任务状态枚举
export type TaskStatus = '待执行' | '执行中' | '已完成' | '已取消' | '异常' | '暂停中'

// 无人机状态枚举
export type DroneStatus = '在线' | '工作中' | '离线' | '异常' | '维护'

// 无人机信息接口
export interface DroneInfo {
  id: string
  name: string
  status: DroneStatus
  batteryLevel: number // 电池电量百分比
  workload?: number // 工作负载百分比
}

// 创建任务请求接口
export interface CreateTaskRequest {
  // 任务基本信息
  name: string // 任务名称
  shape: TaskShape // 形状
  perimeter: number // 周长
  area: number // 面积（km²）
  centerLongitude: number // 中心经度
  centerLatitude: number // 中心纬度
  locationDetail: string // 位置详情

  // 任务配置
  purpose: TaskPurpose // 用途
  urgency: TaskUrgency // 紧急度
  remark?: string // 备注（可选，最大200字符）

  // 无人机选择
  selectedDroneId: string // 选中的无人机ID
}

// 创建任务响应接口
export interface CreateTaskResponse {
  code: number
  message: string
  data?: {
    taskId: string
    createdAt: string
  }
}

// 任务详情接口 - 统一的任务数据类型
export interface TaskDetail {
  id: string
  name: string // 任务名称/标题
  shape: TaskShape
  perimeter: number
  area: number
  centerLongitude: number
  centerLatitude: number
  locationDetail: string // 详细地点信息
  purpose: TaskPurpose
  urgency: TaskUrgency
  remark?: string
  status: TaskStatus
  droneId?: string
  droneName?: string
  createdAt: string
  updatedAt: string
  startTime?: string
  endTime?: string
  executeTime?: string // 执行时间
  thumbnail?: string // 任务缩略图 image url
  markCount?: number // 标记数量
  photoCount?: number // 照片数量
}

// 任务列表查询参数接口
export interface TaskListParams {
  page?: number
  pageSize?: number
  dateRange?: [Date, Date] | null
  status?: TaskStatus | null
  purpose?: TaskPurpose | null
  searchKeyword?: string
}

// 任务列表响应接口
export interface TaskListResponse {
  data: TaskDetail[]
  total: number
}

// 任务无人机预计信息（根据地图计算得出）
export interface TaskDroneEstimate {
  droneId: string // 无人机ID
  droneName: string // 无人机名称
  status: DroneStatus // 无人机状态
  batteryLevel: number // 电池电量百分比
  nestDistance: number // 机巢距离（km）
  estimatedTime: number // 预计耗时（分钟）
  estimatedMileage: number // 预计里程（km）
  flightSpeed: number // 飞行速度（米/秒）
  photoCount: number // 预计照片数量
}
