/**
 * 通用UI组件数据模型
 * 这里放置可以在整个应用中复用的基础类型
 */

// 日期范围类型
export interface DateRange {
  startDate: Date
  endDate: Date
}

// 控制按钮数据类型
export interface ControlButtonData {
  value: string
  label: string
  active?: boolean
}

// 通用列表项类型
export interface ListItem {
  id: string
  name: string
  [key: string]: any
}

// 分页参数类型
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

// 筛选参数类型
export interface FilterParams {
  keyword?: string
  status?: string
  type?: string
  startDate?: string
  endDate?: string
}

// 排序参数类型
export interface SortParams {
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 操作按钮类型
export interface ActionButton {
  label: string
  action: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: string
  disabled?: boolean
}

// 状态标签类型
export interface StatusTag {
  label: string
  type: 'success' | 'warning' | 'danger' | 'info' | 'default'
  color?: string
}



// DataDisplay组件数据类型
export interface DataDisplayItem {
  title: string
  value: number | string
  unit: string
  icon: string
  iconColor?: string
}

// 列表项组件数据类型
export interface ListItemData {
  id: string
  title: string
  subtitle?: string
  status?: string
  icon?: string
  actions?: Array<{
    label: string
    action: string
    type?: 'primary' | 'success' | 'warning' | 'danger'
  }>
}

// 卡片组件数据类型
export interface CardData {
  id: string
  title: string
  content: string
  image?: string
  status?: string
  tags?: string[]
  actions?: Array<{
    label: string
    action: string
  }>
}
