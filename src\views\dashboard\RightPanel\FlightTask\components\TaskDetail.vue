<template>
  <div class="task-detail">
    <!-- 详情头部 -->
    <div class="detail-header">
      <div class="header-left">
        <el-button size="small" :icon="ArrowLeft" @click="handleBack" class="back-btn">
          任务详情
        </el-button>
      </div>
    </div>

    <!-- 详情内容 -->
    <div class="detail-content">
      <el-scrollbar class="detail-scrollbar">
        <div class="detail-sections">
          <!-- 详细信息部分 -->
          <div class="detail-section">
            <div class="section-title">详细信息</div>
            <div class="section-content">
              <!-- 任务状态和基本信息 -->
              <div class="task-status-info">
                <div class="status-row">
                  <span class="status-icon">📍</span>
                  <span class="status-text">{{
                    task?.name || '浙江省衢州市龙游县某白石镇的西南区域村庄区域'
                  }}</span>
                </div>
                <div class="status-row">
                  <span class="status-icon">🔧</span>
                  <span class="status-text">{{ getStatusText(task?.status) }}</span>
                </div>
                <div class="status-row">
                  <span class="status-icon">⏰</span>
                  <span class="status-text">{{ formatTime(task?.updatedAt) }}</span>
                </div>
                <div class="status-row">
                  <span class="status-text">巡检轮次1</span>
                </div>
              </div>

              <!-- 缩略图和统计信息 -->
              <div class="thumbnail-section">
                <div class="thumbnail-container">
                  <img
                    v-if="task?.thumbnail"
                    :src="task.thumbnail"
                    :alt="task.name"
                    class="task-thumbnail"
                  />
                  <div v-else class="thumbnail-placeholder">
                    <el-icon class="placeholder-icon"><Picture /></el-icon>
                  </div>

                  <!-- 统计信息覆盖层 -->
                  <div class="thumbnail-overlay">
                    <div class="overlay-stats">
                      <span class="stat-item">
                        <el-icon><Location /></el-icon>
                        标记{{ task?.markCount || 0 }}
                      </span>
                      <span class="stat-item">
                        <el-icon><Picture /></el-icon>
                        照片{{ task?.photoCount || 0 }}
                      </span>
                    </div>
                    <div class="overlay-date">{{ formatDate(task?.createdAt) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 基础信息部分 -->
          <div class="detail-section">
            <div class="section-title">基础信息</div>
            <div class="section-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">形状:</span>
                  <span class="info-value">{{ task?.shape || '面状' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">面积:</span>
                  <span class="info-value">{{ task?.area || '50m' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">中心经度:</span>
                  <span class="info-value">{{
                    task?.centerLongitude || '119.156490124184436'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">中心纬度:</span>
                  <span class="info-value">{{ task?.centerLatitude || '29.129619043741296' }}</span>
                </div>
                <div class="info-item full-width">
                  <span class="info-label">位置详情:</span>
                  <span class="info-value">{{
                    task?.locationDetail || '浙江省衢州市龙游县某白石镇的西南区域村庄区域-119'
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 巡检信息部分 -->
          <div class="detail-section">
            <div class="section-title">巡检信息</div>
            <div class="section-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">用途:</span>
                  <span class="info-value">{{ task?.purpose || '全景' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">紧急度:</span>
                  <span class="info-value">{{ task?.urgency || '--' }}</span>
                </div>
                <div class="info-item full-width">
                  <span class="info-label">备注:</span>
                  <span class="info-value">{{ task?.remark || '不紧急' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 接受信息部分 -->
          <div class="detail-section">
            <div class="section-title">提交信息</div>
            <div class="section-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">提交人:</span>
                  <span class="info-value">李明</span>
                </div>
                <div class="info-item">
                  <span class="info-label">提交时间:</span>
                  <span class="info-value">{{ task?.executeTime || '暂无数据' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">紧急度:</span>
                  <span class="info-value">不紧急</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 底部确定按钮 -->
    <div class="detail-footer">
      <el-button type="primary" size="default" @click="handleConfirm" class="confirm-btn">
        确定
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { ArrowLeft, Picture, Location } from '@element-plus/icons-vue'
import type { TaskDetail } from '@/types/ui'

// Props
const props = defineProps<{
  task: TaskDetail | null
  loading?: boolean
}>()

// Emits
const emit = defineEmits<{
  back: []
  confirm: []
}>()

// 计算属性：为了在模板中方便使用
const task = computed(() => props.task)

// 事件处理
const handleBack = () => {
  emit('back')
}

const handleConfirm = () => {
  emit('confirm')
}

// 工具函数
const getStatusText = (status?: string) => {
  return status || '待执行'
}

const formatTime = (isoString?: string) => {
  if (!isoString) return '不紧急 9:00-17:00 04-02至09-29'
  const date = new Date(isoString)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `不紧急 9:00-17:00 ${month}-${day}至09-29`
}

const formatDate = (isoString?: string) => {
  if (!isoString) return '2025年04月02日'
  const date = new Date(isoString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}年${month}月${day}日`
}
</script>

<style scoped lang="scss">
.task-detail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba($bg-card, 0.1);
  border-radius: $border-radius-base;
}

// 详情头部
.detail-header {
  flex-shrink: 0;
  padding: 0.75rem;
  border-bottom: 1px solid $border-color-light;

  .back-btn {
    background: transparent !important;
    border: none !important;
    color: $text-active !important;
    font-size: $font-size-panel-title;
    padding: 0 !important;

    &:hover {
      color: $primary-color !important;
    }
  }
}

// 详情内容
.detail-content {
  flex: 1;
  min-height: 0;

  .detail-scrollbar {
    height: 100%;
  }

  .detail-sections {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
}

// 详情分组
.detail-section {
  .section-title {
    font-size: $font-size-panel-normal;
    color: $text-active;
    font-weight: 500;
    margin-bottom: 0.5rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid rgba($primary-color, 0.3);
  }

  .section-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
}

// 任务状态信息
.task-status-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  .status-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: $font-size-panel-label;

    .status-icon {
      font-size: 1rem;
    }

    .status-text {
      color: $text-default;
    }
  }
}

// 缩略图部分
.thumbnail-section {
  margin-top: 0.5rem;

  .thumbnail-container {
    position: relative;
    width: 100%;
    height: 8rem;
    border-radius: $border-radius-base;
    overflow: hidden;
    background: rgba($bg-medium, 0.3);

    .task-thumbnail {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .thumbnail-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: $text-inactive;

      .placeholder-icon {
        font-size: 2rem;
      }
    }

    .thumbnail-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
      padding: 0.5rem;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;

      .overlay-stats {
        display: flex;
        gap: 0.5rem;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          color: $text-default;
          font-size: $font-size-panel-caption;
        }
      }

      .overlay-date {
        color: $text-default;
        font-size: $font-size-panel-caption;
      }
    }
  }
}

// 信息网格
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    &.full-width {
      grid-column: 1 / -1;
    }

    .info-label {
      font-size: $font-size-panel-caption;
      color: $text-secondary;
    }

    .info-value {
      font-size: $font-size-panel-label;
      color: $text-default;
      word-break: break-all;
    }
  }
}

// 详情底部
.detail-footer {
  flex-shrink: 0;
  padding: 0.75rem;
  border-top: 1px solid $border-color-light;
  display: flex;
  justify-content: center;

  .confirm-btn {
    width: 100%;
    background: $primary-color !important;
    border-color: $primary-color !important;
    color: $bg-dark !important;
    font-weight: 500;

    &:hover {
      background: rgba($primary-color, 0.8) !important;
      border-color: rgba($primary-color, 0.8) !important;
    }
  }
}
</style>
