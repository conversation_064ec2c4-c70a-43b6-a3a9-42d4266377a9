<template>
  <div class="bar-chart-container" ref="chartContainerRef">
    <v-chart v-if="chartReady" class="chart" :option="chartOption" autoresize />
  </div>
</template>

<script setup lang="ts">
import VChart, { THEME_KEY } from 'vue-echarts'
import { provide, computed } from 'vue'
import { useEChartsResize } from '@/composables/useChartResize'
import { use } from 'echarts/core'
import { BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { ComposeOption } from 'echarts/core'
import type { BarSeriesOption } from 'echarts/charts'
import type {
  TitleComponentOption,
  TooltipComponentOption,
  GridComponentOption,
  DataZoomComponentOption,
} from 'echarts/components'

// 注册必要的组件
use([TitleComponent, TooltipComponent, GridComponent, DataZoomComponent, Bar<PERSON>hart, <PERSON>vas<PERSON><PERSON><PERSON>])

// 定义图表选项类型
type EChartsOption = ComposeOption<
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | DataZoomComponentOption
  | BarSeriesOption
>

// 定义数据项类型
interface BarItem {
  name: string
  value: number
}

// 定义组件属性
interface Props {
  // 图表标题
  title?: string
  // 数据项
  data?: BarItem[]
  // 柱状图颜色
  barColor?: string
  // 是否显示数值标签
  showLabel?: boolean
  // 图表高度
  height?: string
  // Y轴最大值
  maxValue?: number
  // 是否显示X轴标签
  showXAxisLabel?: boolean
  // 是否显示Y轴
  showYAxis?: boolean
  // 柱状图宽度比例
  barWidth?: number
  // 数值单位
  unit?: string
}

// 组件属性默认值
const props = defineProps<Props>()

// 默认属性值
const defaultProps = {
  title: '每日巡检次数',
  data: [
    { name: '2025-03-27', value: 10 },
    { name: '2025-03-28', value: 5 },
    { name: '2025-03-29', value: 6 },
    { name: '2025-03-30', value: 5 },
    { name: '2025-03-31', value: 1 },
    { name: '2025-04-01', value: 15 },
    { name: '2025-04-02', value: 8 },
  ],
  barColor: '#00FFFE',
  showLabel: true,
  height: '5rem',
  showXAxisLabel: true,
  showYAxis: true,
  barWidth: 0.4,
  unit: '次',
}

// 计算图表选项
const chartOption = computed<EChartsOption>(() => {
  const data = props.data || defaultProps.data
  const unit = props.unit || defaultProps.unit

  // 智能计算Y轴最大值
  const calculateMaxValue = () => {
    if (props.maxValue) {
      // 如果明确传入了maxValue，则使用传入的值
      return props.maxValue
    }

    const dataValues = data.map((item) => item.value)
    const maxDataValue = Math.max(...dataValues)

    // 智能计算：给最大值增加20%的空间，并向上取整到合适的数值
    const padding = maxDataValue * 0.2
    const suggestedMax = maxDataValue + padding

    // 向上取整到合适的数值（比如：23 -> 25, 67 -> 70, 123 -> 130）
    const magnitude = Math.pow(10, Math.floor(Math.log10(suggestedMax)))
    const normalized = suggestedMax / magnitude

    let roundedNormalized
    if (normalized <= 1) roundedNormalized = 1
    else if (normalized <= 2) roundedNormalized = 2
    else if (normalized <= 5) roundedNormalized = 5
    else roundedNormalized = 10

    return roundedNormalized * magnitude
  }

  const maxValue = calculateMaxValue()

  return {
    backgroundColor: 'transparent',
    title: props.title
      ? {
          text: props.title,
          textStyle: {
            color: '#00FFFE',
            fontSize: 12,
          },
          left: '10px',
          top: 0,
        }
      : undefined,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: `{b}: {c}${unit}`,
    },
    grid: {
      left: props.showYAxis ? '15%' : '5%',
      right: '5%',
      bottom: props.showXAxisLabel ? '15%' : '5%',
      top: props.title ? '15%' : '5%',
      containLabel: false,
    },
    xAxis: {
      type: 'category',
      data: data.map((item) => item.name),
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: props.showXAxisLabel ?? defaultProps.showXAxisLabel,
        color: '#00FFFE',
        fontSize: 10,
        interval: 0,
        rotate: 0,
        formatter: function (value: string) {
          // 只显示日期的最后部分，如04-02
          const parts = value.split('-')
          if (parts.length >= 3) {
            return `${parts[1]}-${parts[2]}`
          }
          return value
        },
      },
    },
    yAxis: {
      type: 'value',
      max: maxValue,
      splitNumber: 5,
      show: props.showYAxis ?? defaultProps.showYAxis,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(85, 85, 85, 0.3)',
          type: 'dashed',
        },
      },
      axisLabel: {
        color: '#e1e1e1',
        fontSize: 10,
        formatter: function (value: number) {
          return `-${value}${unit}`
        },
      },
    },
    series: [
      {
        name: props.title || defaultProps.title,
        type: 'bar',
        data: data.map((item) => item.value),
        barWidth: `${props.barWidth ? props.barWidth * 100 : defaultProps.barWidth * 100}%`,
        itemStyle: {
          color: props.barColor || defaultProps.barColor,
        },
        label: {
          show: props.showLabel ?? defaultProps.showLabel,
          position: 'top',
          color: props.barColor || defaultProps.barColor,
          fontSize: 12,
          formatter: `{c}${unit}`,
        },
      },
    ],
  }
})

// 使用图表尺寸监听组合函数
const { chartContainerRef, chartReady } = useEChartsResize('BarChart')

// 设置暗色主题
provide(THEME_KEY, 'dark')
</script>

<style scoped>
.bar-chart-container {
  width: 100%;
  height: v-bind('props.height || defaultProps.height');
}
</style>
