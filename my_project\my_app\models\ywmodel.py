from django.db import models

class TtGlwZhbzfx(models.Model):
    id = models.BigIntegerField(primary_key=True)
    mc = models.CharField(max_length=255, blank=True, null=True)
    rwfw = models.CharField(max_length=255, blank=True, null=True)
    start_point = models.CharField(max_length=255, blank=True, null=True)
    rwbj = models.CharField(max_length=255, blank=True, null=True)
    sjbj = models.CharField(max_length=255, blank=True, null=True)
    rwtu = models.CharField(max_length=255, blank=True, null=True)
    baogao = models.TextField()
    create_user = models.CharField(max_length=255, blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_ZHBZFX'
        db_table_comment = '综合保障分析'

class TtGlwZymbfx(models.Model):
    id = models.BigIntegerField(primary_key=True)
    mc = models.CharField(max_length=255, blank=True, null=True)
    rwfw = models.CharField(max_length=255, blank=True, null=True)
    start_point = models.CharField(max_length=255, blank=True, null=True)
    rwbj = models.CharField(max_length=255, blank=True, null=True)
    sjbj = models.CharField(max_length=255, blank=True, null=True)
    rwtu = models.CharField(max_length=255, blank=True, null=True)
    baogao = models.TextField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_ZYMBFX'
        db_table_comment = '重要目标分析'

class TtGlwPointMark(models.Model):
    id = models.BigIntegerField(primary_key=True)
    title = models.CharField(max_length=255, blank=True, null=True)
    type_str = models.CharField(max_length=255, blank=True, null=True, db_comment='关键点类型中文:类型1，类型2，等')
    type_index = models.BigIntegerField(blank=True, null=True, db_comment='关键点类型序号。1：类型1，2：类型2，等')
    icon_str = models.CharField(max_length=255, blank=True, null=True, db_comment='关键点位的图标的路径字符串')
    icon_index = models.BigIntegerField(blank=True, null=True, db_comment='关键点位的图标序号')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_POINT_MARK'
        db_table_comment = '人工点位标注'

class RencheDict(models.Model):
    id = models.BigIntegerField(primary_key=True)
    mc = models.CharField(max_length=255, blank=True, null=True)
    long = models.CharField(max_length=255, blank=True, null=True)
    width = models.CharField(max_length=255, blank=True, null=True)
    high = models.CharField(max_length=255, blank=True, null=True)
    load = models.CharField(max_length=255, blank=True, null=True)
    area = models.FloatField(blank=True, null=True)
    type = models.SmallIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'renche_dict'
        db_table_comment = '人车面积'


class TtGlwJjdy(models.Model):
    id = models.BigIntegerField(primary_key=True)
    mc = models.CharField(max_length=255, blank=True, null=True)
    rwfw = models.CharField(max_length=255, blank=True, null=True)
    start_point = models.CharField(max_length=255, blank=True, null=True)
    rwbj = models.CharField(max_length=255, blank=True, null=True)
    sjbj = models.CharField(max_length=255, blank=True, null=True)
    rwtu = models.CharField(max_length=255, blank=True, null=True)
    lists = models.CharField(max_length=255, blank=True, null=True)
    baogao = models.TextField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_JJDY'
        db_table_comment = '集结地域分析'

class TtGlwGcwzfx(models.Model):
    id = models.BigIntegerField(primary_key=True)
    mc = models.CharField(max_length=255, blank=True, null=True)
    rwfw = models.CharField(max_length=255, blank=True, null=True)
    start_point = models.CharField(max_length=255, blank=True, null=True)
    rwbj = models.CharField(max_length=255, blank=True, null=True)
    people_number = models.BigIntegerField(blank=True, null=True)
    baogao = models.TextField(blank=True, null=True)  # This field type is a guess.
    create_time = models.DateTimeField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    sjbj = models.CharField(max_length=255, blank=True, null=True)
    rwtu = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_GCWZFX'
        db_table_comment = '观察位置'

class TtGlwWwfk(models.Model):
    id = models.BigIntegerField(primary_key=True)
    mc = models.CharField(max_length=255, blank=True, null=True)
    event_data = models.TextField(blank=True, null=True)  # This field type is a guess.
    baogao = models.TextField(blank=True, null=True)  # This field type is a guess.
    create_time = models.DateTimeField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_WWFK'
        db_table_comment = '外围封控'

class TtGlwQsfx(models.Model):
    id = models.BigIntegerField(primary_key=True)
    mc = models.CharField(max_length=255, blank=True, null=True)
    rwtu = models.TextField(blank=True, null=True)
    baogao = models.TextField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_QSFX'
        db_table_comment = '驱散方向'

class TtGlwDlhjfx(models.Model):
    id = models.BigIntegerField(primary_key=True)
    mc = models.CharField(max_length=255, blank=True, null=True)
    event_data = models.TextField(blank=True, null=True)  # This field type is a guess.
    baogao = models.TextField(blank=True, null=True)  # This field type is a guess.
    create_time = models.DateTimeField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_DLHJFX'
        db_table_comment = '地理环境分析'

class WjyyGzSysModelWeight(models.Model):
    id = models.BigIntegerField(primary_key=True)
    title = models.CharField(max_length=255, blank=True, null=True, db_comment='模型名称_中文')
    value = models.CharField(max_length=255, blank=True, null=True, db_comment='模型简称_英文')
    create_time = models.DateTimeField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    model_type = models.BigIntegerField(blank=True, null=True, db_comment='模型类型：0：集结地，1：驱散方向')
    template_id = models.BigIntegerField(blank=True, null=True, db_comment='模板id')
    filter = models.CharField(max_length=255, blank=True, null=True, db_comment='权重条件')

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_MODEL_WEIGHT'
        db_table_comment = '后台管理_模型权重管理'

class SysModelWeightTemplate(models.Model):
    id = models.BigIntegerField(primary_key=True)
    title = models.CharField(max_length=255, blank=True, null=True)
    is_active = models.BooleanField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    status = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_MODEL_WEIGHT_TEMPLATE'
        db_table_comment = '后台管理_模型权重模板管理'

class SysModelRuls(models.Model):
    id = models.BigIntegerField(primary_key=True)
    create_time = models.DateTimeField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    moban_title = models.CharField(max_length=255, blank=True, null=True, db_comment='模板标题')
    moban_data = models.TextField(blank=True, null=True, db_comment='模板概述')  # This field type is a guess.
    disable = models.BigIntegerField(blank=True, null=True, db_comment='是否禁用：0：启用，1：禁用，同类型的模板只能启动一个')
    moban_type = models.CharField(max_length=255, blank=True, null=True, db_comment='模板类型')

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_MODEL_RULS'
        db_table_comment = '后台管理_模板概述编辑'


class SysModelRulsFields(models.Model):
    id = models.BigIntegerField(primary_key=True)
    title = models.CharField(max_length=255, blank=True, null=True, db_comment='模板名称')
    create_time = models.DateTimeField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    fields = models.TextField(blank=True, null=True, db_comment='模板对应字段的json')  # This field type is a guess.

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_MODEL_RULS_FIELDS'
        db_table_comment = '后台管理_模板概述插入的字段'

class TtGlwZhfx(models.Model):
    id = models.BigIntegerField(primary_key=True)
    mc = models.CharField(max_length=255, blank=True, null=True)
    # rwtu = models.TextField(blank=True, null=True)
    baogao = models.JSONField(blank=True, null=True)
    sjbj = models.CharField(max_length=255, blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    update_user = models.CharField(max_length=255, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_ZHFX'
        db_table_comment = '综合分析表'

class WjyyGzBggl(models.Model):
    id = models.BigIntegerField(primary_key=True)
    mc = models.CharField(max_length=255, blank=True, null=True)
    baogao = models.JSONField(blank=True, null=True)  # This field type is a guess.
    create_time = models.DateTimeField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    bg_type = models.BigIntegerField(blank=True, null=True, db_comment='1:')

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_BGGL'
        db_table_comment = '报告管理'

    @classmethod
    def get_es_index_name(cls):
        return "wjyy_gz_bggl"

# wjx 事件管理 4.15
class WjyyGzFxsj(models.Model):
    id = models.BigIntegerField(primary_key=True,db_comment="id")
    mc = models.CharField(max_length=90, blank=True, null=True,db_comment="名称")
    sjfxxx = models.JSONField(blank=True, null=True,db_comment="事件分析信息")  # This field type is a guess.
    create_time = models.DateTimeField(blank=True, null=True,db_comment="创建时间")
    create_user = models.CharField(max_length=255, blank=True, null=True,db_comment="创建人")
    create_user_id = models.BigIntegerField(blank=True, null=True,db_comment="创建人id")
    update_time = models.DateTimeField(blank=True, null=True,db_comment="更新时间")
    update_user = models.CharField(blank=True, null=True,db_comment="更新人")
    update_user_id = models.BigIntegerField(blank=True, null=True,db_comment="更新人id")
    dlhjfxfw = models.CharField(max_length=255, blank=True, null=True,db_comment="地理环境分析范围")
    zhbzfxfw = models.CharField(max_length=255, blank=True, null=True,db_comment="综合保障分析范围")
    zymbfxfw = models.CharField(max_length=255, blank=True, null=True,db_comment="重要目标分析范围")
    jjdyfxfw = models.CharField(max_length=255, blank=True, null=True,db_comment="集结地域分析范围")
    gcwzfxfw = models.CharField(max_length=255, blank=True, null=True,db_comment="观察位置分析范围")
    wwfkfxfw = models.CharField(max_length=255, blank=True, null=True,db_comment="外围封控分析范围")
    qsfxfxfw = models.CharField(max_length=255, blank=True, null=True,db_comment="驱散方向分析范围")
    drawType = models.CharField(max_length=255, blank=True, null=True,db_comment="形状类型")

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_FXSJ'
        db_table_comment = '分析事件'