/**
 * 视频流相关API接口
 */
import type { VideoStreamInfo, MultiScreenUIData, MonitorCardsUIData } from '@/types/ui'

/**
 * 视频流API
 */
export const videoApi = {
  /**
   * 获取多屏监控数据
   */
  getMultiScreenData: (): Promise<MultiScreenUIData> => {
    // 暂时返回模拟数据，后期对接真实API
    return Promise.resolve({
      videoData: {
        nest_inside: [
          {
            id: 'nest-001',
            droneId: 'drone-001',
            deviceName: '塔石测试机',
            imageUrl: 'public/image/jiankong-1.png',
            windSpeed: '2级',
            weather: '多云',
            humidity: '46%RH',
            streamInfo: {
              droneId: 'drone-001',
              streamUrl: 'rtmp://example.com/nest-001',
              streamType: 'nest_inside',
              isActive: true,
              quality: 'HD',
            },
          },
          {
            id: 'nest-002',
            droneId: 'drone-002',
            deviceName: '新型号无人机',
            imageUrl: 'public/image/jiankong-1.png',
            windSpeed: '1级',
            weather: '晴朗',
            humidity: '42%RH',
            streamInfo: {
              droneId: 'drone-002',
              streamUrl: 'rtmp://example.com/nest-002',
              streamType: 'nest_inside',
              isActive: true,
              quality: 'HD',
            },
          },
          {
            id: 'nest-003',
            droneId: 'drone-002',
            deviceName: '新型号无人机',
            imageUrl: 'public/image/jiankong-1.png',
            windSpeed: '1级',
            weather: '晴朗',
            humidity: '42%RH',
            streamInfo: {
              droneId: 'drone-002',
              streamUrl: 'rtmp://example.com/nest-002',
              streamType: 'nest_inside',
              isActive: true,
              quality: 'HD',
            },
          },
          {
            id: 'nest-004',
            droneId: 'drone-002',
            deviceName: '新型号无人机',
            imageUrl: 'public/image/jiankong-1.png',
            windSpeed: '1级',
            weather: '晴朗',
            humidity: '42%RH',
            streamInfo: {
              droneId: 'drone-002',
              streamUrl: 'rtmp://example.com/nest-002',
              streamType: 'nest_inside',
              isActive: true,
              quality: 'HD',
            },
          },
        ],
        nest_outside: [
          {
            id: 'outer-001',
            droneId: 'drone-001',
            deviceName: '塔石测试机',
            imageUrl: 'public/image/jiankong-1.png',
            windSpeed: '2级',
            weather: '多云',
            humidity: '46%RH',
            streamInfo: {
              droneId: 'drone-001',
              streamUrl: 'rtmp://example.com/outer-001',
              streamType: 'nest_outside',
              isActive: false,
              quality: 'SD',
            },
          },
        ],
        live_stream: [
          {
            id: 'live-001',
            droneId: 'drone-001',
            deviceName: '塔石测试机',
            imageUrl: 'public/image/jiankong-1.png',
            windSpeed: '2级',
            weather: '多云',
            humidity: '46%RH',
            streamInfo: {
              droneId: 'drone-001',
              streamUrl: 'rtmp://example.com/live-001',
              streamType: 'live_stream',
              isActive: true,
              quality: 'HD',
            },
          },
          {
            id: 'live-002',
            droneId: 'drone-002',
            deviceName: '新型号无人机',
            imageUrl: 'public/image/jiankong-1.png',
            windSpeed: '1级',
            weather: '晴朗',
            humidity: '42%RH',
            streamInfo: {
              droneId: 'drone-002',
              streamUrl: 'rtmp://example.com/live-002',
              streamType: 'live_stream',
              isActive: false,
              quality: 'SD',
            },
          },
        ],
      },
    })
  },

  /**
   * 获取监控卡片数据
   */
  getMonitorCardsData: (): Promise<MonitorCardsUIData> => {
    return Promise.resolve({
      cards: [
        {
          id: 'main',
          title: '主监控',
          type: 'video',
        },
        {
          id: 'secondary',
          title: '辅助监控',
          type: 'video',
        },
      ],
    })
  },

  /**
   * 获取视频流信息
   * @param droneId 无人机ID
   * @param streamType 视频流类型
   */
  getVideoStreamInfo: (
    droneId: string,
    streamType: VideoStreamInfo['streamType'],
  ): Promise<VideoStreamInfo> => {
    return Promise.resolve({
      droneId,
      streamUrl: `rtmp://example.com/${droneId}-${streamType}`,
      streamType,
      isActive: true,
      quality: 'HD',
    })
  },

  /**
   * 启动视频流
   * @param droneId 无人机ID
   * @param streamType 视频流类型
   */
  startVideoStream: (droneId: string, streamType: VideoStreamInfo['streamType']): Promise<void> => {
    console.log('启动视频流:', droneId, streamType)
    return Promise.resolve()
  },

  /**
   * 停止视频流
   * @param droneId 无人机ID
   * @param streamType 视频流类型
   */
  stopVideoStream: (droneId: string, streamType: VideoStreamInfo['streamType']): Promise<void> => {
    console.log('停止视频流:', droneId, streamType)
    return Promise.resolve()
  },
}
