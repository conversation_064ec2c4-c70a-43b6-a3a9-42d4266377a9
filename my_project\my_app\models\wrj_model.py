# -*- coding: utf-8 -*-
"""
业务模型 - 无人机相关数据模型
从 my_models.yw_model 迁移到 my_app.models.yw_model
"""
from django.db import models


class TtDroneTasks(models.Model):
    """无人机任务表"""
    id = models.BigAutoField(primary_key=True, db_comment="id")
    task_name = models.CharField(max_length=255, db_comment='任务的唯一名称，用于标识和检索任务')
    task_status = models.CharField(max_length=255, default='', db_comment='任务状态')
    task_shape = models.CharField(max_length=50, default='', db_comment='任务区域的几何形状类型')
    perimeter = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, db_comment='任务区域的周长，单位：米')
    area = models.DecimalField(max_digits=10, decimal_places=4, default=0.0000, db_comment='任务区域的面积，单位：平方千米')
    center_longitude = models.DecimalField(max_digits=9, decimal_places=6, default=0.0000, null=True, blank=True, db_comment='任务区域的中心点经度，WGS84坐标系')
    center_latitude = models.DecimalField(max_digits=9, decimal_places=6, default=0.0000, null=True, blank=True, db_comment='任务区域的中心点纬度，WGS84坐标系')
    location_detail = models.TextField(default='', db_comment='任务位置的详细描述信息')
    task_purpose = models.CharField(max_length=50, default='', db_comment='任务的主要用途类型')
    task_urgency = models.CharField(max_length=50, default='', db_comment='任务的紧急程度级别')
    remark = models.CharField(max_length=200, blank=True, null=True, db_comment='任务的附加说明信息，最大长度200字符')
    selected_drone_id = models.CharField(max_length=50, default='', db_comment='执行此任务的无人机唯一标识符')
    task_shape_points = models.CharField(max_length=900, default='', db_comment='区域点数据')
    thumbnail = models.CharField(max_length=900, default='', db_comment='任务缩略图 image url')
    mark_count = models.BigIntegerField(blank=True, default=0, null=True, db_comment='标记数量')
    photo_count = models.BigIntegerField(blank=True, default=0, null=True, db_comment='照片数量')
    shenh = models.BigIntegerField(default=0, db_comment='审核 0待审核 1通过 2未通过')
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    update_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(auto_now=True, db_comment='更新时间')

    class Meta:
        managed = False
        db_table = 'tt_drone_tasks'
        db_table_comment = '任务表'


class TtDroneBasicInfo(models.Model):
    """无人机设备基础信息表"""
    drone_id = models.BigAutoField(primary_key=True, db_comment="id")
    drone_serial = models.CharField(unique=True, max_length=50, db_comment='无人机序列号，设备出厂唯一编号')
    model = models.CharField(max_length=50, db_comment='无人机型号（如 Mavic 3、Phantom 4）')
    brand = models.CharField(max_length=50, db_comment='无人机品牌（如 DJI、Autel、Parrot）')
    weight_kg = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True, db_comment='无人机重量（千克）')
    dimensions = models.CharField(max_length=100, blank=True, null=True, db_comment='无人机尺寸（展开/折叠状态）')
    max_flight_time_min = models.IntegerField(blank=True, null=True, db_comment='最大续航时间（分钟）')
    camera_model = models.CharField(max_length=100, blank=True, null=True, db_comment='搭载的相机型号')
    camera_resolution = models.CharField(max_length=50, blank=True, null=True, db_comment='相机分辨率（如 4K/60fps）')
    gps_precision = models.CharField(max_length=50, blank=True, null=True, db_comment='GPS定位精度（如厘米级/米级）')
    obstacle_avoidance = models.BooleanField(blank=True, null=True, db_comment='是否支持避障功能')
    status = models.CharField(max_length=20, blank=True, null=True, db_comment='设备状态：available(可用)、maintenance(维修中)、retired(报废)')
    battery_capacity_mah = models.IntegerField(blank=True, null=True, db_comment='电池容量（毫安时）')
    firmware_version = models.CharField(max_length=20, blank=True, null=True, db_comment='当前固件版本号')
    department = models.CharField(max_length=50, blank=True, null=True, db_comment='所属部门/团队')
    responsible_person = models.CharField(max_length=50, blank=True, null=True, db_comment='设备负责人')
    purchase_date = models.DateField(blank=True, null=True, db_comment='采购日期')
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, db_comment='采购价格（元）')
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tt_drone_basic_info'
        db_table_comment = '无人机设备基础信息表'


class TtDroneFlightRecords(models.Model):
    """无人机飞行记录表"""
    flight_id = models.BigAutoField(primary_key=True, db_comment="id")
    drone_id = models.BigIntegerField(db_comment='执行飞行的无人机ID，关联drone_basic_info表')
    flight_number = models.CharField(unique=True, max_length=50, db_comment='飞行编号（格式：DRN-YYYYMMDD-XXXX）')
    task_id = models.IntegerField(blank=True, null=True, db_comment='关联的任务ID（可关联任务表）')
    start_time = models.DateTimeField(db_comment='飞行开始时间（UTC+8）')
    end_time = models.DateTimeField(blank=True, null=True, db_comment='飞行结束时间（UTC+8）')
    total_flight_time_sec = models.IntegerField(blank=True, null=True, db_comment='总飞行时长（秒）')
    start_location = models.TextField(blank=True, null=True, db_comment='起飞位置（WGS84坐标系，格式：POINT(longitude latitude)）')
    end_location = models.TextField(blank=True, null=True, db_comment='降落位置（WGS84坐标系，格式：POINT(longitude latitude)）')
    max_altitude_m = models.DecimalField(max_digits=6, decimal_places=2, blank=True, null=True, db_comment='最大飞行高度（米）')
    avg_speed_kmh = models.DecimalField(max_digits=6, decimal_places=2, blank=True, null=True, db_comment='平均飞行速度（千米/小时）')
    weather_conditions = models.JSONField(blank=True, null=True, db_comment='飞行时天气状况（JSON格式：温度、湿度、风速等）')
    flight_path = models.TextField(blank=True, null=True, db_comment='完整飞行轨迹（WGS84坐标系，LINESTRING格式）')
    telemetry_data = models.JSONField(blank=True, null=True, db_comment='实时飞行数据（JSON格式：经纬度、高度、速度等）')
    device_status = models.JSONField(blank=True, null=True, db_comment='设备状态数据（JSON格式：电池电量、电机温度等）')
    operator_id = models.IntegerField(blank=True, null=True, db_comment='操控员ID（可关联用户表）')
    flight_mode = models.CharField(max_length=20, blank=True, null=True, db_comment='飞行模式：auto(自动)、manual(手动)、waypoint(航点)')
    mission_success = models.BooleanField(blank=True, null=True, db_comment='任务是否成功完成')
    notes = models.TextField(blank=True, null=True, db_comment='飞行备注信息')
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    is_del = models.IntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tt_drone_flight_records'
        db_table_comment = '无人机飞行记录表'


class TtDroneAlerts(models.Model):
    """告警记录表"""
    alert_id = models.AutoField(primary_key=True, db_comment='告警记录唯一标识')
    alert_time = models.DateTimeField(db_comment='告警发生时间（带时区）')
    alert_location = models.CharField(max_length=255, blank=True, null=True, db_comment='告警发生位置')
    notes = models.CharField(max_length=255, blank=True, null=True, db_comment='告警发生描述')
    task_id = models.BigIntegerField(blank=True, null=True, db_comment='任务id')
    flight_id = models.BigIntegerField(blank=True, null=True, db_comment='飞行记录id')
    drone_type = models.CharField(max_length=50, blank=True, null=True)
    alert_type_id = models.IntegerField(blank=True, null=True, db_comment='告警类型 30 松材线虫病 31暴露垃圾 32 渣土乱倒33水域污染源 34工地未苫盖')
    alert_level = models.SmallIntegerField(db_comment='告警级别（1-5级，1为最低，10为最高）')
    points = models.CharField(max_length=255, blank=True, null=True, db_comment='警告点')
    areas = models.CharField(max_length=2000, blank=True, null=True, db_comment='警告面')
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    status = models.DateTimeField(blank=True, null=True, db_comment='处理状态 0新发现1反馈处理2处理完成')

    class Meta:
        managed = False
        db_table = 'tt_drone_alerts'
        db_table_comment = '告警记录'


class TtWeatherData(models.Model):
    """天气数据表"""
    weather_id = models.BigIntegerField(primary_key=True)
    wendu = models.CharField(max_length=50, blank=True, null=True)
    xiangduishidu = models.CharField(max_length=50, blank=True, null=True)
    qiya = models.CharField(max_length=50, blank=True, null=True)
    fengsu = models.CharField(max_length=50, blank=True, null=True)
    fengxiang = models.CharField(max_length=50, blank=True, null=True)
    jiangshui = models.CharField(max_length=50, blank=True, null=True)
    tianqi = models.CharField(max_length=50, blank=True, null=True)
    nengjiandu = models.DecimalField(max_digits=7, decimal_places=2, blank=True, null=True)
    yunliang = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    ludianwendu = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    uvzhishu = models.DecimalField(max_digits=4, decimal_places=1, blank=True, null=True)
    weather_date = models.DateField(blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tt_weather_data'
        db_table_comment = '天气表'
