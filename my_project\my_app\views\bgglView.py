# 报告管理相关模块导入
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from my_project.token import ExpiringTokenAuthentication
from rest_framework.response import Response
# 日志相关模块导入
from vgis_log.logTools import LoggerHelper
import logging
from my_app.models import SysLog
# 获取 Django 日志记录器
logger = logging.getLogger('django')

# 获取系统编码，使用 platform 模块获取当前操作系统类型并转换为小写
import platform
platformType = platform.system().lower()

# 时间模块导入，用于处理日期和时间
import datetime

# 给前端返回的语法相关模块导入，用于构建统一的响应格式
from my_app.views.response.baseRespone import Result

# 人工点位标注默认相关模型导入
from my_app.models import WjyyGzBggl

# 数据 id 生成类导入，用于生成唯一的 ID
from my_app.utils.snowflake_id_util import SnowflakeIDUtil

# JSON 转换模块导入，用于处理 JSON 数据
import json

# DRF 自定义请求装饰器导入，用于定义自定义的视图动作
from rest_framework.decorators import action

# 重要目标分析查询类导入，可通过传入 SQL 给其他视图查询使用
# from my_app.manage.importantTargetAnalysisManage import ImportantTargetAnalysisManage

# Django 数据库方法导入，用于与数据库进行交互
from django.db import connection, transaction

# Elasticsearch 相关模块导入，用于与 Elasticsearch 搜索引擎进行交互
from elasticsearch import Elasticsearch
from elasticsearch_dsl import Search
from my_app.manage.ywManager import YwManager

class BgglView(viewsets.ModelViewSet):
    # 设置权限类，要求用户必须经过身份验证才能访问该视图集的接口
    permission_classes = (IsAuthenticated,)
    # 设置认证类，使用自定义的过期令牌认证方式
    authentication_classes = (ExpiringTokenAuthentication,)

    # 初始化 Elasticsearch 客户端，连接到指定的 Elasticsearch 服务
    hosts=['http://***********:9200']

    # 创建点位标记的接口，使用 POST 请求方法，不是针对单个对象的操作
    @action(methods=['POST'], detail=False)
    def create_bg(self, request):
        baogao = request.data['baogao']
        # 定义 API 路径，用于日志记录
        api_path = "/bg/create_bg/"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 定义功能标题，用于日志记录和返回信息
            function_title = "新增报告请，求时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

            # 将请求用户的 ID 赋值给请求数据中的 create_user_id 字段
            request.data["create_user_id"] = request.auth.user_id
            # 判断请求用户对象的类型，如果不是字符串类型，则将用户名赋值给 create_user 字段
            if type(request.auth.user) != 'str':
                request.data["create_user"] = request.auth.user.username
            else:
                request.data["create_user"] = request.auth.user
            # 检查请求数据中是否包含报告类型字段
            if "bg_type" in request.data:
                # 检查请求数据中是否包含报告内容字段
                # obj_data['baogao'] = json.dumps(baogao, ensure_ascii=False)
                # # 同步到 Elasticsearch，此处代码被注释掉，可根据需要启用
                # es = Elasticsearch(hosts=self.hosts)
                # es.index(index=WjyyGzBggl.get_es_index_name(), id=obj_data['id'], body=obj_data)
                # es.close()
                # 清空报告内容
                if "baogao" in request.data:
                    if request.data["baogao"] is None or str(request.data["baogao"]) == "" or str(request.data["baogao"]) == "{}":
                        # 记录警告日志信息
                      LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,request, function_title, "报告内容不能为空", None, 3)
                      return Result.fail("报告内容不能为空", "报告内容不能为空")
                    # request.data["baogao"] = request.data["baogao"]
                # 设置报告的创建时间为当前时间
                request.data["create_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                # 获取报告名称，如果不存在则赋值为 None
                mc = request.data["mc"] if "mc" in request.data else None
                # 检查数据库中是否已存在相同名称的报告
                reportName = WjyyGzBggl.objects.filter(mc=mc)
                if reportName.exists() > 0:
                    # 构建错误信息
                    error_info = "名称:{}已存在,请换个名称".format(mc)
                    # 清空报告内容
                    request.data["baogao"] = None
                    # 记录警告日志信息
                    LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request, function_title, error_info, None,3)
                    # 返回失败结果
                    return Result.fail(error_info, error_info)
                if "report_type" in request.data:
                    # 删除请求数据中的新密码字段
                    del request.data["report_type"]
                # 生成唯一的报告 ID
                request.data['id'] = SnowflakeIDUtil.snowflakeId()
                with transaction.atomic():
                    # 在数据库中创建新的报告记录
                    WjyyGzBggl.objects.create(**request.data)
                    request.data['baogao'] = json.dumps(baogao, ensure_ascii=False)
                    # 同步到 Elasticsearch，此处代码被注释掉，可根据需要启用
                    es = Elasticsearch(hosts=self.hosts)
                    es.index(index=WjyyGzBggl.get_es_index_name(), id=request.data['id'], body=request.data)
                    es.close()
                    # 清空报告内容
                    request.data["baogao"] = None
                    function_title += "返回时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                    # 记录正常结束日志信息
                    LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)
                    # 返回成功结果
                    return Result.sucess("{}成功".format(function_title))
            else:
                # 构建错误信息
                error_info = "新增失败，报告类型不能为空"
                function_title +="返回时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request,
                                                           function_title, None, error_info)
                # 返回失败结果
                return Result.fail(error_info, error_info)
        except Exception as exp:
            # 构建错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 清空报告内容
            request.data["baogao"] = None
            function_title += "返回时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request, function_title, None, exp)
            # 返回失败结果
            return Result.fail(error_info, json_data)

    @action(methods=['POST'], detail=False)
    def create_json_bg(self, request):
        file = request.FILES.get("file")
        json_data = None
        # 检查文件扩展名是否为 .json
        obj_data = {}
        if not file.name.endswith('.json'):
            return Result.fail("解析失败", "无效的文件类型。只允许上传 JSON 文件")
        try:
            # 读取文件内容
            file_content = file.read().decode('utf-8')
            # 解析 JSON 数据
            json_data = json.loads(file_content)
            obj_data["baogao"] = json_data["baogao"]
            obj_data["mc"] = json_data["mc"]
            obj_data["bg_type"] = json_data["bg_type"]

        except json.JSONDecodeError:
            Result.fail("解析失败","无效的 JSON 格式")
        except Exception as e:
            Result.fail("解析失败","{}失败".format(str(e)))

        request.FILES["file"] = None
        # 定义 API 路径，用于日志记录
        api_path = "/bg/create_bg/"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 定义功能标题，用于日志记录和返回信息
            function_title = "新增报告请，求时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

            # 将请求用户的 ID 赋值给请求数据中的 create_user_id 字段
            obj_data["create_user_id"] = request.auth.user_id
            # 判断请求用户对象的类型，如果不是字符串类型，则将用户名赋值给 create_user 字段
            if type(request.auth.user) != 'str':
                obj_data["create_user"] = request.auth.user.username
            else:
                obj_data["create_user"] = request.auth.user
            # 检查请求数据中是否包含报告类型字段
            if "bg_type" in obj_data:
                # 检查请求数据中是否包含报告内容字段
                if "baogao" in obj_data:
                    # 将报告内容转换为 JSON 字符串
                    obj_data["baogao"] = json.dumps(obj_data["baogao"])
                # 设置报告的创建时间为当前时间
                obj_data["create_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                # 获取报告名称，如果不存在则赋值为 None
                mc = obj_data["mc"] if "mc" in obj_data else None
                # 检查数据库中是否已存在相同名称的报告
                if len(WjyyGzBggl.objects.filter(mc=mc)) > 0:
                    # 构建错误信息
                    error_info = "名称:{}已存在,请换个名称".format(mc)
                    # 清空报告内容
                    obj_data["baogao"] = None
                    # 记录异常日志信息
                    LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                               request, function_title, error_info, None)
                    # 返回失败结果
                    return Result.fail(error_info, error_info)
                # 生成唯一的报告 ID
                obj_data['id'] = SnowflakeIDUtil.snowflakeId()
                with transaction.atomic():
                    # 在数据库中创建新的报告记录

                    WjyyGzBggl.objects.create(**obj_data)
                    # 同步到 Elasticsearch
                    es = Elasticsearch(hosts=self.hosts)
                    es.index(index=WjyyGzBggl.get_es_index_name(), id=request.data['id'], body=request.data)
                    es.close()
                    obj_data["baogao"] = None
                    function_title += "返回时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                    # 记录正常结束日志信息
                    LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                                  function_title)
                    # 返回成功结果
                    return Result.sucess("{}成功".format(function_title))
            else:
                # 构建错误信息
                error_info = "新增失败，报告类型不能为空"
                function_title += "返回时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request,
                                                           function_title, None, error_info)
                # 返回失败结果
                return Result.fail(error_info, error_info)
        except Exception as exp:
            # 构建错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 清空报告内容
            request.data["baogao"] = None
            function_title += "返回时间：{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request,
                                                       function_title, None, exp)
            # 返回失败结果
            return Result.fail(error_info, json_data)

    # 更新点位标记的接口，使用 POST 请求方法，不是针对单个对象的操作
    @action(methods=['POST'], detail=False)
    def update_bg(self, request):
        baogao = request.data['baogao']
        # 定义 API 路径，用于日志记录
        api_path = "/bg/update_bg/"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 定义功能标题，用于日志记录和返回信息
            function_title = "更新报告"
            # 将请求用户的 ID 赋值给请求数据中的 update_user_id 字段
            request.data["update_user_id"] = request.auth.user_id
            # 判断请求用户对象的类型，如果不是字符串类型，则将用户名赋值给 update_user 字段
            if type(request.auth.user) != 'str':
                request.data["update_user"] = request.auth.user.username
            else:
                request.data["update_user"] = request.auth.user
            # 设置报告的更新时间为当前时间
            request.data["update_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # 获取报告的 ID
            id = request.data["id"]
            # 根据 ID 查询数据库中的报告记录
            tis = WjyyGzBggl.objects.filter(id=id)
            # 检查是否查询到相关记录
            if tis.exists() > 0:
                # 检查请求数据中是否包含报告内容字段
                if "baogao" in request.data:
                    # 检查报告内容是否不为空
                    if (len(request.data["baogao"]) > 0):
                        # 将报告内容转换为 JSON 字符串
                        request.data["baogao"] = json.dumps(request.data["baogao"])
                with transaction.atomic():
                    # 更新数据库中的报告记录
                    tis.update(**request.data)
                    request.data['baogao'] = json.dumps(baogao, ensure_ascii=False)
                    # 同步到 Elasticsearch，此处代码被注释掉，可根据需要启用
                    es = Elasticsearch(hosts=self.hosts)
                    es.update(index=WjyyGzBggl.get_es_index_name(), id=id, body={"doc": request.data})
                    es.close()
                    # 返回成功结果
                    return Result.sucess("{}成功".format(function_title))
            else:
                # 返回失败结果，提示未找到相关数据
                return Result.sucess("{}失败，可能原因是{}".format(function_title, "没有相关数据"))
        except Exception as exp:
            # 构建错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 清空报告内容
            request.data["baogao"] = None
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request, function_title, None, exp)
            # 返回失败结果
            return Result.fail(error_info, json_data)

    # 删除点位标记的接口，使用 POST 请求方法，不是针对单个对象的操作
    @action(methods=['POST'], detail=False)
    def delete_bg(self, request):
        # 定义 API 路径，用于日志记录
        api_path = "/bg/delete_bg/"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 定义功能标题，用于日志记录和返回信息
            function_title = "删除报告"
            # 获取报告的 ID
            id = request.data["id"]
            # 根据 ID 查询数据库中的报告记录
            Tto = WjyyGzBggl.objects.filter(id=id)
            # 检查是否查询到相关记录
            if Tto.exists() > 0:
                with transaction.atomic():
                    # 删除数据库中的报告记录
                    Tto.delete()
                    # 更新功能标题，包含报告名称
                    function_title = "删除报告"
                    # 构建成功信息
                    msg = "{}成功".format(function_title)
                    # 从 Elasticsearch 删除记录，此处代码被注释掉，可根据需要启用
                    es = Elasticsearch(hosts=self.hosts)
                    es.delete(index=WjyyGzBggl.get_es_index_name(), id=id)
                    es.close()
                    # 返回成功结果
                    Result.sucess(msg)

                # 更新功能标题，包含报告名称
                # function_title = "删除报告"
                # [Tto][0].delete()
                # # 构建成功信息
                # msg = "{}成功".format(function_title)
                #
                # # 返回成功结果
                # Result.sucess(msg)
            else:
                # 构建失败信息
                msg = "删除{}失败，未找到数据".format("报告")
                # 清空报告内容
                request.data["baogao"] = None
                # 记录正常结束日志信息
                LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)
            # 返回结果
            return Result.sucess(msg)
        except Exception as exp:
            # 清空报告内容
            request.data["baogao"] = {}

            # 构建错误信息
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request,
                                                       function_title, error_info, exp)
            # 将异常信息转换为 JSON 字符串
            json_data = json.dumps(str(exp))
            # 返回失败结果
            return Result.fail(error_info, json_data)

    # 获取点位标记的接口，使用 POST 请求方法，不是针对单个对象的操作
    @action(methods=['POST'], detail=False)
    def get_bg(self, request):
        # 定义 API 路径，用于日志记录
        api_path = "/bg/get_bg/"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        # 定义功能标题，用于日志记录和返回信息
        function_title = "获取报告"
        try:
            # 初始化重要目标分析管理类，传入数据库连接对象
            # importantTargetAnalysisManage = ImportantTargetAnalysisManage(connection)
            ywManager = YwManager(connection)
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)
            # 调用重要目标分析管理类的方法，执行 SQL 查询
            # static_value = ywManager.get_zhfx(request,
            #                                                         'SELECT  * FROM	"WJYY_GZ_BGGL"    where 1=1 ')
            params =  request.data
            fei = bool(params.get("fei", False))
            huozhe = bool(params.get("huozhe", False))
            if not fei and not huozhe:
                del request.data["fei"]
                static_value = ywManager.get_bggl(request)
            elif fei and not huozhe:
                del request.data["fei"]
                static_value = ywManager.get_bggl_reverse(request)
            else:
                static_value = ywManager.get_bggl_or(request)
            # 清空报告内容
            request.data["baogao"] = None
            # 记录正常结束日志信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)
            # 构建成功信息
            msg = "{}成功".format(function_title)
            # 返回成功结果，包含查询结果
            return Result.sucess(msg, static_value)
        except Exception as exp:
            # 清空报告内容
            request.data["baogao"] = None
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request, function_title, None, exp)
            # 构建失败信息
            msg = "{}失败".format(function_title)
            # 返回失败结果，包含异常信息
            return Result.fail(msg, str(exp))

    # 搜索全部点位标记的接口，使用 POST 请求方法，不是针对单个对象的操作
    @action(methods=['POST'], detail=False)
    def search_all_bg(self, request):
        # 定义 API 路径，用于日志记录
        api_path = "/bg/search_all_bg/"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        # 定义功能标题，用于日志记录和返回信息
        function_title = "搜索全部报告"
        try:
            # 使用 elasticsearch_dsl 查询 Elasticsearch 中指定索引的所有文档
            es = Elasticsearch(hosts=self.hosts)
            s = Search(using=es, index=WjyyGzBggl.get_es_index_name()).query("match_all")
            # 执行查询
            response = s.execute()
            es.close()
            # 将查询结果转换为字典列表
            results = [hit.to_dict() for hit in response]
            # 清空报告内容
            request.data["baogao"] = None
            # 记录正常结束日志信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)
            # 构建成功信息
            msg = "{}成功".format(function_title)
            # 返回成功结果，包含查询结果
            return Result.sucess(msg, results)
        except Exception as exp:
            # 清空报告内容
            request.data["baogao"] = None
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request,
                                                       function_title, None, exp)
            # 构建失败信息
            msg = "{}失败".format(function_title)
            # 返回失败结果，包含异常信息
            return Result.fail(msg, str(exp))

    # 分页搜索点位标记的接口，使用 POST 请求方法，不是针对单个对象的操作
    @action(methods=['POST'], detail=False)
    def search_bg_page(self, request):
        # 定义 API 路径，用于日志记录
        api_path = "/bg/search_bg_page/"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        # 定义功能标题，用于日志记录和返回信息
        function_title = "分页搜索报告"
        try:
            # 获取请求数据中的页码，默认为 1
            page = int(request.data.get('page', 1))
            # 获取请求数据中的每页数量，默认为 10
            page_size = int(request.data.get('page_size', 10))
            # 获取请求数据中的搜索字符串，默认为空字符串
            search_string = request.data.get('search_string', '')

            # 如果搜索字符串为空，直接返回空结果
            if not search_string:
                data = {
                    'results': [],
                    'page': page,
                    'page_size': page_size,
                    'total_pages': 0,
                    'total_count': 0
                }
                return Result.sucess("搜索字符串为空，返回空结果", data)

            # 使用 elasticsearch_dsl 初始化搜索对象，指定 Elasticsearch 客户端和索引
            es = Elasticsearch(hosts=self.hosts)
            s = Search(using=es, index=WjyyGzBggl.get_es_index_name())
            # 如果搜索字符串不为空，则添加多字段匹配查询条件 # 这行已经不需要了，因为上面已经处理了空字符串的情况
            # if search_string: # 这行已经不需要了，因为上面已经处理了空字符串的情况
            s = s.query("multi_match", query=search_string, fields=['mc', 'baogao'])
            # 设置分页范围
            s = s[(page - 1) * page_size:page * page_size]
            # 执行查询
            response = s.execute()
            es.close()
            # 获取总记录数
            total_count = response.hits.total.value
            # 计算总页数
            total_pages = (total_count + page_size - 1) // page_size
            # 将查询结果转换为字典列表
            results = [hit.to_dict() for hit in response]
            for result in results:
                result['baogao'] = json.loads(result['baogao'])
            # 清空报告内容
            request.data["baogao"] = None
            # 记录正常结束日志信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request, function_title)
            # 构建成功信息
            msg = "{}成功".format(function_title)
            # 返回成功结果，包含查询结果和分页信息
            data = {
                'results': results,
                'page': page,
                'page_size': page_size,
                'total_pages': total_pages,
                'total_count': total_count
            }
            return Result.sucess(msg, data)
        except Exception as exp:
            # 清空报告内容
            request.data["baogao"] = None
            # 记录异常日志信息
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user, request,
                                                       function_title, None, exp)
            # 构建失败信息
            msg = "{}失败".format(function_title)
            # 返回失败结果，包含异常信息
            return Result.fail(msg, str(exp))