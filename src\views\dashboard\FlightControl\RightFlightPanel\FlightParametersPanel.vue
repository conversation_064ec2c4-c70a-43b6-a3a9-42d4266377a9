<script setup lang="ts">
import { computed } from 'vue'
import type { FlightRealTimeData } from '@/types/ui'

// 组件属性 - 从父组件接收数据
interface Props {
  data: FlightRealTimeData['parameters']
}

const props = defineProps<Props>()

// 飞行参数数据 - 直接使用props传入的数据
const parametersData = computed(() => {
  return props.data
})

// 计算信号强度颜色
const getSignalColor = (signal: string) => {
  if (signal.includes('-')) {
    const value = parseInt(signal.replace(/[^\d-]/g, ''))
    if (value > -50) return '#4CAF50' // 绿色 - 强信号
    if (value > -70) return '#FF9800' // 橙色 - 中等信号
    return '#F44336' // 红色 - 弱信号
  }
  return '#00FFFE' // 默认颜色
}

// 计算状态颜色
const getStatusColor = (status: string) => {
  if (status === '正常' || status === '良好') return '#4CAF50'
  if (status === '一般' || status === '轻微') return '#FF9800'
  return '#F44336'
}

// 计算GPS数量颜色
const getGpsColor = (count: number) => {
  if (count >= 10) return '#4CAF50' // 绿色 - 信号强
  if (count >= 6) return '#FF9800' // 橙色 - 信号中等
  return '#F44336' // 红色 - 信号弱
}
</script>

<template>
  <ScreenCard title="飞行参数" icon="mdi:airplane-settings">
    <div class="flight-parameters-content">
      <!-- 参数列表 -->
      <div class="parameters-list">
        <!-- 第一行：离地高度、离机高度 -->
        <div class="parameters-row">
          <div class="parameter-item">
            <UIcon name="mdi:altimeter" size="0.8rem" color="#00FFFE" />
            <div class="param-info">
              <span class="param-label">离地高度</span>
              <span class="param-value">{{ parametersData.groundAltitude }}m</span>
            </div>
          </div>
          <div class="parameter-item">
            <UIcon name="mdi:map-marker-distance" size="0.8rem" color="#00FFFE" />
            <div class="param-info">
              <span class="param-label">离机高度</span>
              <span class="param-value">{{ parametersData.droneAltitude }}m</span>
            </div>
          </div>
        </div>

        <!-- 第二行：离机距离、上传信号 -->
        <div class="parameters-row">
          <div class="parameter-item">
            <UIcon name="mdi:map-marker-radius" size="0.8rem" color="#00FFFE" />
            <div class="param-info">
              <span class="param-label">离机距离</span>
              <span class="param-value">{{ parametersData.droneDistance }}km</span>
            </div>
          </div>
          <div class="parameter-item">
            <UIcon
              name="mdi:upload"
              size="0.8rem"
              :color="getSignalColor(parametersData.uploadSignal)"
            />
            <div class="param-info">
              <span class="param-label">上传信号</span>
              <span
                class="param-value"
                :style="{ color: getSignalColor(parametersData.uploadSignal) }"
              >
                {{ parametersData.uploadSignal }}
              </span>
            </div>
          </div>
        </div>

        <!-- 第三行：下载信号、GPS信号 -->
        <div class="parameters-row">
          <div class="parameter-item">
            <UIcon
              name="mdi:download"
              size="0.8rem"
              :color="getSignalColor(parametersData.downloadSignal)"
            />
            <div class="param-info">
              <span class="param-label">下载信号</span>
              <span
                class="param-value"
                :style="{ color: getSignalColor(parametersData.downloadSignal) }"
              >
                {{ parametersData.downloadSignal }}
              </span>
            </div>
          </div>
          <div class="parameter-item">
            <UIcon
              name="mdi:satellite-variant"
              size="0.8rem"
              :color="getGpsColor(parametersData.gpsCount)"
            />
            <div class="param-info">
              <span class="param-label">GPS数量</span>
              <span class="param-value" :style="{ color: getGpsColor(parametersData.gpsCount) }">
                {{ parametersData.gpsCount }}颗
              </span>
            </div>
          </div>
        </div>

        <!-- 第四行：飞行模式、罗盘状态 -->
        <div class="parameters-row">
          <div class="parameter-item">
            <UIcon name="mdi:airplane" size="0.8rem" color="#00FFFE" />
            <div class="param-info">
              <span class="param-label">飞行模式</span>
              <span class="param-value">{{ parametersData.flightMode }}</span>
            </div>
          </div>
          <div class="parameter-item">
            <UIcon
              name="mdi:compass"
              size="0.8rem"
              :color="getStatusColor(parametersData.compassStatus)"
            />
            <div class="param-info">
              <span class="param-label">罗盘状态</span>
              <span
                class="param-value"
                :style="{ color: getStatusColor(parametersData.compassStatus) }"
              >
                {{ parametersData.compassStatus }}
              </span>
            </div>
          </div>
        </div>

        <!-- 第五行：信道干扰、图传信号 -->
        <div class="parameters-row">
          <div class="parameter-item">
            <UIcon
              name="mdi:wifi-strength-alert-outline"
              size="0.8rem"
              :color="getStatusColor(parametersData.channelInterference)"
            />
            <div class="param-info">
              <span class="param-label">信道干扰</span>
              <span
                class="param-value"
                :style="{ color: getStatusColor(parametersData.channelInterference) }"
              >
                {{ parametersData.channelInterference }}
              </span>
            </div>
          </div>
          <div class="parameter-item">
            <UIcon
              name="mdi:video-wireless"
              size="0.8rem"
              :color="getStatusColor(parametersData.videoSignal)"
            />
            <div class="param-info">
              <span class="param-label">图传信号</span>
              <span
                class="param-value"
                :style="{ color: getStatusColor(parametersData.videoSignal) }"
              >
                {{ parametersData.videoSignal }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ScreenCard>
</template>

<style scoped lang="scss">
.flight-parameters-content {
  height: 100%;
  padding: 0.5rem 0;

  .parameters-list {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .parameters-row {
      display: flex;
      gap: 0.5rem;

      .parameter-item {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 0.3rem;
        padding: 0.4rem;
        background: rgba($bg-card, 0.3);
        border-radius: $border-radius-small;
        border: 1px solid rgba($border-color, 0.3);
        transition: all 0.2s ease;

        &:hover {
          background: rgba($bg-card, 0.5);
          border-color: rgba($primary-color, 0.2);
        }

        .param-info {
          display: flex;
          flex-direction: column;
          gap: 0.1rem;

          .param-label {
            font-size: $font-size-panel-micro;
            color: $text-secondary;
            line-height: 1;
          }

          .param-value {
            font-size: $font-size-panel-caption;
            color: $text-default;
            font-weight: bold;
            line-height: 1;
          }
        }
      }
    }
  }
}
</style>
