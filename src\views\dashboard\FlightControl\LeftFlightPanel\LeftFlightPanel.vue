<script setup lang="ts">
import { computed } from 'vue'
import { useDroneStore } from '@/stores/droneStore'
import DroneNestPanel from './DroneNestPanel.vue'
import TemperaturePanel from './TemperaturePanel.vue'
import BatteryPanel from './BatteryPanel.vue'
import FlightMissionPanel from './FlightMissionPanel.vue'

// 使用Store
const droneStore = useDroneStore()

// 为BasePanel创建一个单一的tab
const tabs = [{ id: 'flight-control', name: '塔石测试机' }]

const activeTab = ref('flight-control')

const handleTabChange = (tabId: string) => {
  activeTab.value = tabId
}

// 从Store获取飞控实时数据
const flightData = computed(() => {
  return droneStore.flightRealTimeData
})
</script>

<template>
  <BasePannel :tabs="tabs" :defaultActiveTab="activeTab" @activeTabChange="handleTabChange">
    <template #content>
      <div class="left-flight-panel-content">
        <!-- 使用CSS Grid布局，5个组件垂直排列 -->
        <div v-if="flightData" class="flight-components-grid">
          <!-- 机巢状态面板 -->
          <DroneNestPanel :data="flightData.nestStatus" />

          <!-- 温度参数面板 -->
          <TemperaturePanel :data="flightData.temperature" />

          <!-- 电池参数面板 -->
          <BatteryPanel :data="flightData.battery" />

          <!-- 飞行任务面板 -->
          <FlightMissionPanel :data="flightData.mission" />
        </div>
        <div v-else class="loading-state">
          <span>正在加载飞控数据...</span>
        </div>
      </div>
    </template>
  </BasePannel>
</template>

<style scoped lang="scss">
.left-flight-panel-content {
  height: 100%;
  padding: 0.5rem;

  .flight-components-grid {
    height: 100%;
    display: grid;
    grid-template-rows: repeat(4, auto);
    gap: 0.5rem;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
</style>
