<template>
  <div class="video-card">
    <div class="video-container">
      <!-- 视频内容区域 -->
      <slot name="video">
        <div class="video-placeholder">
          <img v-if="videoData.imageUrl" :src="videoData.imageUrl" alt="视频画面" />
          <div v-else class="no-video">暂无视频信号</div>
        </div>
      </slot>

      <!-- Bottom Overlay for Info and Position Icon -->
      <div class="bottom-overlay">
        <!-- 信息卡片 -->
        <div class="info-card">
          <div class="info-col">
            <div v-for="item in infoCardItems" :key="item.id" class="info-item">
              <UIcon :name="item.icon" color="#00fffe" size="0.8rem" />
              <span>{{ item.text }}</span>
            </div>
          </div>
        </div>

        <!-- 定位图标 -->
        <div class="position-icon" @click.stop="handlePositionClick">
          <UIcon name="mdi:crosshairs-gps" color="#00fffe" size="1rem" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import UIcon from '@/components/common/UIcon/UIcon.vue'

// 定义单个信息项的显示结构
interface InfoItemDisplay {
  id: string
  icon: string
  text: string
}

// 定义视频数据类型
interface VideoData {
  // 视频ID
  id: string
  // 视频图片URL（如果不是实时视频）
  imageUrl?: string
  // 风速
  windSpeed?: string | number
  // 天气状况
  weather?: string
  // 湿度
  humidity?: string | number
  // 位置信息
  location?: string
  // 设备名称
  deviceName?: string
}

// 定义组件属性
interface Props {
  // 传入所有视频数据
  data: VideoData
}

// 定义组件事件
const emit = defineEmits<{
  // 点击定位图标事件
  (e: 'position-click', id: string): void
}>()

// 默认属性值
const defaultData: VideoData = {
  id: '',
  imageUrl: '',
  windSpeed: '2',
  weather: '多云',
  humidity: '46',
  location: '筑山02-财政局',
  deviceName: '筑山02-财政局',
}

// 组件属性
const props = defineProps<Props>()

// 合并默认数据和传入数据
const videoData = computed<VideoData>(() => {
  return { ...defaultData, ...props.data }
})

// 计算属性，用于生成信息卡片项的数组
const infoCardItems = computed<InfoItemDisplay[]>(() => {
  const vd = videoData.value // 使用已经合并了默认值的 videoData
  return [
    { id: 'windSpeed', icon: 'mdi:weather-windy', text: `${vd.windSpeed ?? 'N/A'} m/s` },
    { id: 'weather', icon: 'mdi:weather-sunny', text: `${vd.weather ?? 'N/A'}` },
    { id: 'humidity', icon: 'mdi:water-percent', text: `${vd.humidity ?? 'N/A'}%RH` },
    { id: 'deviceName', icon: 'mdi:map-marker-outline', text: `${vd.deviceName ?? 'N/A'}` },
  ].filter((item) => item.text !== 'N/A') // 可选择过滤掉没有有效数据的项
})

// 处理定位图标点击事件
const handlePositionClick = () => {
  emit('position-click', props.data.id)
}
</script>

<style scoped lang="scss">
.video-card {
  width: 100%;
  height: 100%;
  min-height: 10rem;
  max-height: 12rem;
  border-radius: 4px;
  overflow: hidden;
  // background-color: #1a1a1a;
  box-shadow: inset 0 0 1rem rgba(0, 255, 254, 0.7);
  border: 1px solid transparent; // Initial transparent border for smooth transition
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease,
    border-color 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: inset 0 0 1rem rgba(0, 255, 254, 0.9);
    border: 1px solid rgba(0, 255, 254, 0.5);
  }

  .video-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .bottom-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem; // Adjust padding as needed
      box-sizing: border-box;
      pointer-events: none; // Allow clicks to pass through to video unless on specific items

      & > * {
        // Direct children (info-card, position-icon) should allow pointer events
        pointer-events: auto;
      }
    }

    .video-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      // background-color: #000;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .no-video {
        color: #999;
        font-size: 0.8rem;
      }
    }

    .info-card {
      // position: absolute; // Removed
      // bottom: 1rem; // Removed
      // left: 1rem; // Removed
      background-color: rgba(0, 255, 254, 0.1);
      border-radius: 4px;
      padding: 0.3rem 0.5rem;
      color: $text-default;
      font-size: 0.6rem;
      z-index: 10;

      .info-col {
        display: flex;
        flex-direction: row;
        gap: 0.5rem;
        margin-bottom: 0.2rem;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          display: flex;
          flex-direction: column;
          // align-items: center;
          gap: 0.1rem;

          span {
            white-space: nowrap;
            font-size: 0.5rem;
          }
        }
      }
    }

    .position-icon {
      // position: absolute; // Removed
      // bottom: 0.5rem; // Removed
      // right: 0.5rem; // Removed
      width: 1.8rem;
      height: 1.8rem;
      // background-color: rgba(0, 0, 0, 0.7);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 20;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(0, 255, 254, 0.2);
        transform: scale(1.1);
      }
    }
  }
}
</style>
