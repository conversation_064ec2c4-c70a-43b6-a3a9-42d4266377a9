<template>
  <!-- 数据成果 -->
  <ScreenCard title="数据成果" :icon="'mdi:chart-line'">
    <div class="data-achievement-content">
      <!-- 第一行：两个数据展示 -->
      <div class="data-row-top">
        <DataDisplay
          v-for="(item, index) in data.topRowData"
          :key="index"
          :title="item.title"
          :value="item.value"
          :unit="item.unit"
          :icon="item.icon"
          :iconColor="item.iconColor"
        />
      </div>

      <!-- 第二行：四个数据展示 -->
      <div class="data-row-bottom">
        <DataDisplay
          v-for="(item, index) in data.bottomRowData"
          :key="index"
          :title="item.title"
          :value="item.value"
          :unit="item.unit"
          :icon="item.icon"
          :iconColor="item.iconColor"
        />
      </div>
    </div>
  </ScreenCard>
</template>

<script setup lang="ts">
import ScreenCard from '@/components/common/ScreenCard/ScreenCard.vue'
import DataDisplay from '@/components/common/DataDisplay/DataDisplay.vue'
import type { DataAchievementUIData } from '@/types/ui/stats'

// ===== Props =====
interface Props {
  data: DataAchievementUIData
}

defineProps<Props>()
</script>

<style scoped lang="scss">
.data-achievement-content {
  padding: 0.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .data-row-top {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 0.3rem;

    :deep(.data-display) {
      flex: 1;
      max-width: 48%;

      .data-icon {
        min-width: 1.4rem;
        min-height: 1.4rem;
        padding: 0.1rem;
      }

      .data-content {
        .data-value {
          font-size: 0.85rem;
        }

        .data-title {
          font-size: 0.45rem;
        }
      }
    }
  }

  .data-row-bottom {
    display: flex;
    justify-content: space-between;
    width: 100%;

    :deep(.data-display) {
      flex: 1;
      max-width: 24%;

      .data-icon {
        min-width: 0.7rem;
        min-height: 0.7rem;
        padding: 0.05rem;
      }

      .data-content {
        min-height: auto;

        .data-value {
          font-size: 0.5rem;
          letter-spacing: 0;
        }

        .data-title {
          font-size: 0.35rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
        }
      }
    }
  }
}
</style>
