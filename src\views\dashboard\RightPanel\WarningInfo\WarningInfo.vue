<template>
  <div class="warning-info">
    <!-- 列表视图 -->
    <div v-if="isListView" class="list-view">
      <div class="filters-section">
        <DateRangePicker @date-changed="handleDateChange" />
        <WarningTypeSelector
          :available-types="availableWarningTypes"
          :selected-type="selectedWarningType"
          @type-selected="handleTypeSelect"
        />
      </div>
      <!-- 预警列表区域 -->
      <div class="warning-list-wrapper">
        <WarningList
          :warnings="warningListItems"
          :loading="loading"
          :error="error"
          @warning-selected="handleWarningSelect"
          @refresh="handleRefresh"
        />
      </div>
    </div>

    <!-- 详情视图 -->
    <div v-else-if="isDetailView" class="detail-view">
      <WarningDetail
        :warning-detail="selectedWarning"
        :loading="loading"
        @back="handleWarningDetailBack"
        @set-typical="handleSetTypical"
        @complete="handleCompleteWarning"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted } from 'vue'
import DateRangePicker from '@/components/common/DateRangePicker/DateRangePicker.vue'
import WarningTypeSelector from './WarningTypeSelector.vue'
import WarningList from './WarningList.vue'
import WarningDetail from './components/WarningDetail.vue'
import { useWarningStore } from '@/stores/warningStore'

// 使用预警Store
const warningStore = useWarningStore()

// 计算属性：从Store中提取数据，向下传递给子组件
const isListView = computed(() => warningStore.isListView)
const isDetailView = computed(() => warningStore.isDetailView)
const availableWarningTypes = computed(() => warningStore.availableWarningTypes)
const selectedWarningType = computed(() => warningStore.filters.warningType)
const warningListItems = computed(() => warningStore.warningListItems)
const selectedWarning = computed(() => warningStore.selectedWarning)
const loading = computed(() => warningStore.loading)
const error = computed(() => warningStore.error)

// 组件挂载时初始化数据
onMounted(async () => {
  await warningStore.initializeData()
})

// 处理日期范围变化 - 重新获取数据
const handleDateChange = async (dates: { startDate: string; endDate: string } | null) => {
  if (dates) {
    // 设置日期筛选条件
    warningStore.filters.dateRange = {
      startDate: new Date(dates.startDate),
      endDate: new Date(dates.endDate),
    }
  } else {
    // 清除日期筛选条件
    warningStore.filters.dateRange = null
  }

  // 重置到第一页并重新获取数据
  warningStore.pagination.currentPage = 1
  await warningStore.fetchWarningDetails()
}

// 处理预警类型选择 - 前端过滤
const handleTypeSelect = (typeId: string) => {
  // 设置类型筛选条件（前端过滤，不重新获取数据）
  warningStore.filters.warningType = typeId
}

// 处理预警选择（从列表跳转到详情）
const handleWarningSelect = (warningId: string) => {
  warningStore.switchToDetail(warningId)
}

// 处理刷新
const handleRefresh = async () => {
  await warningStore.fetchWarningDetails()
}

// 处理预警详情事件
const handleWarningDetailBack = () => {
  warningStore.switchToList()
}

const handleSetTypical = async (id: string) => {
  await warningStore.setAsTypical(id)
}

const handleCompleteWarning = async (id: string) => {
  await warningStore.completeWarning(id)
}
</script>

<style scoped lang="scss">
.warning-info {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;

  // 视图容器
  .list-view,
  .detail-view {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .list-view {
    padding: 0.5rem;
    row-gap: 0.5rem;
  }
}

.filters-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 0.5rem;
}

.warning-list-wrapper {
  flex: 1;
  width: 100%; // Fill the grid column
  height: 100%; // Fill the '1fr' grid row height
  overflow-y: auto;
}
</style>
