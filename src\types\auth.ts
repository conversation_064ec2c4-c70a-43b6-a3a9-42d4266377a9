/**
 * 认证相关类型定义
 */

// 登录请求参数
export interface LoginRequest {
  username: string
  password: string
  verifcation: string
  client_time: number
}

// 登录响应数据 - 匹配后端返回结构
export interface LoginResponse {
  success: boolean
  code: number
  info: string
  userid: number
  username: string
  token: string
}

// 用户信息接口 - 简化版本
export interface UserInfo {
  userid: number
  username: string
}

// 认证状态
export interface AuthState {
  isAuthenticated: boolean
  token: string | null
  refreshToken: string | null
  userInfo: UserInfo | null
  loginTime: number | null
  expiresIn: number | null
}

// 登录表单验证规则类型
export interface LoginFormRules {
  username: any[]
  password: any[]
  verification: any[]
  [key: string]: any[] // 添加索引签名
}

// 登录表单数据类型
export interface LoginFormData {
  username: string
  password: string
  verification: string // 验证码
  remember?: boolean // 记住密码选项
}
