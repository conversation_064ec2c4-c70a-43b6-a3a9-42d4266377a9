# -*- coding: utf-8 -*-
"""
字典模型 - 系统字典相关数据模型
从 my_models.dict_model 迁移到 my_app.models.dict_model
"""
from django.db import models


class TtSelectDict(models.Model):
    """标注符号数据表"""
    id = models.BigAutoField(primary_key=True, db_comment="id")
    cname = models.CharField(max_length=100, db_comment="中文名称")
    ename = models.CharField(max_length=100, db_comment="英文名称")
    type = models.BigIntegerField(blank=True, null=True, db_comment='类型')
    remark = models.Char<PERSON>ield(max_length=2000, db_comment="描述")
    sysmbol_path = models.CharField(max_length=255, db_comment="标注符号相对路径")
    td_sysmbol = models.CharField(max_length=255, db_comment="3D符号相对路径")
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建人')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新人id')

    class Meta:
        managed = False
        db_table = 'tt_select_dict'
        db_table_comment = '标注符号数据表'
