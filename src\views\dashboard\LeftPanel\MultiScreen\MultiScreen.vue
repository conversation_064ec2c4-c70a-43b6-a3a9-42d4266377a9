<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUIStore } from '@/stores/uiStore'
import { useDroneStore } from '@/stores/droneStore'
import DroneVideoCard from './components/DroneVideoCard.vue'
import type { VideoStreamType } from '@/types/ui'
import { VIDEO_STREAM_TYPES } from '@/types/ui'

// 使用Store
const uiStore = useUIStore()
const droneStore = useDroneStore()

// 当前选中的标签页 - UI内部状态
const activeTab = ref<VideoStreamType>('nest_inside')

// 计算属性，获取当前激活标签页的全部视频数据
const currentVideoData = computed(() => {
  if (!droneStore.multiScreenData) return []
  return droneStore.multiScreenData.videoData[activeTab.value] || []
})

// 切换标签页
const switchTab = (tab: VideoStreamType) => {
  activeTab.value = tab
}

// 处理监控卡片控制按钮点击事件
const handleMonitorControlClick = (droneId: string) => {
  console.log('点击了监控卡片控制按钮, Drone ID:', droneId)

  // 直接进入飞控模式（会自动显示MonitorCards）
  uiStore.enterFlightMode()
}

// 组件挂载时获取数据
onMounted(() => {
  droneStore.fetchMultiScreenData()
})
</script>

<template>
  <div class="multi-screen">
    <!-- 顶部标签页 -->
    <div class="tab-header">
      <div
        v-for="(config, key) in VIDEO_STREAM_TYPES"
        :key="key"
        :class="['tab-item', { active: activeTab === key }]"
        @click="switchTab(key as VideoStreamType)"
      >
        {{ config.label }}
      </div>
    </div>

    <!-- 视频卡片列表 -->
    <div class="video-list">
      <DroneVideoCard
        v-for="video in currentVideoData"
        :key="video.id"
        :data="video"
        @monitor-control-click="handleMonitorControlClick"
      />
    </div>

    <!-- 视频总数信息 -->
    <div class="video-count-info">总数: {{ currentVideoData.length }}</div>
  </div>
</template>

<style scoped lang="scss">
.multi-screen {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0.5rem;
  gap: 0.5rem;
  position: relative;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid rgba(0, 255, 254, 0.3);
  margin-bottom: 0.5rem;
  height: 2rem;
  align-items: center;

  .tab-item {
    padding: 0 1rem;
    cursor: pointer;
    color: #e1e1e1;
    font-size: 0.7rem;
    position: relative;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
    line-height: 1;

    &.active {
      color: #00fffe;
      font-weight: bold;

      &:after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #00fffe;
        box-shadow: 0 0 5px #00fffe;
      }
    }

    &:hover {
      color: #00fffe;
    }
  }
}

.video-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.video-count-info {
  position: absolute;
  bottom: 0.5rem;
  left: 0.5rem; // Adjusted for better padding from scrollbar if present
  padding: 0.3rem 0.6rem;
  background-color: rgba(10, 30, 40, 0.85); // Darker, more opaque background
  color: #00fffe;
  font-size: 0.7rem;
  border-radius: 3px;
  z-index: 10;
  border: 1px solid rgba(0, 255, 254, 0.2);
}
</style>
