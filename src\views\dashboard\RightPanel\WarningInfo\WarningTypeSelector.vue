<template>
  <div class="warning-type-selector">
    <el-radio-group
      :model-value="props.selectedType"
      @change="onTypeChange"
      class="full-width-radio-group"
    >
      <!-- “全部类型”选项 -->
      <el-radio :value="allTypeOption.id" class="type-item type-item-all">
        <span class="type-name">{{ allTypeOption.name }}</span>
        <span v-if="allTypeOption.count !== undefined" class="type-count"
          >({{ allTypeOption.count }})</span
        >
      </el-radio>

      <!-- 其他类型选项 -->
      <div class="other-types-grid">
        <el-tooltip
          v-for="item in otherWarningTypes"
          :key="item.id + '-tooltip'"
          :content="item.name + (item.count !== undefined ? ' (' + item.count + ')' : '')"
          placement="top"
          effect="dark"
          :hide-after="0"
          :show-arrow="false"
          popper-class="custom-tooltip"
        >
          <el-radio :value="item.id" class="type-item">
            <span class="type-name">{{ item.name }}</span>
            <span v-if="item.count !== undefined" class="type-count">({{ item.count }})</span>
          </el-radio>
        </el-tooltip>
      </div>
    </el-radio-group>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// 定义props
interface Props {
  availableTypes: Array<{
    id: string
    name: string
    count?: number
  }>
  selectedType: string
}

const props = withDefaults(defineProps<Props>(), {
  selectedType: 'all',
})

// 计算属性：获取"全部类型"选项
const allTypeOption = computed(() => {
  const allOption = props.availableTypes.find((type) => type.id === 'all')
  return allOption || { id: 'all', name: '全部类型' }
})

// 计算属性：获取其他类型选项
const otherWarningTypes = computed(() => props.availableTypes.filter((type) => type.id !== 'all'))

const selectedType = ref<string>('all') // 默认选中“全部类型”

const emit = defineEmits<{
  'type-selected': [typeId: string]
}>()

const onTypeChange = (value: string | number | boolean | undefined) => {
  if (typeof value === 'string') {
    emit('type-selected', value)
  }
}
</script>
<style scoped lang="scss">
.warning-type-selector {
  padding: 0.2rem 0; // 再缩小一点上下padding
}

.full-width-radio-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem; // 再缩小一点间距
}

.type-item {
  display: flex;
  align-items: center;
  padding: 0.2rem 0.3rem; // 再缩小内边距
  border-radius: 3px;
  background-color: rgba(255, 255, 255, 0.03);
  color: $text-secondary;
  cursor: pointer;
  transition: all 0.2s ease;
  height: auto;
  margin-right: 0 !important;
  overflow: hidden; // 防止内部内容溢出导致布局问题

  &:hover {
    background-color: rgba(255, 255, 255, 0.08);
    color: $text-hover;
  }

  &.is-checked {
    background-color: rgba($primary-color, 0.25);
    color: $text-active;
    border: 1px solid rgba($primary-color, 0.5);

    .type-name,
    .type-count {
      color: $text-active;
    }
  }

  :deep(.el-radio__input) {
    // .el-radio__inner 样式保持之前的缩小版
    .el-radio__inner {
      width: 11px; // 可以再小一点
      height: 11px;
      border-color: $border-color;
      background-color: transparent;

      &::after {
        width: 5px; // 对应缩小
        height: 5px;
        background-color: $primary-color;
      }
    }
    &.is-checked .el-radio__inner {
      border-color: $primary-color;
    }
  }

  :deep(.el-radio__label) {
    display: flex;
    align-items: center;
    padding-left: 0.2rem; // 再缩小
    font-size: 0.6rem; // 显著缩小字体 (原0.7rem)
    white-space: nowrap; // 强制不换行
    overflow: hidden; // 隐藏溢出部分
    text-overflow: ellipsis; // 用省略号显示溢出
    line-height: 1; // 调整行高以适应更小的字体
    color: inherit;
    // flex-grow: 1; // 让标签占据剩余空间，配合overflow
    min-width: 0; // 配合flex-grow和overflow
  }
}

.type-item-all {
  width: 100%;
  :deep(.el-radio__label) {
    font-size: 0.65rem; // “全部类型”字体 (原0.75rem)
  }
}

.other-types-grid {
  display: grid;
  grid-template-columns: repeat(3, minmax(80px, 1fr)); // 减小min值 (原100px/120px)
  gap: 0.3rem; // 再缩小间距
  width: 100%;
}

.type-name {
  margin-right: 0.15rem; // 再缩小
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 1; // 允许名称收缩
  min-width: 20px; // 给一个最小宽度，防止完全消失
}

.type-count {
  font-size: 0.55rem; // 显著缩小数量字体 (原0.65rem)
  white-space: nowrap; // 防止括号换行
  flex-shrink: 0; // 不允许数量收缩
}

// 自定义 tooltip 样式
:global(.custom-tooltip) {
  background-color: $tooltip-bg-color !important;
  color: $tooltip-text-color !important;
  border: 1px solid $tooltip-border-color !important;
  padding: 4px 8px !important;
  font-size: 0.7rem !important;
  border-radius: 3px !important;
}
</style>
