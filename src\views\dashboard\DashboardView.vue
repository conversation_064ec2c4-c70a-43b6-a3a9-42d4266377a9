<script setup lang="ts">
import { computed, onMounted, watch, ref, nextTick } from 'vue'
import { useUIStore } from '@/stores/uiStore'
import { useDroneStore } from '@/stores/droneStore'
import DashHeader from './DashHeader.vue'
import LeftPanel from './LeftPanel/LeftPanel.vue'
import RightPanel from './RightPanel/RightPanel.vue'
import BottomPanel from './BottomPanel/BottomPanel.vue'
import DashMap from './DashMap/DashMap.vue'
// 飞控相关组件
import MonitorCards from './FlightControl/components/MonitorCards.vue'
import TopFlightStatusBar from './FlightControl/components/TopFlightStatusBar.vue'
import LeftFlightPanel from './FlightControl/LeftFlightPanel/LeftFlightPanel.vue'
import RightFlightPanel from './FlightControl/RightFlightPanel/RightFlightPanel.vue'
import VideoStreamBackground from './FlightControl/components/VideoStreamBackground.vue'

// 使用Store
const uiStore = useUIStore()
const droneStore = useDroneStore()

// 模板引用
const backgroundLayer = ref<HTMLElement>()
const mapCardContainer = ref<HTMLElement>()
const mapInstance = ref<InstanceType<typeof DashMap>>()
const videoCardContainer = ref<HTMLElement>()
const monitorVideoInstance = ref<InstanceType<typeof VideoStreamBackground>>()
const droneVideoInstance = ref<InstanceType<typeof VideoStreamBackground>>()

// 当前视图模式
const viewMode = computed(() => uiStore.viewMode)
// 飞控模式下的背景显示类型
const flightBackgroundType = computed(() => uiStore.flightBackgroundType)
// 地图显示位置
const mapDisplayPosition = computed(() => uiStore.mapDisplayPosition)
// 监控卡片显示状态
const showMonitorCards = computed(() => uiStore.showMonitorCards || viewMode.value === 'flight')

// 视频组件位置状态 - 初始状态应该在卡片中（因为默认地图在背景）
const monitorVideoPosition = ref<'background' | 'card-1' | 'card-2'>('card-1')
const droneVideoPosition = ref<'background' | 'card-1' | 'card-2'>('card-2')

// ===== 地图动态移动逻辑 =====

/**
 * 动态移动地图到指定容器
 */
const moveMapToContainer = async (targetContainer: HTMLElement) => {
  await nextTick() // 确保DOM更新完成

  if (mapInstance.value?.$el && targetContainer) {
    targetContainer.appendChild(mapInstance.value.$el)
    console.log('地图已移动到新容器:', targetContainer.className)
  }
}

/**
 * 将地图移动到背景层
 */
const moveMapToBackground = async () => {
  if (backgroundLayer.value) {
    await moveMapToContainer(backgroundLayer.value)
  }
}

/**
 * 将地图移动到卡片容器
 */
const moveMapToCard = async () => {
  if (mapCardContainer.value) {
    await moveMapToContainer(mapCardContainer.value)
  }
}

// ===== 视频组件动态移动逻辑 =====

/**
 * 动态移动视频组件到指定容器
 */
const moveVideoToContainer = async (videoInstance: any, targetContainer: HTMLElement) => {
  await nextTick() // 确保DOM更新完成

  if (videoInstance?.value?.$el && targetContainer) {
    console.log('移动视频DOM节点:', {
      from: videoInstance.value.$el.parentElement?.className || 'none',
      to: targetContainer.className,
      videoElement: videoInstance.value.$el.tagName,
    })
    targetContainer.appendChild(videoInstance.value.$el)
    console.log('视频已移动到新容器:', targetContainer.className)
  } else {
    console.warn('视频移动失败:', {
      hasVideoInstance: !!videoInstance?.value,
      hasVideoEl: !!videoInstance?.value?.$el,
      hasTargetContainer: !!targetContainer,
    })
  }
}

/**
 * 将视频移动到背景层
 */
const moveVideoToBackground = async (streamType: 'monitor' | 'drone_video') => {
  console.log(`开始移动${streamType}视频到背景层`)
  if (backgroundLayer.value) {
    const videoInstance = streamType === 'monitor' ? monitorVideoInstance : droneVideoInstance
    await moveVideoToContainer(videoInstance, backgroundLayer.value)

    // 更新位置状态
    if (streamType === 'monitor') {
      monitorVideoPosition.value = 'background'
      console.log('监控视频位置状态更新为: background')
    } else {
      droneVideoPosition.value = 'background'
      console.log('无人机视频位置状态更新为: background')
    }
  }
}

/**
 * 计算视频在卡片中的位置
 * 注意：这里的逻辑需要与MonitorCards组件的卡片排序逻辑保持一致
 * MonitorCards排序规则：
 * 1. 过滤掉当前背景类型
 * 2. 地图卡片总是排在第一位（如果存在）
 * 3. 其他选项按原始顺序排列：monitor, drone_video
 */
const calculateVideoCardPosition = (streamType: 'monitor' | 'drone_video'): 'card-1' | 'card-2' => {
  const currentBackground = flightBackgroundType.value
  console.log(`计算${streamType}视频位置，当前背景:${currentBackground}`)

  let position: 'card-1' | 'card-2'

  // 模拟MonitorCards的排序逻辑
  const allOptions = ['map', 'monitor', 'drone_video']
  const availableOptions = allOptions.filter((option) => option !== currentBackground)

  // 地图总是排在第一位（如果存在）
  const mapIndex = availableOptions.indexOf('map')
  const hasMap = mapIndex !== -1

  let cardOrder: string[]
  if (hasMap) {
    // 如果有地图，地图在第一位，其他按原始顺序
    const otherOptions = availableOptions.filter((option) => option !== 'map')
    cardOrder = ['map', ...otherOptions]
  } else {
    cardOrder = availableOptions
  }

  console.log(`当前背景: ${currentBackground}, 卡片顺序: [${cardOrder.join(', ')}]`)

  // 找到目标视频类型在卡片中的位置
  const targetIndex = cardOrder.indexOf(streamType)
  if (targetIndex === 0) {
    position = 'card-1'
  } else if (targetIndex === 1) {
    position = 'card-2'
  } else {
    // 如果视频类型不在卡片中（即在背景中），返回默认位置
    position = 'card-1'
    console.warn(`${streamType}视频不在卡片中，可能在背景中`)
  }

  console.log(`${streamType}视频计算位置结果: ${position}`)
  return position
}

/**
 * 将视频移动到卡片位置
 */
const moveVideoToCard = async (streamType: 'monitor' | 'drone_video') => {
  console.log(`开始移动${streamType}视频到卡片位置`)
  if (videoCardContainer.value) {
    const videoInstance = streamType === 'monitor' ? monitorVideoInstance : droneVideoInstance
    const cardPosition = calculateVideoCardPosition(streamType)

    console.log(`计算得出${streamType}视频应该在${cardPosition}位置`)
    await moveVideoToContainer(videoInstance, videoCardContainer.value)

    // 更新位置状态
    if (streamType === 'monitor') {
      monitorVideoPosition.value = cardPosition
      console.log('监控视频位置状态更新为:', cardPosition)
    } else {
      droneVideoPosition.value = cardPosition
      console.log('无人机视频位置状态更新为:', cardPosition)
    }

    console.log(`${streamType}视频移动到${cardPosition}位置完成`)
  }
}

// 监听地图显示位置变化，动态移动地图
watch(
  mapDisplayPosition,
  async (newPosition) => {
    console.log('地图位置变化:', newPosition)

    if (newPosition === 'background') {
      await moveMapToBackground()
    } else if (newPosition.startsWith('card')) {
      await moveMapToCard()
    }
  },
  { immediate: false },
) // 不立即执行，等组件挂载后再执行

// 监听飞控背景类型变化，动态移动视频组件
watch(
  flightBackgroundType,
  async (newType, oldType) => {
    console.log('飞控背景类型变化:', oldType, '->', newType)

    if (viewMode.value === 'flight') {
      console.log('开始重新定位所有视频组件...')

      // 根据新的背景类型，重新定位所有视频组件
      if (newType === 'monitor') {
        // 监控视频背景：monitor→背景，drone_video→卡片
        await moveVideoToBackground('monitor')
        await moveVideoToCard('drone_video')
      } else if (newType === 'drone_video') {
        // 无人机视频背景：drone_video→背景，monitor→卡片
        await moveVideoToBackground('drone_video')
        await moveVideoToCard('monitor')
      } else if (newType === 'map') {
        // 地图背景：所有视频→卡片
        await moveVideoToCard('monitor')
        await moveVideoToCard('drone_video')
      }

      console.log('所有视频组件重新定位完成')
    }
  },
  { immediate: false },
)

// 监听视图模式变化，处理进入/退出飞控模式的状态管理
watch(
  viewMode,
  async (newMode, oldMode) => {
    console.log('视图模式变化:', oldMode, '->', newMode)

    if (newMode === 'flight' && oldMode !== 'flight') {
      // 进入飞控模式
      console.log('进入飞控模式，初始化视频组件位置')
      await nextTick() // 确保DOM更新完成
      await initializeVideoPositions()
    } else if (newMode !== 'flight' && oldMode === 'flight') {
      // 退出飞控模式
      console.log('退出飞控模式，清理视频组件状态')
      resetVideoPositions()
    }
  },
  { immediate: false },
)

/**
 * 重置视频位置状态
 */
const resetVideoPositions = () => {
  console.log('重置视频位置状态')
  monitorVideoPosition.value = 'card-1'
  droneVideoPosition.value = 'card-2'
  console.log(
    '视频位置状态重置完成 - 监控:',
    monitorVideoPosition.value,
    '无人机:',
    droneVideoPosition.value,
  )
}

/**
 * 初始化视频组件位置
 */
const initializeVideoPositions = async () => {
  console.log('初始化视频组件位置，当前背景类型:', flightBackgroundType.value)
  console.log(
    '初始视频位置状态 - 监控:',
    monitorVideoPosition.value,
    '无人机:',
    droneVideoPosition.value,
  )

  if (flightBackgroundType.value === 'monitor') {
    await moveVideoToBackground('monitor')
    await moveVideoToCard('drone_video')
  } else if (flightBackgroundType.value === 'drone_video') {
    await moveVideoToBackground('drone_video')
    await moveVideoToCard('monitor')
  } else if (flightBackgroundType.value === 'map') {
    // 默认地图背景时，两个视频都在卡片中
    await moveVideoToCard('monitor')
    await moveVideoToCard('drone_video')
  }

  console.log(
    '初始化完成后视频位置状态 - 监控:',
    monitorVideoPosition.value,
    '无人机:',
    droneVideoPosition.value,
  )
}

// 监听视频位置状态变化（调试用）
watch(
  [monitorVideoPosition, droneVideoPosition],
  ([newMonitorPos, newDronePos], [oldMonitorPos, oldDronePos]) => {
    console.log('视频位置状态变化:', {
      监控视频: `${oldMonitorPos} -> ${newMonitorPos}`,
      无人机视频: `${oldDronePos} -> ${newDronePos}`,
    })
  },
  { immediate: true },
)

// 组件挂载时获取基础数据和初始化组件位置
onMounted(async () => {
  console.log('组件挂载，当前视图模式:', viewMode.value)

  // 获取无人机列表
  droneStore.fetchDroneList()

  // 初始化地图位置
  await nextTick()
  if (mapDisplayPosition.value === 'background') {
    await moveMapToBackground()
  } else if (mapDisplayPosition.value.startsWith('card')) {
    await moveMapToCard()
  }

  // 初始化视频组件位置（仅在飞控模式下）
  if (viewMode.value === 'flight') {
    await initializeVideoPositions()
  }
})
</script>
<template>
  <div class="dashboard-container">
    <!-- 背景层：背景图片和视频流 -->
    <div ref="backgroundLayer" class="background-layer">
      <!-- 背景图片 -->
      <div class="background-overlay">
        <img src="@/assets/images/mask_border.png" alt="Background" />
      </div>
    </div>

    <!-- UI组件层：所有UI组件都使用绝对定位 -->
    <div class="ui-components-layer">
      <!-- 头部组件 -->
      <div class="dashboard-header">
        <DashHeader />
      </div>

      <!-- 常规面板 - 正常模式显示 -->
      <template v-if="viewMode !== 'flight'">
        <div class="dashboard-left-panel">
          <LeftPanel />
        </div>
        <div class="dashboard-right-panel">
          <RightPanel />
        </div>
        <div class="dashboard-bottom-panel">
          <BottomPanel />
        </div>
      </template>

      <!-- 飞控面板 - 飞控模式显示 -->
      <template v-if="viewMode === 'flight'">
        <div class="flight-top-bar">
          <TopFlightStatusBar />
        </div>
        <div class="flight-left-panel">
          <LeftFlightPanel />
        </div>
        <div class="flight-right-panel">
          <RightFlightPanel />
        </div>
      </template>

      <!-- 监控卡片 - 显示状态控制 -->
      <div v-if="showMonitorCards" class="monitor-cards">
        <MonitorCards />
      </div>

      <!-- 地图卡片容器 - 用于卡片模式时的地图定位 -->
      <div ref="mapCardContainer" class="map-card-container"></div>

      <!-- 视频卡片容器 - 用于卡片模式时的视频定位 -->
      <div ref="videoCardContainer" class="video-card-container"></div>
    </div>

    <!-- 单一地图实例 - 通过JavaScript动态移动到不同容器 -->
    <DashMap
      ref="mapInstance"
      v-show="viewMode !== 'flight' || viewMode === 'flight'"
      :class="['map-component', `map-position-${mapDisplayPosition}`]"
    />

    <!-- 单一视频实例 - 通过JavaScript动态移动到不同容器 -->
    <VideoStreamBackground
      ref="monitorVideoInstance"
      v-if="viewMode === 'flight'"
      stream-type="monitor"
      :class="['video-component', `video-position-${monitorVideoPosition}`]"
    />

    <VideoStreamBackground
      ref="droneVideoInstance"
      v-if="viewMode === 'flight'"
      stream-type="drone_video"
      :class="['video-component', `video-position-${droneVideoPosition}`]"
    />
  </div>
</template>

<style scoped lang="scss">
.dashboard-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  // 背景层 - 保持在底层
  .background-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1; // 始终在底层

    .background-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      pointer-events: none;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        opacity: 0.6;
        mix-blend-mode: overlay;
      }
    }

    .map-component {
      position: absolute;
      transition: all 0.4s ease-in-out;

      // 背景位置 - 全屏显示，低层级
      &.map-position-background {
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2; // 背景模式时在底层
      }
    }

    .video-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2;
    }
  }

  // UI组件层 - 所有组件都使用绝对定位，z-index: 10+
  .ui-components-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    pointer-events: none; // 让点击穿透到地图，除非组件内部设置pointer-events: auto
  }

  // 头部组件
  .dashboard-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: $dashboard-header-height;
    pointer-events: auto;
  }

  // 常规面板
  .dashboard-left-panel {
    position: absolute;
    top: calc($dashboard-header-height + $dashboard-panel-margin); // 头部高度 + 间距
    bottom: $dashboard-panel-margin;
    left: $dashboard-panel-margin;
    width: $dashboard-panel-width;
    pointer-events: auto;
  }

  .dashboard-right-panel {
    position: absolute;
    top: calc($dashboard-header-height + $dashboard-panel-margin); // 头部高度 + 间距
    bottom: $dashboard-panel-margin;
    right: $dashboard-panel-margin;
    width: $dashboard-panel-width;
    pointer-events: auto;
  }

  .dashboard-bottom-panel {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: auto;
  }

  // 飞控面板
  .flight-top-bar {
    position: absolute;
    top: calc($dashboard-header-height + $dashboard-panel-margin + 1.5rem); // 头部高度 + 间距
    left: 50%;
    transform: translateX(-50%);
    pointer-events: auto;
  }

  .flight-left-panel {
    position: absolute;
    top: calc($dashboard-header-height + $dashboard-panel-margin * 2); // 头部  + 间距
    bottom: $dashboard-panel-margin;
    left: $dashboard-panel-margin; // 与常规左侧面板保持一致
    width: $dashboard-panel-width;
    pointer-events: auto;
  }

  .flight-right-panel {
    position: absolute;
    top: calc($dashboard-header-height + $dashboard-panel-margin * 2); // 头部  + 间距
    bottom: $dashboard-panel-margin;
    right: $dashboard-panel-margin; // 与常规右侧面板保持一致
    width: $dashboard-panel-width;
    pointer-events: auto;
  }

  // 监控卡片 - 修复定位问题
  .monitor-cards {
    position: absolute;
    bottom: $dashboard-panel-margin; // 与底部保持间距
    right: calc(
      $dashboard-panel-width + $dashboard-panel-margin * 2
    ); // 右侧面板宽度 + 右侧面板右间距
    pointer-events: auto;
    z-index: 10; // 降低层级，让地图能够覆盖在上面

    // 确保卡片容器不受父容器影响
    width: $monitor-card-width;
    height: auto;
  }

  // 地图卡片容器 - 用于定位卡片模式下的地图
  .map-card-container {
    position: absolute;
    pointer-events: none; // 容器本身不拦截事件
    z-index: 15; // 略高于MonitorCards
  }

  // 视频卡片容器 - 用于定位卡片模式下的视频
  .video-card-container {
    position: absolute;
    pointer-events: none; // 容器本身不拦截事件
    z-index: 15; // 高于MonitorCards，让视频显示在卡片之上
  }

  // 视频组件样式 - 适用于所有位置
  .video-component {
    transition: all 0.4s ease-in-out;
    pointer-events: auto;

    // 背景位置样式
    &.video-position-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2;
    }

    // 卡片位置1样式 - 右下角第一个卡片（上方卡片）
    &.video-position-card-1 {
      position: fixed !important;
      bottom: calc(
        $dashboard-panel-margin + $monitor-card-height + $monitor-card-gap + 1px
      ) !important;
      right: calc($dashboard-panel-width + $dashboard-panel-margin * 2 + 1px) !important;
      width: calc($monitor-card-width - 1px) !important;
      height: calc($monitor-card-height - 2rem - 1px) !important;
      border-radius: 0 0 $border-radius-base $border-radius-base;
      overflow: hidden;
      z-index: 15 !important;

      // 确保视频组件填满整个卡片区域
      :deep(.video-stream-background) {
        width: 100% !important;
        height: 100% !important;
        border-radius: 0 0 $border-radius-base $border-radius-base;
      }
    }

    // 卡片位置2样式 - 右下角第二个卡片（下方卡片）
    &.video-position-card-2 {
      position: fixed !important;
      // card-2是下方卡片，从卡片头部下方开始，所以要加上头部高度
      bottom: calc($dashboard-panel-margin + 1px) !important;
      right: calc($dashboard-panel-width + $dashboard-panel-margin * 2) !important;
      width: $monitor-card-width !important;
      height: calc($monitor-card-height - 2rem - 1px) !important;
      border-radius: 0 0 $border-radius-base $border-radius-base;
      overflow: hidden;
      z-index: 15 !important;

      // 确保视频组件填满整个卡片区域
      :deep(.video-stream-background) {
        width: 100% !important;
        height: 100% !important;
        border-radius: 0 0 $border-radius-base $border-radius-base;
      }
    }
  }

  // 地图组件样式 - 适用于所有位置
  .map-component {
    transition: all 0.3s ease-in-out;
    pointer-events: auto;

    // 背景位置样式
    &.map-position-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2;
    }

    // 卡片位置1样式 - 右下角第一个卡片（上方卡片）
    &.map-position-card-1 {
      position: fixed !important;
      bottom: calc(
        $dashboard-panel-margin + $monitor-card-height + $monitor-card-gap + 1px
      ) !important;
      right: calc($dashboard-panel-width + $dashboard-panel-margin * 2 + 1px) !important;
      width: calc($monitor-card-width - 1px) !important;
      height: calc($monitor-card-height - 2rem - 1px) !important;
      border-radius: 0 0 $border-radius-base $border-radius-base;
      overflow: hidden;
      box-shadow: inset 0 0 1rem rgba($glow-color, 0.3);
      z-index: 15 !important;

      // 确保地图容器填满整个卡片区域
      :deep(#mapContainer) {
        width: 100% !important;
        height: 100% !important;
        border-radius: 0 0 $border-radius-base $border-radius-base;
      }
    }

    // 卡片位置2样式 - 右下角第二个卡片（下方卡片）
    &.map-position-card-2 {
      position: fixed !important;
      // card-2是下方卡片，从卡片头部下方开始，所以要加上头部高度
      bottom: calc($dashboard-panel-margin + 2rem + 1px) !important;
      right: calc($dashboard-panel-width + $dashboard-panel-margin * 2) !important;
      width: $monitor-card-width !important;
      height: calc($monitor-card-height - 2rem - 1px) !important;
      border-radius: 0 0 $border-radius-base $border-radius-base;
      overflow: hidden;
      box-shadow: inset 0 0 1rem rgba($glow-color, 0.3);
      z-index: 15 !important;

      // 确保地图容器填满整个卡片区域
      :deep(#mapContainer) {
        width: 100% !important;
        height: 100% !important;
        border-radius: 0 0 $border-radius-base $border-radius-base;
      }
    }
  }
}
</style>
