/**
 * 无人机基础信息相关类型定义
 */

// 无人机状态枚举
export type DroneStatus = '在线' | '工作中' | '离线' | '异常' | '维护'

// 无人机基础信息类型
export interface DroneBasicInfo {
  id: string
  name: string
  position: {
    longitude: number
    latitude: number
    altitude: number
  }
  locationName: string // 地址名称，如"财政局大楼"
  status: DroneStatus
  // 其他基础参数
  model?: string // 无人机型号
  serialNumber?: string // 序列号
  lastUpdateTime?: string
}

// 无人机列表UI数据
export interface DroneListUIData {
  drones: DroneBasicInfo[]
  selectedDroneId: string | null
  totalCount: number
  onlineCount: number
  workingCount: number
  offlineCount: number
}

// 无人机状态统计
export interface DroneStatsUIData {
  total: number
  online: number
  working: number
  offline: number
  error: number
  maintenance: number
}
