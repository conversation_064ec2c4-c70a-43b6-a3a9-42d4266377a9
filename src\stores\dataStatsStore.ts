/**
 * 数据统计状态管理 Store
 * 管理数据统计相关的状态和数据获取逻辑
 */
import { ref } from 'vue'
import { defineStore } from 'pinia'

// 导入UI数据类型
import type {
  InspectionFrequencyUIData,
  EventCompletionUIData,
  CostReductionUIData,
  DataAchievementUIData,
  DateRange,
} from '@/types/ui'

// 导入API
import { dataStatsApi } from '@/api/dataStats'

export const useDataStatsStore = defineStore('dataStats', () => {
  // ===== 状态定义 =====

  // 当前选择的时间范围
  const currentDateRange = ref<DateRange>({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // 当月第一天
    endDate: new Date(), // 今天
  })

  // 数据加载状态
  const isLoading = ref(false)

  // 错误信息
  const error = ref<string | null>(null)

  // ===== UI数据 =====

  // 巡检频次数据
  const inspectionFrequencyData = ref<InspectionFrequencyUIData | null>(null)

  // 事件办结数据
  const eventCompletionData = ref<EventCompletionUIData | null>(null)

  // 降本增效数据
  const costReductionData = ref<CostReductionUIData | null>(null)

  // 数据成果数据
  const dataAchievementData = ref<DataAchievementUIData | null>(null)

  // ===== Actions =====

  /**
   * 设置时间范围
   */
  const setDateRange = (dateRange: DateRange) => {
    currentDateRange.value = dateRange
    // 触发数据重新获取
    fetchAllData()
  }

  /**
   * 获取所有统计数据
   */
  const fetchAllData = async () => {
    isLoading.value = true
    error.value = null

    try {
      // 调用API获取数据
      const result = await dataStatsApi.getAllDataStats(currentDateRange.value)

      // 设置数据
      inspectionFrequencyData.value = result.inspectionData
      eventCompletionData.value = result.eventData
      costReductionData.value = result.costData
      dataAchievementData.value = result.achievementData

      console.log('数据统计数据获取成功', {
        dateRange: currentDateRange.value,
        timestamp: new Date().toISOString(),
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取数据失败'
      console.error('获取数据统计数据失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 刷新数据
   */
  const refreshData = () => {
    fetchAllData()
  }

  return {
    // 状态
    currentDateRange,
    isLoading,
    error,

    // UI数据
    inspectionFrequencyData,
    eventCompletionData,
    costReductionData,
    dataAchievementData,

    // 方法
    setDateRange,
    fetchAllData,
    refreshData,
  }
})
