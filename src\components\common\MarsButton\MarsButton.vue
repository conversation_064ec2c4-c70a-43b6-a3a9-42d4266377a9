<script setup lang="ts">
interface Props {
  title: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  icon?: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'md',
  disabled: false,
  icon: '',
  loading: false,
})

const emit = defineEmits(['click'])

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}

const getTypeClass = () => {
  return `mars-btn-${props.type}`
}

const getSizeClass = () => {
  return `mars-btn-${props.size}`
}
</script>

<template>
  <button
    class="mars-btn"
    :class="[
      getTypeClass(),
      getSizeClass(),
      { 'mars-btn-disabled': disabled, 'mars-btn-loading': loading },
    ]"
    @click="handleClick"
    :disabled="disabled"
  >
    <span v-if="loading" class="mars-btn-loading-icon"></span>
    <span v-else-if="icon" class="mars-btn-icon">{{ icon }}</span>
    <span class="mars-btn-content">{{ props.title }}</span>
  </button>
</template>

<style scoped lang="scss">
.mars-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 16px;
  min-width: 80px;
  font-weight: 500;
  color: #fff;
  background: linear-gradient(90deg, rgba(0, 102, 204, 0.5) 0%, rgba(0, 153, 204, 0.7) 100%);
  border: 1px solid #00fffe;
  border-radius: 4px;
  box-shadow: 0 0 8px rgba(0, 255, 254, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
  backdrop-filter: blur(4px);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 254, 0.2), transparent);
    transition: all 0.5s;
  }

  &:hover {
    box-shadow: 0 0 15px rgba(0, 255, 254, 0.5);
    // transform: translateY(-2px);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(1px);
    box-shadow: 0 0 5px rgba(0, 255, 254, 0.3);
  }

  .mars-btn-content {
    position: relative;
    z-index: 1;
  }

  .mars-btn-icon {
    margin-right: 8px;
  }

  .mars-btn-loading-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
    border-radius: 50%;
    animation: mars-btn-spin 1s linear infinite;
  }
}

.mars-btn-primary {
  background: linear-gradient(90deg, rgba(0, 102, 204, 0.5) 0%, rgba(0, 153, 204, 0.7) 100%);
  border-color: $primary-color;
}

.mars-btn-success {
  background: linear-gradient(90deg, rgba(0, 128, 0, 0.5) 0%, rgba(0, 170, 0, 0.7) 100%);
  border-color: #00ff00;
}

.mars-btn-warning {
  background: linear-gradient(90deg, rgba(204, 102, 0, 0.5) 0%, rgba(255, 153, 0, 0.7) 100%);
  border-color: #ffa500;
}

.mars-btn-danger {
  background: linear-gradient(90deg, rgba(204, 0, 0, 0.5) 0%, rgba(255, 0, 0, 0.7) 100%);
  border-color: #ff0000;
}

.mars-btn-info {
  background: linear-gradient(90deg, rgba(51, 51, 51, 0.5) 0%, rgba(102, 102, 102, 0.7) 100%);
  // border-color: ;
}

/* 尺寸变体 */
.mars-btn-xs {
  padding: 0.2rem;
  min-width: 0.5rem;
  height: 1rem;
  font-size: 0.5rem;
  border-radius: 2px;
}

.mars-btn-sm {
  padding: 0.2rem 0.2rem;
  min-width: 2rem;
  height: 2rem;
  font-size: 0.7rem;
  border-radius: 2px;
}

.mars-btn-md {
  padding: 6px 16px;
  min-width: 80px;
  height: 36px;
  font-size: 14px;
  border-radius: 4px;
}

.mars-btn-lg {
  padding: 8px 20px;
  min-width: 100px;
  height: 44px;
  font-size: 16px;
  border-radius: 4px;
}

.mars-btn-xl {
  padding: 10px 24px;
  min-width: 120px;
  height: 52px;
  font-size: 18px;
  border-radius: 5px;
}

.mars-btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

@keyframes mars-btn-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
