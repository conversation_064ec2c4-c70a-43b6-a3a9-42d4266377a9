/**
 * WebSocket 组合函数
 * 用于测试后端WebSocket接口连通性
 */
import { ref, onUnmounted, readonly, computed } from 'vue'

// WebSocket连接状态
export type WebSocketStatus = 'disconnected' | 'connecting' | 'connected' | 'error'

export function useWebSocket() {
  // 状态管理
  const ws = ref<WebSocket | null>(null)
  const status = ref<WebSocketStatus>('disconnected')
  const error = ref<string | null>(null)
  const lastMessage = ref<unknown>(null)
  const messageCount = ref(0)

  // 获取WebSocket基础URL
  const getWebSocketUrl = () => {
    const baseUrl = import.meta.env.VITE_WS_BASE_URL
    if (!baseUrl) {
      throw new Error('VITE_WS_BASE_URL 环境变量未配置')
    }
    return baseUrl
  }

  /**
   * 连接WebSocket
   * @param endpoint WebSocket端点（可选），如果不传则直接连接基础地址
   */
  const connect = (endpoint?: string) => {
    try {
      // 构建完整的WebSocket URL
      const baseUrl = getWebSocketUrl()
      const wsUrl = endpoint ? `${baseUrl}${endpoint}` : baseUrl

      console.log('🔌 尝试连接WebSocket:', wsUrl)

      // 更新状态
      status.value = 'connecting'
      error.value = null

      // 创建WebSocket连接
      ws.value = new WebSocket(wsUrl)

      // 连接成功
      ws.value.onopen = (event) => {
        status.value = 'connected'
        console.log('✅ WebSocket连接成功:', event)
        console.log('📡 连接地址:', wsUrl)
        console.log('🕐 连接时间:', new Date().toLocaleString())
      }

      // 接收消息
      ws.value.onmessage = (event) => {
        messageCount.value++

        try {
          // 尝试解析JSON数据
          const data = JSON.parse(event.data)
          lastMessage.value = data

          console.log(`📨 收到WebSocket JSON消息 #${messageCount.value}:`, data)
          console.log('🕐 接收时间:', new Date().toLocaleString())
        } catch {
          // 如果不是JSON，直接显示原始数据
          lastMessage.value = event.data
          console.log(`📨 收到WebSocket原始消息 #${messageCount.value}:`, event.data)
          console.log('🕐 接收时间:', new Date().toLocaleString())
        }
      }

      // 连接错误
      ws.value.onerror = (event) => {
        status.value = 'error'
        error.value = 'WebSocket连接错误'
        console.error('❌ WebSocket连接错误:', event)
        console.error('🔗 错误地址:', wsUrl)
        console.error('🕐 错误时间:', new Date().toLocaleString())
      }

      // 连接关闭
      ws.value.onclose = (event) => {
        status.value = 'disconnected'
        console.log('🔌 WebSocket连接关闭:', {
          code: event.code,
          reason: event.reason,
          wasClean: event.wasClean,
        })
        console.log('🕐 关闭时间:', new Date().toLocaleString())

        // 清理引用
        ws.value = null
      }
    } catch (err) {
      status.value = 'error'
      error.value = err instanceof Error ? err.message : 'WebSocket连接失败'
      console.error('❌ WebSocket连接异常:', err)
    }
  }

  /**
   * 断开WebSocket连接
   */
  const disconnect = () => {
    if (ws.value) {
      console.log('🔌 主动断开WebSocket连接')
      ws.value.close(1000, '用户主动断开')
    }
  }

  /**
   * 发送消息到WebSocket
   * @param message 要发送的消息
   */
  const sendMessage = (message: unknown) => {
    if (ws.value && status.value === 'connected') {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
      ws.value.send(messageStr)
      console.log('📤 发送WebSocket消息:', message)
      console.log('🕐 发送时间:', new Date().toLocaleString())
    } else {
      console.warn('⚠️ WebSocket未连接，无法发送消息:', message)
    }
  }

  /**
   * 获取连接统计信息
   */
  const getStats = () => {
    return {
      status: status.value,
      messageCount: messageCount.value,
      error: error.value,
      isConnected: status.value === 'connected',
      lastMessageTime: lastMessage.value ? new Date().toLocaleString() : null,
    }
  }

  // 组件卸载时自动断开连接
  onUnmounted(() => {
    disconnect()
  })

  return {
    // 状态
    status: readonly(status),
    error: readonly(error),
    lastMessage: readonly(lastMessage),
    messageCount: readonly(messageCount),

    // 方法
    connect,
    disconnect,
    sendMessage,
    getStats,

    // 计算属性
    isConnected: computed(() => status.value === 'connected'),
    isConnecting: computed(() => status.value === 'connecting'),
    hasError: computed(() => status.value === 'error'),
  }
}
