/**
 * 无人机基础数据相关API接口
 */
import type { DroneBasicInfo, DroneListUIData, DroneStatsUIData } from '@/types/ui'

/**
 * 无人机基础数据API
 */
export const droneApi = {
  /**
   * 获取无人机列表
   */
  getDroneList: (): Promise<DroneListUIData> => {
    // 暂时返回模拟数据，后期对接真实API
    return Promise.resolve({
      drones: [
        {
          id: 'drone-001',
          name: '塔石测试机',
          position: {
            longitude: 117.9634,
            latitude: 40.9515,
            altitude: 120
          },
          locationName: '财政局大楼',
          status: '在线',
          model: 'DJI M300 RTK',
          serialNumber: 'SN001234567',
          lastUpdateTime: new Date().toISOString()
        },
        {
          id: 'drone-002',
          name: '新型号无人机',
          position: {
            longitude: 117.9734,
            latitude: 40.9615,
            altitude: 85
          },
          locationName: '市政府大楼',
          status: '工作中',
          model: 'DJI M350 RTK',
          serialNumber: 'SN001234568',
          lastUpdateTime: new Date().toISOString()
        },
        {
          id: 'drone-003',
          name: '备用无人机',
          position: {
            longitude: 117.9534,
            latitude: 40.9415,
            altitude: 0
          },
          locationName: '机场停机坪',
          status: '离线',
          model: 'DJI M300 RTK',
          serialNumber: 'SN001234569',
          lastUpdateTime: new Date().toISOString()
        }
      ],
      selectedDroneId: 'drone-001',
      totalCount: 3,
      onlineCount: 1,
      workingCount: 1,
      offlineCount: 1
    })
  },

  /**
   * 获取无人机详细信息
   * @param droneId 无人机ID
   */
  getDroneDetail: (droneId: string): Promise<DroneBasicInfo> => {
    return Promise.resolve({
      id: droneId,
      name: '塔石测试机',
      position: {
        longitude: 117.9634,
        latitude: 40.9515,
        altitude: 120
      },
      locationName: '财政局大楼',
      status: '在线',
      model: 'DJI M300 RTK',
      serialNumber: 'SN001234567',
      lastUpdateTime: new Date().toISOString()
    })
  },

  /**
   * 获取无人机状态统计
   */
  getDroneStats: (): Promise<DroneStatsUIData> => {
    return Promise.resolve({
      total: 3,
      online: 1,
      working: 1,
      offline: 1,
      error: 0,
      maintenance: 0
    })
  },

  /**
   * 更新无人机位置
   * @param droneId 无人机ID
   * @param position 新位置
   */
  updateDronePosition: (droneId: string, position: DroneBasicInfo['position']): Promise<void> => {
    console.log('更新无人机位置:', droneId, position)
    return Promise.resolve()
  },

  /**
   * 更新无人机状态
   * @param droneId 无人机ID
   * @param status 新状态
   */
  updateDroneStatus: (droneId: string, status: DroneBasicInfo['status']): Promise<void> => {
    console.log('更新无人机状态:', droneId, status)
    return Promise.resolve()
  }
}
