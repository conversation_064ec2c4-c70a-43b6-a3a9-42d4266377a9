# 无人机指挥仪表盘

这是一个基于 Vue 3 + TypeScript + Vite 的现代化前端开发项目，集成了 Element Plus UI 组件库，并配置了 Axios 请求工具、Pinia 状态管理等常用功能，专为无人机监控与指挥系统设计。

## 技术栈

- **核心框架**：Vue 3 (使用组合式 API)
- **构建工具**：Vite
- **类型系统**：TypeScript
- **UI 组件库**：Element Plus
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **HTTP 请求**：Axios
- **地图引擎**：Mars3D
- **数据可视化**：ECharts
- **图标系统**：Iconify
- **代码质量**：ESLint + Prettier + OxLint

## 特性

- ✨ **现代化开发体验**：基于 Vite 的快速开发服务器和构建工具
- 🔄 **API 请求封装**：封装 Axios，提供请求和响应拦截器
- 🌐 **环境配置**：支持开发和生产环境的分离配置
- 🔒 **代理服务器**：解决跨域问题
- 🧩 **组件自动导入**：使用 unplugin-auto-import 和 unplugin-vue-components
- 🎨 **UI 设计**：集成 Element Plus 和自定义主题
- 📦 **状态管理**：使用 Pinia 进行状态管理
- 🗺️ **地图能力**：集成 Mars3D 实现三维地图展示
- 📊 **数据可视化**：使用 ECharts 实现数据图表展示
- 🧪 **代码质量**：ESLint + Prettier + OxLint 保证代码质量

## 目录结构

```
├── public/              # 静态资源
├── src/                 # 源代码
│   ├── api/             # API 接口定义
│   ├── assets/          # 项目资源
│   ├── components/      # 公共组件
│   ├── router/          # 路由配置
│   ├── stores/          # Pinia 状态管理
│   ├── utils/           # 工具函数
│   │   └── request/     # 请求工具
│   ├── views/           # 页面视图
│   ├── App.vue          # 根组件
│   └── main.ts          # 入口文件
├── .env                 # 基础环境变量
├── .env.development     # 开发环境变量
├── .env.production      # 生产环境变量
├── vite.config.ts       # Vite 配置
└── tsconfig.json        # TypeScript 配置
```

## 开发指南

### 推荐的 IDE 设置

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (禁用 Vetur)。

推荐安装以下 VSCode 插件：
- Vue.volar
- dbaeumer.vscode-eslint
- EditorConfig.EditorConfig
- oxc.oxc-vscode
- esbenp.prettier-vscode

### 项目设置

```sh
npm install
```

### 开发环境运行

```sh
npm run dev
```

### 生产环境构建

```sh
npm run build
```

### 代码检查

```sh
npm run lint
```

### 代码格式化

```sh
npm run format
```

## API 请求

### 基本用法

项目已封装 Axios 请求工具，位于 `src/utils/request` 目录下：

```typescript
// 示例 API 调用
import { get, post, put, del } from '@/utils/request'

export const exampleApi = {
  // GET 请求示例
  getExample: (id: number) => {
    return get('/example', { id, status: 'active' })
  },

  // POST 请求示例
  postExample: (data: { name: string; age: number }) => {
    return post('/example', data)
  },
}
```

### 环境配置

- 开发环境：使用 Vite 代理服务器解决跨域问题
- 生产环境：使用实际的 API 服务器地址

## 自定义配置

查看 [Vite 配置参考](https://vitejs.dev/config/) 了解更多配置选项。

## 测试说明

默认未配置测试框架。如需添加测试功能，推荐使用以下工具：

- **单元测试**：[Vitest](https://vitest.dev/) - 与 Vite 完美集成的单元测试框架
- **组件测试**：[@vue/test-utils](https://test-utils.vuejs.org/) - Vue 官方的组件测试工具
- **端到端测试**：[Cypress](https://www.cypress.io/) 或 [Playwright](https://playwright.dev/)

