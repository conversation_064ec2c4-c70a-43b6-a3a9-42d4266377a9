<template>
  <div class="line-chart-container" ref="chartContainerRef">
    <v-chart v-if="chartReady" class="chart" :option="chartOption" autoresize />
  </div>
</template>

<script setup lang="ts">
import VChart, { THEME_KEY } from 'vue-echarts'
import { provide, computed } from 'vue'
import { useEChartsResize } from '@/composables/useChartResize'
import { use } from 'echarts/core'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { ComposeOption } from 'echarts/core'
import type { LineSeriesOption } from 'echarts/charts'
import type {
  TitleComponentOption,
  TooltipComponentOption,
  LegendComponentOption,
  GridComponentOption,
} from 'echarts/components'

// 注册必要的组件
use([TitleComponent, TooltipComponent, LegendComponent, GridComponent, LineChart, Canvas<PERSON>enderer])

// 定义图表选项类型
type EChartsOption = ComposeOption<
  | TitleComponentOption
  | TooltipComponentOption
  | LegendComponentOption
  | GridComponentOption
  | LineSeriesOption
>

// 定义组件属性
interface Props {
  // 图表标题
  title?: string
  // X轴数据
  xData?: string[]
  // Y轴数据
  yData?: number[]
  // 系列名称
  seriesName?: string
  // 是否显示面积
  showArea?: boolean
  // 是否平滑曲线
  smooth?: boolean
  // 线条颜色
  lineColor?: string
  // 图表高度
  height?: string
}

// 组件属性默认值
const props = defineProps<Props>()

// 默认属性值
const defaultProps = {
  title: '',
  xData: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
  yData: [120, 132, 101, 134, 90, 230, 210],
  seriesName: '数据',
  showArea: true,
  smooth: true,
  lineColor: '#00FFFE',
  height: '100%',
}

// 使用图表尺寸监听组合函数
const { chartContainerRef, chartReady } = useEChartsResize('LineChart')

// 计算图表选项
const chartOption = computed<EChartsOption>(() => {
  return {
    backgroundColor: 'transparent',
    title: props.title
      ? {
          text: props.title,
          textStyle: {
            color: '#e1e1e1',
            fontSize: 14,
          },
          left: 'center',
        }
      : undefined,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: props.title ? '15%' : '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: props.xData || defaultProps.xData,
      axisLine: {
        lineStyle: {
          color: '#555',
        },
      },
      axisLabel: {
        color: '#e1e1e1',
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#555',
        },
      },
      axisLabel: {
        color: '#e1e1e1',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(85, 85, 85, 0.3)',
        },
      },
    },
    series: [
      {
        name: props.seriesName || defaultProps.seriesName,
        type: 'line',
        data: props.yData || defaultProps.yData,
        smooth: props.smooth ?? defaultProps.smooth,
        lineStyle: {
          width: 2,
          color: props.lineColor || defaultProps.lineColor,
        },
        itemStyle: {
          color: props.lineColor || defaultProps.lineColor,
        },
        areaStyle: props.showArea
          ? {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: `rgba(${hexToRgb(props.lineColor || defaultProps.lineColor)}, 0.3)`,
                  },
                  {
                    offset: 1,
                    color: `rgba(${hexToRgb(props.lineColor || defaultProps.lineColor)}, 0.05)`,
                  },
                ],
              },
            }
          : undefined,
      },
    ],
  }
})

// 辅助函数：将十六进制颜色转换为RGB
function hexToRgb(hex: string): string {
  // 默认颜色
  if (!hex || hex === '') return '0, 255, 254'

  // 移除#前缀
  hex = hex.replace('#', '')

  // 处理缩写形式
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2]
  }

  // 解析RGB值
  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)

  return `${r}, ${g}, ${b}`
}

// 设置暗色主题
provide(THEME_KEY, 'dark')
</script>

<style scoped>
.line-chart-container {
  width: 100%;
  height: v-bind('props.height || defaultProps.height');
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 100px;
}
</style>
