<script setup lang="ts">
import UIcon from '@/components/common/UIcon/UIcon.vue'
import PanelHeader from './PanelHeader.vue'
interface TabItem {
  id: string
  name: string
}
const props = defineProps<{
  tabs: TabItem[]
  defaultActiveTab?: string
}>()
const emit = defineEmits<{ activeTabChange: [id: string] }>()
const showPanel = ref(true)

const activeTabChange = (id: string) => {
  emit('activeTabChange', id)
}
const close = () => {
  showPanel.value = false
}
</script>

<template>
  <div v-if="showPanel" class="base-pannel">
    <!-- <div class="corner-bl"></div>
    <div class="corner-br"></div> -->
    <div class="panel-header">
      <PanelHeader
        :tabs="props.tabs"
        @activeTabChange="activeTabChange"
        @close="close"
        :defaultActiveTab="defaultActiveTab"
      />
    </div>

    <div class="panel-content">
      <slot name="content" />
    </div>
  </div>
  <div v-else class="expand-button">
    <UIcon name="mdi:menu" @click="showPanel = true" size="1rem" color="#00fffe" />
  </div>
</template>

<style scoped lang="scss">
.base-pannel {
  position: absolute;
  z-index: 10;
  width: 16rem;
  background: $bg-panel;
  color: $text-default;
  border-radius: 0.5rem;
  // padding: 0rem 0.5rem;
  box-shadow: inset 0 0 1rem $glow-color;
  font-size: 0.7rem;
  height: 100%;
  // overflow: auto;
  .panel-header {
    // padding: 0 0.1rem;
    // border-bottom: 1px solid rgba(0, 255, 254, 0.2);
    border-radius: 0.5;
    box-shadow: inset 0 -1rem 1rem -1rem $glow-color;
  }
  .panel-content {
    // padding-top: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.5rem;
    height: calc(100% - 2rem);
    overflow: auto;
    overflow-x: hidden;
    // flex: 1;
  }

  // // 左上角
  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   width: 28px;
  //   height: 28px;
  //   border-top: 1.5px solid #00cfff;
  //   border-left: 1.5px solid #00cfff;
  //   border-top-left-radius: 3px;
  //   pointer-events: none;
  //   z-index: 20;
  // }
  // // 右上角
  // &::after {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   right: 0;
  //   width: 28px;
  //   height: 28px;
  //   border-top: 1.5px solid #00cfff;
  //   border-right: 1.5px solid #00cfff;
  //   border-top-right-radius: 3px;
  //   pointer-events: none;
  //   z-index: 20;
  // }

  // .corner-bl {
  //   position: absolute;
  //   left: 0;
  //   bottom: 0;
  //   width: 28px;
  //   height: 28px;
  //   border-bottom: 1.5px solid #00cfff;
  //   border-left: 1.5px solid #00cfff;
  //   border-bottom-left-radius: 3px;
  //   pointer-events: none;
  //   z-index: 20;
  // }
  // .corner-br {
  //   position: absolute;
  //   right: 0;
  //   bottom: 0;
  //   width: 28px;
  //   height: 28px;
  //   border-bottom: 1.5px solid #00cfff;
  //   border-right: 1.5px solid #00cfff;
  //   border-bottom-right-radius: 3px;
  //   pointer-events: none;
  //   z-index: 20;
  // }
}
.expand-button {
  padding: 0.25rem; // Add some padding to make hover easier
  display: inline-block; // Ensures padding is effective
  cursor: pointer; // Indicate it's clickable

  svg {
    // Target the SVG rendered by UIcon
    display: block; // Good practice for SVG sizing and layout
    transition:
      transform 0.2s ease-in-out,
      filter 0.2s ease-in-out;
  }

  &:hover {
    svg {
      transform: scale(1.15); // Slightly enlarge the icon
      filter: brightness(1.3); // Make the icon noticeably brighter
    }
  }
}
</style>
