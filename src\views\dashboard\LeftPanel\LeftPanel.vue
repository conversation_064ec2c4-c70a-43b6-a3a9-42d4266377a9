<script setup lang="ts">
import BasePannel from '@/components/common/BasePannel/BasePannel.vue'
import { ref } from 'vue'
import DataSats from './DataStats/DataSats.vue'
import MultiScreen from './MultiScreen/MultiScreen.vue'
import FlightSchedule from './FlightSchedule/FlightSchedule.vue'
interface TabItem {
  id: string
  name: string
  title: string
  content: string
}

const tabs: TabItem[] = [
  {
    id: 'dataStats',
    name: '数据统计',
    title: '数据统计',
    content: '无人机数据统计信息将显示在这里',
  },
  {
    id: 'multiScreen',
    name: '多屏监控',
    title: '多屏监控',
    content: '无人机多屏监控信息将显示在这里',
  },
  {
    id: 'flightSchedule',
    name: '航线排期',
    title: '航线排期',
    content: '无人机航线排期信息将显示在这里',
  },
]

const activeTab = ref<string>(tabs[0].id)

const switchTab = (tabId: string) => {
  activeTab.value = tabId
}
</script>

<template>
  <BasePannel class="left-panel" :tabs="tabs" @activeTabChange="switchTab">
    <template #content>
      <div v-for="tab in tabs" :key="tab.id" v-show="activeTab === tab.id" class="tab-content">
        <MultiScreen v-if="activeTab === 'multiScreen'" />
        <DataSats v-else-if="activeTab === 'dataStats'" />
        <FlightSchedule v-else-if="activeTab === 'flightSchedule'" />
      </div>
    </template>
  </BasePannel>
</template>

<style scoped lang="scss">
.left-panel {
  width: 100%;
  height: 100%;

  .tab-content {
    height: 100%;
  }
}
</style>
