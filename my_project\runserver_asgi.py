#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
import os
import sys
from daphne.cli import CommandLineInterface

if __name__ == "__main__":
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "my_project.settings")

    # 默认参数配置
    default_args = [
        'daphne',
        'my_project.asgi:application',
        '--port', '8000',
        '--bind', '0.0.0.0',
        '--verbosity', '3',
        '--access-log', '-',
        # '--force-color',  # 强制彩色输出
        '--proxy-headers',  # 代理头支持
    ]
    
    # 如果没有提供参数，使用默认参数
    if len(sys.argv) == 1:
        sys.argv = default_args
        print("=" * 50)
        print("🚀 启动ASGI服务器...")
        print(f"📡 HTTP/API服务: http://localhost:8000")
        print(f"🔌 WebSocket服务: ws://localhost:8000/ws/msg")
        print(f"📚 API文档: http://localhost:8000/swagger/")
        print("=" * 50)
    elif len(sys.argv) < 2 or not sys.argv[1].endswith('.asgi:application'):
        sys.argv.insert(1, 'my_project.asgi:application')

    # 使用 Daphne 启动 ASGI 应用
    CommandLineInterface.entrypoint()