import logging
import time
import datetime
from django.db import connection
from django.views.decorators.csrf import csrf_exempt
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from vgis_log.logTools import Lo<PERSON><PERSON>elper
from vgis_utils.vgis_http.httpTools import HttpHelper
from my_app.manage.businessManager import BuisnessOperator
from my_app.manage.commonManager import CommonOperator
from my_app.manage.QdictManager import QdictManager
from my_app.models import SysLog
from my_app.views.response.baseRespone import Result

from my_app.utils.commonUtility import CommonHelper

from my_project.token import ExpiringTokenAuthentication

logger = logging.getLogger('django')



# 上传文件视图集
class QdictViewSet(viewsets.ModelViewSet):

    # 权限类，要求用户必须经过身份验证
    permission_classes = (IsAuthenticated,)
    # token认证
    # authentication_classes = (TokenAuthentication,)
    # 自定义token认证
    authentication_classes = (ExpiringTokenAuthentication,)

    @action(detail=False, methods=['POST'])
    def get_select_dict(self, request):
        api_path = request.path
        function_title = "查询属性信息"
        try:
            qdictManager = QdictManager(connection)
            start = LoggerHelper.set_start_log_info(logger)

            static_value = qdictManager.get_select_dict(request)
            # 调用 LoggerHelper 类的方法记录操作正常结束的日志信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))