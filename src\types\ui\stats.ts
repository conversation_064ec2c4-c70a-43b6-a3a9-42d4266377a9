/**
 * 数据统计模块UI数据模型
 */

import type { DataDisplayItem } from './common'

// 巡检频次组件数据类型
export interface InspectionFrequencyUIData {
  timesData: {
    dailyAverage: number
    totalCount: number
    chartData: Array<{ name: string; value: number }>
  }
  mileageData: {
    dailyAverage: number
    totalCount: number
    chartData: Array<{ name: string; value: number }>
  }
  durationData: {
    dailyAverage: number
    totalCount: number
    chartData: Array<{ name: string; value: number }>
  }
}

// 事件办结组件数据类型
export interface EventCompletionUIData {
  typeData: Array<{
    name: string
    acceptCount: number
    cooperateCount: number
    percentage: number
    color?: string
  }>
  locationData: Array<{
    name: string
    acceptCount: number
    cooperateCount: number
    percentage: number
    color?: string
  }>
}

// 降本增效组件数据类型
export interface CostReductionUIData {
  items: Array<{
    value: number
    unit: string
    label: string
  }>
}

// 数据成果组件数据类型
export interface DataAchievementUIData {
  topRowData: DataDisplayItem[]
  bottomRowData: DataDisplayItem[]
}
