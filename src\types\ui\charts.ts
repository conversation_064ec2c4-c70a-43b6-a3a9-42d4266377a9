/**
 * 图表组件UI数据模型
 * 这里放置各种图表组件需要的数据类型
 */

// 基础图表数据点类型
export interface ChartDataPoint {
  name: string
  value: number
  color?: string
}

// BarChart组件数据类型
export interface BarChartData {
  title: string
  data: ChartDataPoint[]
  barColor?: string
  showLabel?: boolean
  height?: string
  maxValue?: number
  showXAxisLabel?: boolean
  showYAxis?: boolean
  barWidth?: number
}

// LineChart组件数据类型
export interface LineChartData {
  title: string
  data: ChartDataPoint[]
  lineColor?: string
  showLabel?: boolean
  height?: string
  smooth?: boolean
}

// PieChart组件数据类型
export interface PieChartData {
  title: string
  data: ChartDataPoint[]
  showLegend?: boolean
  height?: string
  radius?: number
}

// GaugeChart组件数据类型
export interface GaugeChartData {
  title: string
  value: number
  max?: number
  unit?: string
  color?: string
  height?: string
}

// 多系列图表数据类型
export interface MultiSeriesChartData {
  title: string
  categories: string[]
  series: Array<{
    name: string
    data: number[]
    color?: string
  }>
  height?: string
}
