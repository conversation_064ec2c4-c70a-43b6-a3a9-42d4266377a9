#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
# @Time    :  2022/6/10 15:08
# <AUTHOR> chenxw
# @Email   : <EMAIL>
# @File    : alogirthmUtility.py
# @Descr   : 数据库操作帮助类
# @Software: PyCharm
from my_project import settings
import psycopg2


class DatabaseHelper:

    def __init__(self):
        pass

    # @staticmethod
    # def get_ksh_database_conection(logger):
    #     HOST = settings.DATABASES["ksh"]["HOST"]
    #     PORT = settings.DATABASES["ksh"]["PORT"]
    #     USER = settings.DATABASES["ksh"]["USER"]
    #     PASSWORD = settings.DATABASES["ksh"]["PASSWORD"]
    #     DATABASE = settings.DATABASES["ksh"]["NAME"]
    #     try:
    #         conn = psycopg2.connect(database=DATABASE, user=USER, password=PASSWORD,
    #                                 host=HOST, port=PORT)
    #         sql = "select count(*) from pg_tables;"
    #         cursor = conn.cursor()
    #         cursor.execute(sql)
    #         records = cursor.fetchall()
    #         logger.info("建立可视化数据连接成功")
    #     except Exception as exp:
    #         conn = None
    #         logger.error("建立可视化数据库连接失败，可能原因：{}".format(str(exp)))
    #     # 需要判断是否连接可视化数据库成功
    #     return conn
    #
    # @staticmethod
    # def close_ksh_database_conection(conn, logger):
    #     try:
    #         logger.info("关闭可视化数据库连接")
    #         conn.close()
    #     except Exception as exp:
    #         logger.error("关闭可视化数据库连接失败，可能原因：{}".format(str(exp)))

    @staticmethod
    def get_string_field_of_table(tablename, conn):
        cursor = conn.cursor()
        # 执行SQL查询
        query = f"SELECT * FROM {tablename}"
        cursor.execute(query)

        # 获取列名和数据类型
        columns = [desc[0] for desc in cursor.description]
        column_types = [desc[1] for desc in cursor.description]

        # 打印列名和数据类型
        # for col, col_type in zip(columns, column_types):
        #     print(f"Column: {col}, Type: {col_type}")

        # CHAR:"<class 'dmPython.FIXED_STRING'>", VARCHAR："<class 'dmPython.STRING'>"
         # 获取字符串类型字段
        string_columns = [col for col, col_type in zip(columns, column_types) if "STRING" in str(col_type)]

        # 打印字符串类型字段
        # print("String columns:", string_columns)
        return string_columns


    @staticmethod
    def get_non_geom_field_of_table(tablename, conn):
        cursor = conn.cursor()
        # 执行SQL查询
        query = f"SELECT * FROM {tablename}"
        cursor.execute(query)

        # 获取列名和数据类型
        columns = [desc[0] for desc in cursor.description]
        column_types = [desc[1] for desc in cursor.description]

        # 打印列名和数据类型
        # for col, col_type in zip(columns, column_types):
        #     print(f"Column: {col}, Type: {col_type}")

        # CHAR:"<class 'dmPython.FIXED_STRING'>", VARCHAR："<class 'dmPython.STRING'>"
        # GEOM:OBJECTVAR
         # 获取字符串类型字段
        string_columns = [col for col, col_type in zip(columns, column_types) if "OBJECTVAR"  not in str(col_type)]

        # 打印字符串类型字段
        # print("String columns:", string_columns)
        return string_columns