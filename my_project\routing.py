from django.core.asgi import get_asgi_application  # 添加这行导入

from channels.routing import ProtocolTypeRouter, URLRouter
from django.urls import re_path
from my_app.consumers import DroneDataConsumer

application = ProtocolTypeRouter({
    # HTTP 协议处理由 Django 原生处理
    "http": get_asgi_application(),

    # WebSocket 协议处理
    "websocket": URLRouter([
        re_path(r'ws/drone/(?P<drone_id>\w+)/$', DroneDataConsumer.as_asgi()),
    ]),
})
