<script setup lang="ts">
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { GaugeChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import type { ComposeOption } from 'echarts/core'
import type { GaugeSeriesOption } from 'echarts/charts'
import { useChartResize } from '@/composables/useChartResize'

// 注册必要的组件
use([GaugeChart, CanvasRenderer])

// 定义图表选项类型
type EChartsOption = ComposeOption<GaugeSeriesOption>

// 定义组件属性
interface Props {
  // 仪表盘标题
  title?: string
  // 当前值
  value?: number
  // 最小值
  min?: number
  // 最大值
  max?: number
  // 单位
  unit?: string
  // 仪表盘颜色
  color?: string
  // 仪表盘大小
  size?: string
  // 是否显示指针
  showPointer?: boolean
  // 是否显示刻度
  showTick?: boolean
  // 仪表盘类型：'speed' | 'direction' | 'altitude' | 'battery'
  type?: 'speed' | 'direction' | 'altitude' | 'battery'
}

// 组件属性默认值
const props = withDefaults(defineProps<Props>(), {
  title: '仪表盘',
  value: 0,
  min: 0,
  max: 100,
  unit: '',
  color: '#00FFFE',
  size: '8rem',
  showPointer: true,
  showTick: true,
  type: 'speed',
})

// 使用图表尺寸监听组合函数，设置更合理的最小尺寸
const { chartContainerRef, chartReady, containerSize } = useChartResize('GaugeChart', 60, 60)

// 根据类型获取不同的配置
const getGaugeConfig = () => {
  switch (props.type) {
    case 'direction':
      return {
        startAngle: 90,
        endAngle: -270,
        min: 0,
        max: 360,
        splitNumber: 4, // 只显示4个方向
        axisLabel: {
          formatter: (value: number) => {
            // 只显示四个主要方向
            if (value === 0 || value === 360) return '北'
            if (value === 90) return '东'
            if (value === 180) return '南'
            if (value === 270) return '西'
            return '' // 其他角度不显示文字
          },
        },
      }
    case 'speed':
      return {
        startAngle: 225,
        endAngle: -45,
        min: props.min,
        max: props.max,
        splitNumber: 5,
        axisLabel: {
          formatter: '{value}',
        },
      }
    case 'altitude':
      return {
        startAngle: 225,
        endAngle: -45,
        min: props.min,
        max: props.max,
        splitNumber: 4,
        axisLabel: {
          formatter: '{value}',
        },
      }
    default:
      return {
        startAngle: 225,
        endAngle: -45,
        min: props.min,
        max: props.max,
        splitNumber: 5,
        axisLabel: {
          formatter: '{value}',
        },
      }
  }
}

// 计算图表选项
const chartOption = computed<EChartsOption>(() => {
  // 统一使用相同的配置
  const config = getGaugeConfig()
  const is360Degree = props.max === 360 // 判断是否为360度仪表盘

  return {
    series: [
      {
        type: 'gauge',
        startAngle: config.startAngle,
        endAngle: config.endAngle,
        min: config.min,
        max: config.max,
        splitNumber: config.splitNumber,
        radius: '70%',
        center: ['50%', '50%'],
        data: [
          {
            value: props.value,
            name: props.title,
          },
        ],
        // 仪表盘轴线
        axisLine: {
          lineStyle: {
            width: 9,
            color: is360Degree
              ? [
                  // 360度仪表盘四个颜色区域（北东南西）
                  [0.25, '#67e0e3'], // 北 - 青色
                  [0.5, '#37a2da'], // 东 - 蓝色
                  [0.75, '#fd666d'], // 南 - 红色
                  [1, '#ffb74d'], // 西 - 橙色
                ]
              : [
                  // 普通仪表盘三个颜色区域
                  [0.3, props.color],
                  [0.7, '#FFD93D'],
                  [1, '#FF6B6B'],
                ],
          },
        },
        // 指针
        pointer: {
          show: props.showPointer,
          itemStyle: {
            color: props.color,
          },
          width: 2,
          length: '60%',
        },
        // 刻度
        axisTick: {
          show: props.showTick,
          distance: 0, // 刻度线向内
          length: 2,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.6)',
            width: 1,
          },
        },
        // 刻度标签
        axisLabel: {
          show: props.showTick,
          distance: props.type === 'speed' ? -20 : -20, // 速度类型更靠近仪表盘
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 8,
          formatter: config.axisLabel.formatter,
        },
        // 分割线
        splitLine: {
          show: props.showTick && props.type !== 'direction', // 方向类型不显示分割线
          distance: 0, // 分割线向内
          length: 6,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.4)',
            width: 1,
          },
        },
        // 标题 - 根据仪表盘类型调整位置
        title: {
          show: true,
          offsetCenter: is360Degree ? [0, '0%'] : [0, '80%'], // 360度仪表盘标题在中心，其他在下方
          fontSize: is360Degree ? 10 : 11, // 360度仪表盘字体稍小
          color: 'rgba(255, 255, 255, 0.8)',
        },
        // 数值显示 - 根据仪表盘类型调整位置
        detail: {
          show: true,
          offsetCenter: is360Degree ? [0, '35%'] : [0, '50%'], // 360度仪表盘数值更靠下
          fontSize: is360Degree ? 11 : 12, // 360度仪表盘字体稍小
          fontWeight: 'bold',
          color: is360Degree ? '#ffffff' : props.color, // 360度仪表盘使用白色
          formatter: `{value}${props.unit}`,
        },
      },
    ],
  }
})
</script>

<template>
  <div ref="chartContainerRef" class="gauge-chart" :style="{ width: size, height: size }">
    <VChart
      v-if="chartReady && containerSize.width > 0 && containerSize.height > 0"
      :option="chartOption"
      :autoresize="true"
      class="chart"
    />
    <div v-else class="chart-loading">
      <div class="loading-placeholder">
        <UIcon name="mdi:loading" size="1.5rem" color="#00FFFE" class="loading-icon" />
        <span class="loading-text">{{ title }}</span>
        <div class="debug-info" v-if="containerSize.width > 0 || containerSize.height > 0">
          <small
            >{{ Math.round(containerSize.width) }}×{{ Math.round(containerSize.height) }}</small
          >
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.gauge-chart {
  display: flex;

  align-items: center;
  justify-content: center;
  min-width: 60px;
  min-height: 60px;
  position: relative;
  width: 100%;
  height: 100%;

  .chart {
    width: 100%;
    height: 100%;
    min-width: 60px;
    min-height: 60px;
    flex-shrink: 0;
  }

  .chart-loading {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .loading-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      color: $text-secondary;

      .loading-icon {
        animation: spin 1s linear infinite;
      }

      .loading-text {
        font-size: $font-size-panel-caption;
        color: $text-secondary;
      }

      .debug-info {
        font-size: $font-size-panel-micro;
        color: $text-secondary;
        opacity: 0.6;
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
